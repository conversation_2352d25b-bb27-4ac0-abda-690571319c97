// // import dotenv from 'dotenv';
// // import { fileURLToPath } from 'url';
// // import path from 'path';
// // import { createClient } from '@supabase/supabase-js';
// // import { ServerClient } from 'postmark';
// // import crypto from 'crypto';

// // // Configuration setup
// // const __filename = fileURLToPath(import.meta.url);
// // const __dirname = path.dirname(__filename);
// // dotenv.config({path: path.resolve(__dirname, '.env')});

// // // Initialize Supabase client
// // const supabase = createClient(
// //   process.env.SUPABASE_URL,
// //   process.env.SUPABASE_API,
// // );

// // // Initialize Postmark client
// // const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
// // const senderMail = process.env.COLLECTION_SENDER_MAIL;
// // const weeklyReportMailTemplateId = process.env.WEEKLY_REPORT_TEMPLATE_ID;
// // const contactEmail = '<EMAIL>';

// // // Function to format date to DD-MM-YYYY
// // const formatDate = (dateString) => {
// //   const date = new Date(dateString);
// //   return date.toLocaleDateString('en-GB', {
// //     day: '2-digit',
// //     month: '2-digit',
// //     year: 'numeric'
// //   }).replace(/\//g, '-');
// // };

// // // Get start and end dates for this week and last week
// // const getWeekDates = () => {
// //   const now = new Date();
  
// //   // This week
// //   const thisWeekEnd = new Date(now);
// //   const thisWeekStart = new Date(now);
// //   thisWeekStart.setDate(thisWeekStart.getDate() - 7);
  
// //   // Last week
// //   const lastWeekEnd = new Date(thisWeekStart);
// //   lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
// //   const lastWeekStart = new Date(lastWeekEnd);
// //   lastWeekStart.setDate(lastWeekStart.getDate() - 6);
  
// //   return {
// //     thisWeekStart: thisWeekStart.toISOString(),
// //     thisWeekEnd: thisWeekEnd.toISOString(),
// //     lastWeekStart: lastWeekStart.toISOString(),
// //     lastWeekEnd: lastWeekEnd.toISOString()
// //   };
// // };

// // // Generate comparison data between this week and last week
// // const generateComparisonData = (thisWeekCount, lastWeekCount) => {
// //   const diff = thisWeekCount - lastWeekCount;
// //   const percentChange = lastWeekCount === 0 
// //     ? (thisWeekCount > 0 ? 100 : 0) 
// //     : Math.round((diff / lastWeekCount) * 100);
  
// //   let arrow, trendClass, comparison;
  
// //   if (diff > 0) {
// //     arrow = "↑";
// //     trendClass = "up";
// //     comparison = `${Math.abs(percentChange)}% increase (${diff} more)`;
// //   } else if (diff < 0) {
// //     arrow = "↓";
// //     trendClass = "down";
// //     comparison = `${Math.abs(percentChange)}% decrease (${Math.abs(diff)} fewer)`;
// //   } else {
// //     arrow = "→";
// //     trendClass = "neutral";
// //     comparison = "No change";
// //   }
  
// //   return { arrow, trendClass, comparison };
// // };

// // // The main function to send weekly reports to all stores
// // export const sendWeeklyReportsToAllStores = async () => {
// //   console.log("Starting to send weekly reports to all stores");
  
// //   try {
// //     // Get all stores
// //     const { data: storeData, error: storeDataError } = await supabase
// //       .from('stores')
// //       .select('*');
      
// //     if (storeDataError) {
// //       console.error("Error fetching stores:", storeDataError.message);
// //       return {
// //         status: 400,
// //         message: "Error fetching stores",
// //         data: null
// //       };
// //     }
    
// //     const results = [];
    
// //     // Process each store
// //     for (const store of storeData) {
// //       const storeId = store.id || store.store_id;
// //       const storeName = store.store_name || store.name;
// //       const storeEmail = store.store_email;
      
// //       if (!storeId || !storeEmail) {
// //         console.error(`Missing store ID or email for ${storeName}`);
// //         results.push({
// //           store: storeName,
// //           status: 'failed',
// //           message: 'Missing store ID or email'
// //         });
// //         continue;
// //       }
      
// //       console.log(`Processing weekly report for store: ${storeName}`);
      
// //       // Get date ranges for this week and last week
// //       const { thisWeekStart, thisWeekEnd, lastWeekStart, lastWeekEnd } = getWeekDates();
      
// //       // Get collection data for this week
// //       const { data: thisWeekCollections, error: thisWeekError } = await supabase
// //         .from('collections')
// //         .select('*')
// //         .eq('store_id', storeId)
// //         .gte('created_at', thisWeekStart)
// //         .lte('created_at', thisWeekEnd);
      
// //       if (thisWeekError) {
// //         console.error(`Error fetching this week's collection data for ${storeName}:`, thisWeekError.message);
// //         results.push({
// //           store: storeName,
// //           status: 'failed',
// //           message: 'Error fetching this week collections'
// //         });
// //         continue;
// //       }
      
// //       // Get collection data for last week
// //       const { data: lastWeekCollections, error: lastWeekError } = await supabase
// //         .from('collections')
// //         .select('*')
// //         .eq('store_id', storeId)
// //         .gte('created_at', lastWeekStart)
// //         .lte('created_at', lastWeekEnd);
      
// //       if (lastWeekError) {
// //         console.error(`Error fetching last week's collection data for ${storeName}:`, lastWeekError.message);
// //         results.push({
// //           store: storeName,
// //           status: 'failed',
// //           message: 'Error fetching last week collections'
// //         });
// //         continue;
// //       }
      
// //       // Process collections by status for this week
// //       const thisWeekGenerated = thisWeekCollections.filter(c => c.status === 'generated');
// //       const thisWeekScheduled = thisWeekCollections.filter(c => c.status === 'scheduled');
// //       const thisWeekPublished = thisWeekCollections.filter(c => c.status === 'published');
      
// //       // Process collections by status for last week
// //       const lastWeekGenerated = lastWeekCollections.filter(c => c.status === 'generated');
// //       const lastWeekScheduled = lastWeekCollections.filter(c => c.status === 'scheduled');
// //       const lastWeekPublished = lastWeekCollections.filter(c => c.status === 'published');
      
// //       // Generate comparison data
// //       const generatedComparison = generateComparisonData(
// //         thisWeekGenerated.length, 
// //         lastWeekGenerated.length
// //       );
      
// //       const scheduledComparison = generateComparisonData(
// //         thisWeekScheduled.length, 
// //         lastWeekScheduled.length
// //       );
      
// //       const publishedComparison = generateComparisonData(
// //         thisWeekPublished.length, 
// //         lastWeekPublished.length
// //       );
      
// //       // Format collections for email template
// //       const generated_collections = thisWeekGenerated.map((collection, index) => ({
// //         sno: index + 1,
// //         collection_name: collection.collection_name,
// //         date_generated: formatDate(collection.created_at)
// //       }));
      
// //       const scheduled_collections = thisWeekScheduled.map((collection, index) => ({
// //         sno: index + 1,
// //         collection_name: collection.collection_name,
// //         scheduled_date: formatDate(collection.scheduled_date || collection.created_at)
// //       }));
      
// //       const published_collections = thisWeekPublished.map((collection, index) => ({
// //         sno: index + 1,
// //         collection_name: collection.collection_name,
// //         published_date: formatDate(collection.published_date || collection.created_at),
// //         storefront_url: `${store.store_url}/collections${collection.collection_url}`
// //       }));
      
// //       // Prepare email template data
// //       const templateData = {
// //         store_name: storeName,
// //         generated_collections,
// //         scheduled_collections,
// //         published_collections,
// //         generated_arrow: generatedComparison.arrow,
// //         generated_trend_class: generatedComparison.trendClass,
// //         generated_comparison: generatedComparison.comparison,
// //         scheduled_arrow: scheduledComparison.arrow,
// //         scheduled_trend_class: scheduledComparison.trendClass,
// //         scheduled_comparison: scheduledComparison.comparison,
// //         published_arrow: publishedComparison.arrow,
// //         published_trend_class: publishedComparison.trendClass,
// //         published_comparison: publishedComparison.comparison
// //       };
      
// //       try {
// //         // Send email via Postmark
// //         const response = await client.sendEmailWithTemplate({
// //           From: senderMail,
// //           To: '<EMAIL>',
// //           TemplateId: weeklyReportMailTemplateId,
// //           TemplateModel: templateData
// //         });
        
// //         console.log(`Weekly report email sent successfully to ${storeName}`);
// //         results.push({
// //           store: storeName,
// //           status: 'success',
// //           message: 'Weekly report email sent successfully'
// //         });
// //       } catch (emailError) {
// //         console.error(`Error sending email to ${storeName}:`, emailError.message);
// //         results.push({
// //           store: storeName,
// //           status: 'failed',
// //           message: `Error sending email: ${emailError.message}`
// //         });
// //       }
// //     }
    
// //     return {
// //       status: 200,
// //       message: "Weekly reports process completed",
// //       data: results
// //     };
    
// //   } catch (error) {
// //     console.error("Global error in weekly report processing:", error.message);
// //     return {
// //       status: 500,
// //       message: "Something went wrong during weekly report processing",
// //       data: error.message
// //     };
// //   }
// // };

// // // Execute the function if called directly
// // if (process.argv[1] === fileURLToPath(import.meta.url)) {
// //   sendWeeklyReportsToAllStores().then(result => {
// //     console.log("Weekly reports execution result:", result);
// //     process.exit(0);
// //   }).catch(error => {
// //     console.error("Error executing weekly reports:", error);
// //     process.exit(1);
// //   });
// // }

// import dotenv from 'dotenv';
// import { fileURLToPath } from 'url';
// import path from 'path';
// import { createClient } from '@supabase/supabase-js';
// import { ServerClient } from 'postmark';
// import crypto from 'crypto';

// // Configuration setup
// const __filename = fileURLToPath(import.meta.url);
// const __dirname = path.dirname(__filename);
// dotenv.config({path: path.resolve(__dirname, '.env')});

// // Initialize Supabase client
// const supabase = createClient(
//   process.env.SUPABASE_URL,
//   process.env.SUPABASE_API,
// );

// // Initialize Postmark client
// const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
// const senderMail = process.env.COLLECTION_SENDER_MAIL;
// const weeklyReportMailTemplateId = process.env.WEEKLY_REPORT_TEMPLATE_ID;
// const contactEmail = '<EMAIL>';

// const slackWebhookURL = process.env.SLACK_WEBHOOK_URL;
// const slackWebhookAccessKey = process.env.SLACK_WEBHOOK_ACCESS_KEY;
// const slackWebhookEndpoint = `${slackWebhookURL}${slackWebhookAccessKey}`;

// const slackMessage = "Weekly Report Mail sent Process Completed for 'Rank Collections App' ";

// // Function to format date to DD-MM-YYYY
// const formatDate = (dateString) => {
//   const date = new Date(dateString);
//   return date.toLocaleDateString('en-GB', {
//     day: '2-digit',
//     month: '2-digit',
//     year: 'numeric'
//   }).replace(/\//g, '-');
// };

// // Get start and end dates for this week and last week
// const getWeekDates = () => {
//   const now = new Date();
  
//   // This week
//   const thisWeekEnd = new Date(now);
//   const thisWeekStart = new Date(now);
//   thisWeekStart.setDate(thisWeekStart.getDate() - 7);
  
//   // Last week
//   const lastWeekEnd = new Date(thisWeekStart);
//   lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
//   const lastWeekStart = new Date(lastWeekEnd);
//   lastWeekStart.setDate(lastWeekStart.getDate() - 6);
  
//   return {
//     thisWeekStart: thisWeekStart.toISOString(),
//     thisWeekEnd: thisWeekEnd.toISOString(),
//     lastWeekStart: lastWeekStart.toISOString(),
//     lastWeekEnd: lastWeekEnd.toISOString()
//   };
// };

// // Generate comparison data between this week and last week
// const generateComparisonData = (thisWeekCount, lastWeekCount) => {
//   const diff = thisWeekCount - lastWeekCount;
//   const percentChange = lastWeekCount === 0 
//     ? (thisWeekCount > 0 ? 100 : 0) 
//     : Math.round((diff / lastWeekCount) * 100);
  
//   let arrow, trendClass, comparison;
  
//   if (diff > 0) {
//     arrow = "↑";
//     trendClass = "up";
//     comparison = `${Math.abs(percentChange)}% increase (${diff} more)`;
//   } else if (diff < 0) {
//     arrow = "↓";
//     trendClass = "down";
//     comparison = `${Math.abs(percentChange)}% decrease (${Math.abs(diff)} fewer)`;
//   } else {
//     arrow = "→";
//     trendClass = "neutral";
//     comparison = "No change";
//   }
  
//   return { arrow, trendClass, comparison };
// };

// // The main function to send weekly reports to all stores
// export const sendWeeklyReportsToAllStores = async () => {
//   console.log("Starting to send weekly reports to all stores");
  
//   try {
//     // Get all stores
//     const { data: storeData, error: storeDataError } = await supabase
//       .from('stores')
//       .select('*');
      
//     if (storeDataError) {
//       console.error("Error fetching stores:", storeDataError.message);
//       return {
//         status: 400,
//         message: "Error fetching stores",
//         data: null
//       };
//     }
    
//     const results = [];
//     const { thisWeekStart, thisWeekEnd, lastWeekStart, lastWeekEnd } = getWeekDates();
    
//     // Process each store
//     for (const store of storeData) {
//       const storeId = store.id || store.store_id;
//       const storeName = store.store_name || store.name;
//       const storeEmail = store.store_email;
      
//       if (!storeId || !storeEmail) {
//         console.error(`Missing store ID or email for ${storeName}`);
//         results.push({
//           store: storeName,
//           status: 'failed',
//           message: 'Missing store ID or email'
//         });
//         continue;
//       }
      
//       console.log(`Processing weekly report for store: ${storeName}`);
      
//       // Get ALL collections for this store (we'll filter in memory)
//       const { data: allCollections, error: collectionError } = await supabase
//         .from('collections')
//         .select('*')
//         .eq('store_id', storeId);
      
//       if (collectionError) {
//         console.error(`Error fetching collection data for ${storeName}:`, collectionError.message);
//         results.push({
//           store: storeName,
//           status: 'failed',
//           message: 'Error fetching collections'
//         });
//         continue;
//       }
      
//       // Function to check if a date is within this week
//       const isInThisWeek = (dateStr) => {
//         if (!dateStr) return false;
//         const date = new Date(dateStr);
//         return date >= new Date(thisWeekStart) && date <= new Date(thisWeekEnd);
//       };
      
//       // Function to check if a date is within last week
//       const isInLastWeek = (dateStr) => {
//         if (!dateStr) return false;
//         const date = new Date(dateStr);
//         return date >= new Date(lastWeekStart) && date <= new Date(lastWeekEnd);
//       };
      
//       // Calculate collections in different statuses for this week
//       const thisWeekGeneratedCollections = allCollections.filter(collection => 
//         isInThisWeek(collection.created_at)
//       );
      
//       const thisWeekScheduledCollections = allCollections.filter(collection => 
//         (collection.status === 'scheduled' || collection.status === 'published') && 
//         isInThisWeek(collection.scheduled_date || collection.updated_at)
//       );
      
//       const thisWeekPublishedCollections = allCollections.filter(collection => 
//         collection.status === 'published' && 
//         isInThisWeek(collection.published_date || collection.updated_at)
//       );
      
//       // Calculate collections in different statuses for last week
//       const lastWeekGeneratedCollections = allCollections.filter(collection => 
//         isInLastWeek(collection.created_at)
//       );
      
//       const lastWeekScheduledCollections = allCollections.filter(collection => 
//         (collection.status === 'scheduled' || collection.status === 'published') && 
//         isInLastWeek(collection.scheduled_date || collection.updated_at)
//       );
      
//       const lastWeekPublishedCollections = allCollections.filter(collection => 
//         collection.status === 'published' && 
//         isInLastWeek(collection.published_date || collection.updated_at)
//       );
      
//       // Generate comparison data
//       const generatedComparison = generateComparisonData(
//         thisWeekGeneratedCollections.length, 
//         lastWeekGeneratedCollections.length
//       );
      
//       const scheduledComparison = generateComparisonData(
//         thisWeekScheduledCollections.length, 
//         lastWeekScheduledCollections.length
//       );
      
//       const publishedComparison = generateComparisonData(
//         thisWeekPublishedCollections.length, 
//         lastWeekPublishedCollections.length
//       );
      
//       // Format collections for email template
//       const generated_collections = thisWeekGeneratedCollections.map((collection, index) => ({
//         sno: index + 1,
//         collection_name: collection.collection_name,
//         date_generated: formatDate(collection.created_at)
//       }));
      
//       const scheduled_collections = thisWeekScheduledCollections.map((collection, index) => ({
//         sno: index + 1,
//         published_type: collection.published_type !== 'manual',
//         collection_name: collection.collection_name,
//         scheduled_date: formatDate(collection.scheduled_time || collection.updated_at || collection.created_at)
//       }));
      
//       const published_collections = thisWeekPublishedCollections.map((collection, index) => ({
//         sno: index + 1,
//         collection_name: collection.collection_name,
//         published_date: formatDate(collection.published_time || collection.updated_at || collection.created_at),
//         storefront_url: `${store.store_url}/collections${collection.collection_url}`
//       }));
      
//       // Prepare email template data
//       const templateData = {
//         store_name: storeName,
//         generated_collections,
//         scheduled_collections,
//         published_collections,
//         generated_arrow: generatedComparison.arrow,
//         generated_trend_class: generatedComparison.trendClass,
//         generated_comparison: generatedComparison.comparison,
//         scheduled_arrow: scheduledComparison.arrow,
//         scheduled_trend_class: scheduledComparison.trendClass,
//         scheduled_comparison: scheduledComparison.comparison,
//         published_arrow: publishedComparison.arrow,
//         published_trend_class: publishedComparison.trendClass,
//         published_comparison: publishedComparison.comparison
//       };
      
//       try {
//         // Send email via Postmark
//         const response = await client.sendEmailWithTemplate({
//           From: senderMail,
//           To: '<EMAIL>',
//           TemplateId: weeklyReportMailTemplateId,
//           TemplateModel: templateData
//         });
        
//         console.log(`Weekly report email sent successfully to ${storeName}`);
//         results.push({
//           store: storeName,
//           status: 'success',
//           message: 'Weekly report email sent successfully',
//           stats: {
//             generated: thisWeekGeneratedCollections.length,
//             scheduled: thisWeekScheduledCollections.length,
//             published: thisWeekPublishedCollections.length
//           }
//         });
//       } catch (emailError) {
//         console.error(`Error sending email to ${storeName}:`, emailError.message);
//         results.push({
//           store: storeName,
//           status: 'failed',
//           message: `Error sending email: ${emailError.message}`
//         });
//       }
//     }
    
//     return {
//       status: 200,
//       message: "Weekly reports process completed",
//       data: results
//     };
    
//   } catch (error) {
//     console.error("Global error in weekly report processing:", error.message);
//     return {
//       status: 500,
//       message: "Something went wrong during weekly report processing",
//       data: error.message
//     };
//   }
// };

// // Execute the function if called directly
// if (process.argv[1] === fileURLToPath(import.meta.url)) {
//   sendWeeklyReportsToAllStores().then(result => {
//       const slackAlert = fetch(slackWebhookEndpoint, {
//           method:'POST',
//           headers: {
//               'Content-Type': 'application/json'
//             },
//             body: JSON.stringify({
//                 text: slackMessage
//             })
//         })
//         if(!slackAlert.ok) {
//             console.error("Error sending Slack alert:", slackAlert.statusText);
//     }
//     console.log("Slack alert sent successfully");
//     console.log("Weekly report mail sent process completed");
//     console.log("Weekly reports execution result:", result);
//     process.exit(0);
//   }).catch(error => {
//     console.error("Error executing weekly reports:", error);
//     process.exit(1);
//   });
// }

import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import { ServerClient } from 'postmark';


// Configuration setup
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Initialize Supabase client
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_API
);

// Initialize Postmark client
const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
const senderMail = process.env.COLLECTION_SENDER_MAIL;
const weeklyReportMailTemplateId = process.env.WEEKLY_REPORT_TEMPLATE_ID;
const contactEmail = '<EMAIL>';

const slackWebhookURL = process.env.SLACK_WEBHOOK_URL;
const slackWebhookAccessKey = process.env.SLACK_WEBHOOK_ACCESS_KEY;
const slackWebhookEndpoint = `${slackWebhookURL}${slackWebhookAccessKey}`;
const slackMessage = "Weekly Report Mail sent Process Completed for 'Rank Collections App' ";

// Function to send Slack alert
const sendSlackAlert = async (message) => {
  try {
    const response = await fetch(slackWebhookEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: message,
      }),
    });

    if (!response.ok) {
      throw new Error(`Slack API responded with status: ${response.status} - ${response.statusText}`);
    }

    console.log('Slack alert sent successfully');
  } catch (error) {
    console.error('Error sending Slack alert:', error.message);
    throw error; // Re-throw to handle in the calling function
  }
};

// Function to format date to DD-MM-YYYY
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date
    .toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    })
    .replace(/\//g, '-');
};

// Get start and end dates for this week and last week
const getWeekDates = () => {
  const now = new Date();

  // This week
  const thisWeekEnd = new Date(now);
  const thisWeekStart = new Date(now);
  thisWeekStart.setDate(thisWeekStart.getDate() - 7);

  // Last week
  const lastWeekEnd = new Date(thisWeekStart);
  lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
  const lastWeekStart = new Date(lastWeekEnd);
  lastWeekStart.setDate(lastWeekStart.getDate() - 6);

  return {
    thisWeekStart: thisWeekStart.toISOString(),
    thisWeekEnd: thisWeekEnd.toISOString(),
    lastWeekStart: lastWeekStart.toISOString(),
    lastWeekEnd: lastWeekEnd.toISOString(),
  };
};

// Generate comparison data between this week and last week
const generateComparisonData = (thisWeekCount, lastWeekCount) => {
  const diff = thisWeekCount - lastWeekCount;
  const percentChange =
    lastWeekCount === 0
      ? thisWeekCount > 0
        ? 100
        : 0
      : Math.round((diff / lastWeekCount) * 100);

  let arrow, trendClass, comparison;

  if (diff > 0) {
    arrow = '↑';
    trendClass = 'up';
    comparison = `${Math.abs(percentChange)}% increase (${diff} more)`;
  } else if (diff < 0) {
    arrow = '↓';
    trendClass = 'down';
    comparison = `${Math.abs(percentChange)}% decrease (${Math.abs(diff)} fewer)`;
  } else {
    arrow = '→';
    trendClass = 'neutral';
    comparison = 'No change';
  }

  return { arrow, trendClass, comparison };
};

// The main function to send weekly reports to all stores
export const sendWeeklyReportsToAllStores = async () => {
  console.log('Starting to send weekly reports to all stores');

  try {
    // Get all stores
    const { data: storeData, error: storeDataError } = await supabase
      .from('stores')
      .select('*');

    if (storeDataError) {
      console.error('Error fetching stores:', storeDataError.message);
      return {
        status: 400,
        message: 'Error fetching stores',
        data: null,
      };
    }

    const results = [];
    const { thisWeekStart, thisWeekEnd, lastWeekStart, lastWeekEnd } = getWeekDates();

    // Process each store
    for (const store of storeData) {
      const storeId = store.id || store.store_id;
      const storeName = store.store_name || store.name;
      const storeEmail = store.store_email;

      if (!storeId || !storeEmail) {
        console.error(`Missing store ID or email for ${storeName}`);
        results.push({
          store: storeName,
          status: 'failed',
          message: 'Missing store ID or email',
        });
        continue;
      }

      console.log(`Processing weekly report for store: ${storeName}`);

      // Get ALL collections for this store (we'll filter in memory)
      const { data: allCollections, error: collectionError } = await supabase
        .from('collections')
        .select('*')
        .eq('store_id', storeId);

      if (collectionError) {
        console.error(`Error fetching collection data for ${storeName}:`, collectionError.message);
        results.push({
          store: storeName,
          status: 'failed',
          message: 'Error fetching collections',
        });
        continue;
      }

      // Function to check if a date is within this week
      const isInThisWeek = (dateStr) => {
        if (!dateStr) return false;
        const date = new Date(dateStr);
        return date >= new Date(thisWeekStart) && date <= new Date(thisWeekEnd);
      };

      // Function to check if a date is within last week
      const isInLastWeek = (dateStr) => {
        if (!dateStr) return false;
        const date = new Date(dateStr);
        return date >= new Date(lastWeekStart) && date <= new Date(lastWeekEnd);
      };

      // Calculate collections in different statuses for this week
      const thisWeekGeneratedCollections = allCollections.filter((collection) =>
        isInThisWeek(collection.created_at)
      );

      const thisWeekScheduledCollections = allCollections.filter(
        (collection) =>
          (collection.status === 'scheduled' || collection.status === 'published') &&
          isInThisWeek(collection.scheduled_date || collection.updated_at)
      );

      const thisWeekPublishedCollections = allCollections.filter(
        (collection) =>
          collection.status === 'published' &&
          isInThisWeek(collection.published_date || collection.updated_at)
      );

      // Calculate collections in different statuses for last week
      const lastWeekGeneratedCollections = allCollections.filter((collection) =>
        isInLastWeek(collection.created_at)
      );

      const lastWeekScheduledCollections = allCollections.filter(
        (collection) =>
          (collection.status === 'scheduled' || collection.status === 'published') &&
          isInLastWeek(collection.scheduled_date || collection.updated_at)
      );

      const lastWeekPublishedCollections = allCollections.filter(
        (collection) =>
          collection.status === 'published' &&
          isInLastWeek(collection.published_date || collection.updated_at)
      );

      // Generate comparison data
      const generatedComparison = generateComparisonData(
        thisWeekGeneratedCollections.length,
        lastWeekGeneratedCollections.length
      );

      const scheduledComparison = generateComparisonData(
        thisWeekScheduledCollections.length,
        lastWeekScheduledCollections.length
      );

      const publishedComparison = generateComparisonData(
        thisWeekPublishedCollections.length,
        lastWeekPublishedCollections.length
      );

      // Format collections for email template
      const generated_collections = thisWeekGeneratedCollections.map((collection, index) => ({
        sno: index + 1,
        collection_name: collection.collection_name,
        date_generated: formatDate(collection.created_at),
      }));

      const scheduled_collections = thisWeekScheduledCollections.map((collection, index) => ({
        sno: index + 1,
        published_type: collection.published_type !== 'manual',
        collection_name: collection.collection_name,
        scheduled_date: formatDate(
          collection.scheduled_time || collection.updated_at || collection.created_at
        ),
      }));

      const published_collections = thisWeekPublishedCollections.map((collection, index) => ({
        sno: index + 1,
        collection_name: collection.collection_name,
        published_date: formatDate(
          collection.published_time || collection.updated_at || collection.created_at
        ),
        storefront_url: `${store.store_url}/collections${collection.collection_url}`,
      }));

      // Prepare email template data
      const templateData = {
        store_name: storeName,
        generated_collections,
        scheduled_collections,
        published_collections,
        generated_arrow: generatedComparison.arrow,
        generated_trend_class: generatedComparison.trendClass,
        generated_comparison: generatedComparison.comparison,
        scheduled_arrow: scheduledComparison.arrow,
        scheduled_trend_class: scheduledComparison.trendClass,
        scheduled_comparison: scheduledComparison.comparison,
        published_arrow: publishedComparison.arrow,
        published_trend_class: publishedComparison.trendClass,
        published_comparison: publishedComparison.comparison,
      };

      try {
        // Send email via Postmark
        const response = await client.sendEmailWithTemplate({
          From: senderMail,
          To: senderMail, // Use storeEmail instead of hardcoded email
          TemplateId: weeklyReportMailTemplateId,
          TemplateModel: templateData,
        });

        console.log(`Weekly report email sent successfully to ${storeName}`);
        results.push({
          store: storeName,
          status: 'success',
          message: 'Weekly report email sent successfully',
          stats: {
            generated: thisWeekGeneratedCollections.length,
            scheduled: thisWeekScheduledCollections.length,
            published: thisWeekPublishedCollections.length,
          },
        });
      } catch (emailError) {
        console.error(`Error sending email to ${storeName}:`, emailError.message);
        results.push({
          store: storeName,
          status: 'failed',
          message: `Error sending email: ${emailError.message}`,
        });
      }
    }

    // After all stores are processed, send Slack alert
    try {
      await sendSlackAlert(slackMessage);
    } catch (slackError) {
      console.error('Failed to send Slack alert, but emails were processed:', slackError.message);
      // Don't fail the entire process if Slack alert fails
    }

    return {
      status: 200,
      message: 'Weekly reports process completed',
      data: results,
    };
  } catch (error) {
    console.error('Global error in weekly report processing:', error.message);
    return {
      status: 500,
      message: 'Something went wrong during weekly report processing',
      data: error.message,
    };
  }
};

// Execute the function if called directly
if (process.argv[1] === fileURLToPath(import.meta.url)) {
  sendWeeklyReportsToAllStores()
    .then((result) => {
      console.log('Weekly reports execution result:', result);
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error executing weekly reports:', error);
      process.exit(1);
    });
}