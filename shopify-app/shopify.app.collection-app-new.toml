# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "71c96bf803bfc6bd114a8095e4baed8f"
name = "collection-app"
handle = "collection-app-4"
application_url = "https://bought-computation-highlight-bandwidth.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "binary-dev-store.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_product_listings,read_products,read_publications,read_script_tags,write_content,write_online_store_pages,write_product_listings,write_products,write_publications,write_script_tags"

[auth]
redirect_urls = [
  "https://bought-computation-highlight-bandwidth.trycloudflare.com/auth/callback",
  "https://bought-computation-highlight-bandwidth.trycloudflare.com/auth/shopify/callback",
  "https://bought-computation-highlight-bandwidth.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-07"

[pos]
embedded = false
