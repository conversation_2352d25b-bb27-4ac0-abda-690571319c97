import dotenv from "dotenv";
import { fileURLToPath } from "url";
import path from "path";
import { createClient } from "@supabase/supabase-js";
import { GraphQLClient, gql } from "graphql-request";
import crypto from "crypto";
import { ServerClient } from "postmark";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, ".env") });

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_API,
);

const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
const senderMail = process.env.COLLECTION_SENDER_MAIL;
const contactLink = `https://www.chakril.com/contact/`;
const contactEmail = `<EMAIL>`;

const removeGid = (gid) => {
  const match = gid.match(/(\d+)$/);
  return match ? match[0] : null;
};


// Email notification function
async function sendPublishedCollectionEmail(storeEmail, emailData) {
    try {
        const response = await client.sendEmailWithTemplate({
            From: senderMail,
            To: storeEmail,
            TemplateId: process.env.PUBLISHED_COLLECTION_ID,
            TemplateModel: emailData,
            Tag: "PublishedCollectionNotification",
            TrackOpens: true,
            TrackLinks: "HtmlAndText"
        });
        console.log(`✅ Published notification email sent successfully to ${storeEmail}`);
        return response;
    } catch (error) {
        console.error(`❌ Error sending published notification email to ${storeEmail}:`, error.message);
        throw error;
    }
}

const decode = (encryptedKey, encryptedIV, encryptedToken) => {
  const decipher = crypto.createDecipheriv(
    "aes-256-cbc",
    Buffer.from(encryptedKey, "hex"),
    Buffer.from(encryptedIV, "hex"),
  );
  let accessToken = decipher.update(encryptedToken, "hex", "utf8");
  accessToken += decipher.final("utf8");
  return accessToken;
};

const calculateNextPublishedTime = (currentTime, frequency) => {
  const nextTime = new Date(currentTime);

  switch (frequency) {
    case "weekly":
      nextTime.setDate(nextTime.getDate() + 7);
      break;
    case "every_three_days":
      nextTime.setDate(nextTime.getDate() + 3);
      break;
    case "every_two_weeks":
      nextTime.setDate(nextTime.getDate() + 14);
      break;
    case "monthly":
      nextTime.setDate(nextTime.getDate() + 30);
      break;
    default:
      nextTime.setDate(nextTime.getDate() + 1);
  }

  return nextTime;
};

const publishedCollections = async () => {
  const { data: storeData, error: storeDataError } = await supabase
    .from("stores")
    .select("*");
  if (storeDataError) {
    console.error("The store Data Not found", storeDataError.message);
    return;
  }

  const { data: preferencesData, error: preferencesError } = await supabase
    .from("preferences")
    .select("*");
  if (preferencesError) {
    console.error("The preferences data not found or something went wrong");
    return;
  }

  const currentTime = new Date();
  for (const schedule of preferencesData) {
    if (!schedule.scheduled_status) continue;

    const store = storeData.find((s) => s.id === schedule.store_id);
    if (store) {
      const publishedTime = new Date(schedule.next_published_time);
      console.log("The published time is: ", publishedTime);
      if (currentTime >= publishedTime) {
        console.log(
          `✅ Condition met! publish the collection to the shopify for ${store.store_name}`,
        );
        try {
          const encryptedToken = store.access_token;
          const encryptedKey = store.encryption_key;
          const encryptedIV = store.iv;
          if (!encryptedToken || !encryptedKey || !encryptedIV) {
            console.error("Missing encrypted Data", store.storeName);
          }

          const accessToken = decode(encryptedKey, encryptedIV, encryptedToken);
          const { data: collectionData, error: collectionDataError } =
            await supabase
              .from("collections")
              .select("*")
              .eq("store_id", store.id)
              .eq("status", "scheduled")
              .eq("generated_type", "auto");
          if (collectionDataError) {
            console.error(
              `Something went wrong or collections data not found for ${store.store_name}`,
              collectionDataError.message,
            );
          }

          const publishedCollections = [];

          const graphQLClient = new GraphQLClient(
            `https://${store.store_name}.myshopify.com/admin/api/2024-07/graphql.json`,
            {
              headers: {
                "X-Shopify-Access-Token": accessToken,
              },
            },
          );
          for (const collection of collectionData) {
            const {
              collection_name,
              collection_url,
              product_details,
              description,
            } = collection;

            // Create the collection
            const createCollectionResponse = await graphQLClient.request(
              gql`
                mutation collectionCreate($input: CollectionInput!) {
                  collectionCreate(input: $input) {
                    userErrors {
                      field
                      message
                    }
                    collection {
                      id
                      title
                      handle
                      descriptionHtml
                    }
                  }
                }
              `,
              {
                input: {
                  title: collection_name,
                  handle: collection_url,
                  descriptionHtml: description,
                },
              },
            );

            const collectionResponse = createCollectionResponse;
            const userErrors = collectionResponse?.collectionCreate?.userErrors;
            const collectionId =
              collectionResponse?.collectionCreate?.collection?.id;
            const collectionHandle =
              collectionResponse?.collectionCreate?.collection?.handle;

            if (userErrors && userErrors.length > 0) {
              console.error(
                `Something went wrong while creating the collection for ${store.store_name}:`,
                userErrors,
              );
              continue;
            }

            const productIds = product_details.map((product) => {
              const id = product.id.toString();
              return id.startsWith("gid://shopify/Product/")
                ? id
                : `gid://shopify/Product/${id}`;
            });

            // Add products to the collection
            const addProductsResponse = await graphQLClient.request(
              gql`
                mutation collectionAddProducts($id: ID!, $productIds: [ID!]!) {
                  collectionAddProducts(id: $id, productIds: $productIds) {
                    collection {
                      id
                      handle
                      title
                      products(first: 250) {
                        nodes {
                          id
                          title
                          handle
                        }
                      }
                    }
                    userErrors {
                      field
                      message
                    }
                  }
                }
              `,
              {
                id: collectionId,
                productIds: productIds,
              },
            );

            const productErrors =
              addProductsResponse?.collectionAddProducts?.userErrors;
            if (productErrors && productErrors.length > 0) {
              console.error(
                `Something went wrong while adding products to the collection for ${store.store_name}:`,
                productErrors,
              );
              continue;
            }

            const publications = await graphQLClient.request(gql`
              {
                publications(first: 100) {
                  edges {
                    node {
                      id
                      name
                    }
                  }
                }
              }
            `);

            const onlineStorePublication = publications.publications.edges.find(
              (edge) => edge.node.name === "Online Store",
            );

            if (!onlineStorePublication) {
              console.error(
                `Online Store Publication not found for ${store.store_name}`,
              );
              continue;
            }

            const onlineStoreId = onlineStorePublication.node.id;
            const currentTime = new Date();
            const isoFormattedTime = currentTime.toISOString();

            // Publish the collection
            const publishMutation = await graphQLClient.request(
              gql`
                        mutation publishablePublish($id: ID!, $input: [PublicationInput!]!) {
                            publishablePublish(id: $id, input: $input) {
                                publishable {
                                    ... on Collection {
                                        id
                                        title
                                        handle
                                        publishedOnPublication(publicationId: "${onlineStoreId}")
                                    }
                                }
                            }
                        }
                    `,
              {
                id: collectionId,
                input: [
                  {
                    publicationId: onlineStoreId,
                    publishDate: isoFormattedTime,
                  },
                ],
              },
            );

            const publishErrors =
              publishMutation?.publishablePublish?.userErrors;
            if (publishErrors && publishErrors.length > 0) {
              console.error(
                `Something went wrong while publishing the collection for ${store.store_name}:`,
                publishErrors,
              );
              continue;
            }

            // Add collection to published collections array with its URL
            publishedCollections.push({
              name: collection_name,
              url: `https://${store.store_url}/collections/${collectionHandle}`
            });

            // Update collection status and next publish time
            const { error: updateCollectionError } = await supabase
              .from("collections")
              .update({
                collection_id: removeGid(collectionId),
                status: "published",
                published_type: "auto",
                published_time: new Date(),
              })
              .eq("store_id", store.id)
              .eq("id", collection.id);

            if (updateCollectionError) {
              console.error(
                `Failed to update collection status in database for ${store.store_name}:`,
                updateCollectionError,
              );
              continue;
            }
          }
          const emailStatus = preferencesData[0].email_enabled
          // Send email notification if collections were published
          if (emailStatus && publishedCollections.length > 0) {
            const emailData = {
              store_name: store.store_name,
              collections: publishedCollections,
              ctaUrl: `https://admin.shopify.com/store/${store.store_name}/apps/collection-app-stage/app/collection_drafts`,
              supportUrl: contactEmail,
              supportLinkText: contactEmail
            };

            try {
              await sendPublishedCollectionEmail(store.store_email, emailData);
            } catch (emailError) {
              console.error(`❌ Failed to send published collection email to ${store.store_name}:`, emailError.message);
            }
          }

          const nextPublishedTime = calculateNextPublishedTime(
            schedule.next_published_time,
            schedule.scheduled_frequency,
          );
          const { error: updateError } = await supabase
            .from("preferences")
            .update({ next_published_time: nextPublishedTime })
            .eq("s_no", schedule.s_no)
            .eq("store_id", store.id);

          if (updateError) {
            console.error(
              `❌ Failed to update schedule time for ${store.store_name}:`,
              updateError,
            );
          } else {
            console.log(
              `✅ Updated generated time for ${store.store_name} to ${nextPublishedTime.toISOString()} (UTC)`,
            );
          }
        } catch (error) {
          console.error(
            `Something went wrong to publish the collection to shopify for ${store.store_name}`,
            error,
          );
        }
      } else {
        console.log(
          `⏳ Waiting... The collection published for ${store.store_name} is still in future.`,
        );
      }
    }
  }
};

publishedCollections();
