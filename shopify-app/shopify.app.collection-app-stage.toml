# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "b7534921a9117726ce09f641866a105b"
name = "collection-app-stage"
handle = "collection-app-stage"
application_url = "https://stage.rankcollection.chakril.site/"
embedded = true

[build]
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders, read_product_listings,read_products,read_publications,read_script_tags,unauthenticated_read_product_pickup_locations,write_product_listings,write_products,write_publications,write_script_tags"

[auth]
redirect_urls = [
  "https://stage.rankcollection.chakril.site/auth/callback",
  "https://stage.rankcollection.chakril.site/auth/shopify/callback",
  "https://stage.rankcollection.chakril.site/api/auth/callback"
]

[webhooks]
api_version = "2024-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "orders/create", "products/create", "products/delete", "products/update" ]
  compliance_topics = [ "customers/data_request", "customers/redact", "shop/redact" ]
  uri = "https://stage.rankcollection.chakril.site/webhooks"

[pos]
embedded = false
