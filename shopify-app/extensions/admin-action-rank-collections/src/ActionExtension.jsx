import { useEffect, useState } from 'react';
import {
  reactExtension,
  useApi,
  AdminAction,
  BlockStack,
  Button,
  Text,
  Banner,
} from '@shopify/ui-extensions-react/admin';

const TARGET = 'admin.collection-index.action.render';

export default reactExtension(TARGET, () => <App />);

function App() {
  const { query, close } = useApi(TARGET);
  const [shopName, setShopName] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    const fetchShopName = async () => {
      try {
        const shopResponse = await query(`
          query {
            shop {
              name
            }
          }
        `);
        const name = shopResponse?.data?.shop?.name;
        setShopName(name || '');
      } catch (err) {
        console.error('Failed to fetch shop name:', err);
      }
    };

    fetchShopName();
  }, [query]);

  const handleCallExtensionRoute = async () => {
    let data;
    try {
      setLoading(true);
      setMessage('');
      setIsError(false);

      const endPoint = `https://create.rankcollection.com/api/extension_route`;

      const response = await fetch(endPoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ shopName }),
      });

      console.dir(response, {depth:null})

      if (!response.ok) {
        throw new Error('Network response was not ok.');
      }

      data = await response.json();
      setMessage(data.message);
    } catch (err) {
      console.error('Failed to call extension route:', err);
      setMessage(data.message);
      setIsError(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminAction
      title="Generate Custom Collection"
      primaryAction={
        <Button onPress={handleCallExtensionRoute} disabled={loading} loading={loading}>
          Generate
        </Button>
      }
      secondaryAction={<Button onPress={close}>Cancel</Button>}
    >
      <BlockStack spacing="loose">
        <Text fontVariant='headingLg' alignment="center" as="h5"  size="large" fontWeight="bold">
          Click "Generate" to start creating a custom collection. Once it's processed, we’ll send you an email notification.
        </Text>
        {message && (
          <Banner tone={isError ? 'critical' : 'success'}>{message}</Banner>
        )}
      </BlockStack>
    </AdminAction>
  );
}


