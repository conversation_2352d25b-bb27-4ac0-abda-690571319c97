{% schema %}
    {
      "name": "Collection Schema",
      "target": "section",
      "enabled_on": {
        "templates": ["collection"]
      },
      "settings": []
    }
    {% endschema %}
    
    {% if collection %}
      <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "{{ collection.title | escape }}",
        "description": "{{ collection.description | escape }}",
        "url": "{{ shop.url }}{{ collection.url }}",
        {% if collection.image %}
        "image": "{{ collection.image | img_url: 'master' | prepend: shop.url }}",
        {% endif %}
        "mainEntity": {
          "@type": "ItemList",
          "name": "Products in {{ collection.title | escape }}",
          "itemListElement": [
            {% for product in collection.products limit: 5 %}
              {
                "@type": "Product",
                "name": "{{ product.title | escape }}",
                "image": "{{ product.featured_image | img_url: 'large' | prepend: shop.url }}",
                "url": "{{ shop.url }}{{ product.url }}"
              }{% unless forloop.last %},{% endunless %}
            {% endfor %}
          ]
        }
      }
      </script>
    {% endif %}
    