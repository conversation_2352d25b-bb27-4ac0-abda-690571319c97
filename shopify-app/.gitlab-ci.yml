include:
  - local: 'gitlab-kibana-ci.yml'

stages:
  - filebeat_setup
  - deploy

deploy:
  stage: deploy
  before_script:
    - apt-get update && apt-get install -y sshpass
  script:
    - echo "Deploy started"
    - echo "Checking tag ${CI_COMMIT_TAG:-not set}"
    # Check tag and determine the target server, directory, and PM2 task
    - |
      if [[ "$CI_COMMIT_TAG" == stage-v* ]]; then
        TARGET_IP="**************"
        DEPLOY_DIR="/srv/rank-collections/shopify-app"
        PM2_TASK="rank-collections-stage"
      elif [[ "$CI_COMMIT_TAG" == rc-v* ]]; then
        TARGET_IP="*************"
        DEPLOY_DIR="rank-collections/shopify-app"
        PM2_TASK="rank-collection-prod"
      else
        echo "Unknown or unset tag: ${CI_COMMIT_TAG:-not set}. Exiting."
        exit 1
      fi

    - echo "Deploying to $TARGET_IP"
    - echo "Using directory $DEPLOY_DIR"
    - echo "Restarting PM2 task $PM2_TASK"

    # Execute deployment on the target server
    - |
      /usr/bin/sshpass -p B1nch4@*2024 ssh -o StrictHostKeyChecking=no bincha-admin@$TARGET_IP "
        hostname &&
        pwd &&
        sudo su &&
        cd $DEPLOY_DIR &&
        pwd &&
        git config --global --add safe.directory $DEPLOY_DIR &&
        git stash &&
        git pull &&
        source ~/.bashrc &&
        source ~/.nvm/nvm.sh &&
        nvm use 20 &&
        node --version &&
        npm install --force &&
        npm run build &&
        pm2 restart $PM2_TASK &&
        pm2 list
      "

  environment:
    name: azure-shopify-runner-rank-collections
  rules:
    - if: '$CI_COMMIT_TAG =~ /^stage-v.*/ || $CI_COMMIT_TAG =~ /^rc-v.*/'