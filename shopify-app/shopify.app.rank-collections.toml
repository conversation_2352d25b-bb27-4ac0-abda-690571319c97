# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "6d40c0162bb3db26b1f831e557654cce"
name = "Rank Collections"
handle = "rank-collections"
application_url = "https://create.rankcollection.com"
embedded = true

[build]
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders,read_product_listings,read_products,read_publications,read_script_tags,write_product_listings,write_products,write_publications,write_script_tags"

[auth]
redirect_urls = [
  "https://create.rankcollection.com/auth/callback",
  "https://create.rankcollection.com/auth/shopify/callback",
  "https://create.rankcollection.com/api/auth/callback"
]

[webhooks]
api_version = "2024-07"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled", "products/create", "products/delete", "products/update", "orders/create" ]
  uri = "https://create.rankcollection.com/webhooks"
  compliance_topics = [ "customers/data_request", "customers/redact", "shop/redact" ]

[pos]
embedded = false
