import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import csvParser from "csv-parser";
import { Readable } from "stream";
import { v4 as uuidv4 } from "uuid";
import moment from "moment";
import { requiredHeader } from "../configs/config";
import { loggerInfo, loggerError } from "../helper/loggerHelper";

export const importCSVFile = async (admin, file) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(`Collection Import via csv function is initialized`,storeName,  file)
  
  const formattedTime =
    moment.utc().format("YYYY-MM-DD HH:mm:ss.SSSSSS") + "+00";

  // Define the required headers
  

  try {
    // Fetch store data
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName)
      .single(); // Assuming 'store_name' is unique

    if (storeDataError) {
      loggerError(
        'Something went wrong.',storeName, storeDataError.message,
      );
      return {
        status: 400,
        message: "Failed to retrieve store information.",
        data: null,
      };
    }

    // Read file content
    let fileContent;
    if (typeof file.text === "function") {
      fileContent = await file.text();
    } else if (file.arrayBuffer) {
      const arrayBuffer = await file.arrayBuffer();
      fileContent = new TextDecoder().decode(arrayBuffer);
    } else {
      throw new Error("Unable to read file content");
    }

    // Parse the CSV content and check headers
    const records = await new Promise((resolve, reject) => {
      const results = [];
      const parser = csvParser();

      // Check the headers
      parser.on("headers", (headers) => {
        const isValid = requiredHeader.every((header) =>
          headers.includes(header),
        );
        if (!isValid) {
          // If the headers are invalid, return the error message
          return reject({
            status: 400,
            message:
              "Please upload a valid CSV file. Please download and refer to the file from the above download button. Only that format is allowed to import the collection from the CSV file.",
            data: null,
          });
        }
      });

      Readable.from(fileContent)
        .pipe(parser)
        .on("data", (data) => results.push(data))
        .on("end", () => resolve(results))
        .on("error", (error) => reject(error));
    });

    if (records.length === 0) {
      throw new Error("CSV file is empty.");
    }

    // Group the records by Collection Name, URL, Description, and Keywords
    const groupedCollections = records.reduce((acc, record) => {
      const key = `${record["Collection Name"]}|${record["URL"]}|${record["description"]}|${record["keywords"]}`;
      if (!acc[key]) {
        acc[key] = {
          collectionName: record["Collection Name"],
          url: record["URL"],
          description: record["description"],
          keywords: record["keywords"],
          products: [],
        };
      }
      acc[key].products.push({
        name: record["name"],
        handle: record["handle"],
        id: record["id"],
      });
      return acc;
    }, {});

    const collections = Object.values(groupedCollections);

    if (collections.length === 0) {
      throw new Error("No valid collections found in CSV.");
    }

    // Check if collection already exists and filter out duplicates
    const uniqueCollections = [];
    for (const collection of collections) {
      const { data: existingCollection, error: existingError } = await supabase
        .from("collections")
        .select("*")
        .eq("collection_name", collection.collectionName)
        .eq("collection_url", collection.url)
        .eq("store_id", storeData.id);

      if (existingError) {
        loggerError(
          'Error during uniqueness check.',storeName, existingError.message,
        );
        return {
          status: 400,
          message: "Error during uniqueness check.",
          data: null,
        };
      }

      // Only include collection if it doesn't already exist
      if (!existingCollection || existingCollection.length === 0) {
        uniqueCollections.push(collection);
      }
    }

    if (uniqueCollections.length === 0) {
      return {
        status: 400,
        message: "All collections already exist in the database.",
        data: null,
      };
    }

    // Prepare data for batch insertion
    const collectionsToInsert = uniqueCollections.map((collection) => ({
      uuid: uuidv4(),
      store_id: storeData.id,
      collection_name: collection.collectionName,
      collection_url: collection.url,
      product_details: collection.products,
      keywords: collection.keywords,
      description: collection.description,
      created_at: formattedTime,
      updated_at: formattedTime,
      generated_time: formattedTime,
      status: "generated",
    }));

    // Insert all collections at once
    const { data: insertData, error: insertError } = await supabase
      .from("collections")
      .insert(collectionsToInsert);

    if (insertError) {
      loggerError(
        'Failed to insert collections.',storeName, insertError.message,
      );
      return {
        status: 400,
        message: "Failed to insert collections into the database.",
        data: null,
      };
    }

    loggerInfo(
      `Successfully inserted ${collectionsToInsert.length} collections.`,storeName,  file
    );
    return {
      status: 200,
      message: "CSV file processed and data inserted successfully.",
      data: collectionsToInsert,
    };
  } catch (error) {
    if (error.status === 400) {
      return error; // Return the specific header error if it was thrown during header validation
    }
    loggerError('Something went wrong.',storeName, error.message);
    return {
      status: 400,
      message: error.message || "Something went wrong.",
      data: null,
    };
  }
};
