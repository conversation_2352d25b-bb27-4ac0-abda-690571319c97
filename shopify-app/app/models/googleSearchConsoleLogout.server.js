import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import { loggerError, loggerInfo } from "../helper/loggerHelper";

export const googleSearchConsoleLogout = async (admin) => {
    const shop = await shopDetails(admin);
    const storeName = await shop?.data?.shop?.name;
    loggerInfo("Google Search Console logout function is initialized.", storeName);
    try{

        const { data: storeData, error: storeDataError } = await supabase.from('stores').update({is_gsc_logged_in: false, gsc_user_data: null  }).eq('store_name', storeName).select('*');
        if(storeDataError){
            loggerError("Something went wrong", storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        loggerInfo("Account logout successfully.", storeName);
        return {
            status: 200,
            message: "Account logout successfully.",
            data: null
        }

    }catch(error){
        loggerError("Something went wrong", storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}