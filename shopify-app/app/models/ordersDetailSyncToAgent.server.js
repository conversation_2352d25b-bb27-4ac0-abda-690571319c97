import axios from 'axios'
import dotenv from 'dotenv'
import { loggerError, loggerInfo } from '../helper/loggerHelper'
import { supabase } from '../db/supabase_insert_helper'
import { engineBaseUrl, engineAPIVersion, collectionRoute, syncRoute, ordersRoute } from '../configs/config'

dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${ordersRoute}`

export const ordersSyncToAgent = async (shop, orderData) => {
    loggerInfo(`Syncing orders to agent for store`, shop, orderData)
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_url', shop)
        if(storeDataError){
            loggerError('Store Details not found',shop, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }


        // ************** Testing EndPoint *************************
        const endPoint = `https://webhook.site/b95a807c-2ea1-4974-b993-55e3be0cfeea`

        const response = await axios.post(endPoint, orderData)
        loggerInfo('Orders synced successfully to agent', shop);
        return {
            status: 200,
            message: "Orders synced successfully to agent",
            data: null
        }


        // ************** Actual EndPoint *************************
        // const apiToken = storeData[0].engine_api_token
        // const payLoad = typeof orderData === 'string' ? JSON.parse(orderData) : orderData

        
        // const response = await axios.post(endPoint, payLoad, {
        //     headers: {
        //         "X-PROACTIVE-TOKEN": apiToken,
        //         "X-BINCHA-APP-TOKEN": appToken
        //     }
        // })
        
        // loggerInfo('Orders synced successfully to agent', shop);
        // return {
        //     status: response.data.status_code,
        //     message: response.data.message,
        //     data: response.data.data
        // }

    }catch(error){
        loggerError('Something went wrong with the orders synced with Agent (Orders sync function to agent)',shop, error.message);
        return {
            status: 400,
            message: "Something went wrong", 
            data: null
        }
    }
}