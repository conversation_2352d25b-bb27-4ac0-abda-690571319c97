import { supabase } from "../db/supabase_insert_helper";
import { loggerInfo, loggerError } from "../helper/loggerHelper";
import { shopDetails } from "./saveStoreData.server";
import { productGID } from "../configs/config";

export const fetchSingleProductDetails = async (admin, id) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    
    loggerInfo(`Fetching Single Product Details for selected Collection`, storeName, id);

    try {
        // Fetch store details
        const { data: storeData, error: storeDataError } = await supabase
            .from("stores")
            .select("*")
            .eq("store_name", storeName);

        if (storeDataError || !storeData.length) {
            loggerError("Store details not found", storeName, storeDataError?.message);
            return { status: 400, message: "Store details not found", data: null };
        }

        // Fetch product details
        const { data: productData, error: productDataError } = await supabase
            .from("collections")
            .select("product_details")
            .eq("id", id)
            .eq("store_id", storeData[0].id);

        if (productDataError || !productData.length) {
            loggerError("Product details not found", storeName, productDataError?.message);
            return { status: 400, message: "Product details not found", data: null };
        }

        // Extract product IDs from product_details
        const productIds = productData[0].product_details.map(product => product.id);
        
        
        if (!productIds || productIds.length === 0) {
            return { status: 200, message: "No products found", data: { products: [], collectionId: id } };
        }

        // Fetch product details from Shopify GraphQL API
        let allProducts = [];
        
        for (const productId of productIds) {
            const fullProductId = `${productGID}${productId}`;
            
            
            try {
                const productDetailResponse = await admin.graphql(`
                    query {
                        product(id: "${fullProductId}") {
                            id
                            title
                            totalInventory
                            priceRangeV2 {
                                minVariantPrice {
                                    amount
                                }
                            }
                            media(first: 1) {
                                edges {
                                    node {
                                        preview {
                                            image {
                                                url
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                `);
                
                // Parse the response as JSON
                const productDetail = await productDetailResponse.json();
                
                
                if (productDetail?.data?.product) {
                    const product = productDetail.data.product;
                    allProducts.push({
                        id: product.id,
                        title: product.title,
                        price: product.priceRangeV2?.minVariantPrice?.amount || "0.00",
                        image: product.media?.edges?.[0]?.node?.preview?.image?.url || "",
                        totalInventory: product.totalInventory,
                    });
                } else {
                    console.log(`No product data returned for ID: ${productId}`);
                }
            } catch (graphqlError) {
                console.error(`GraphQL query failed for product ID: ${productId}`, graphqlError);
            }
        }
        
        
        return { 
            status: 200, 
            message: allProducts.length > 0 ? "Product details found" : "No product details found", 
            data: {
                products: allProducts, 
                collectionId: id
            } 
        };
    } catch (error) {
        loggerError("Something went wrong while fetching product details", storeName, error.message);
        return { status: 400, message: "Something went wrong", data: null };
    }
};