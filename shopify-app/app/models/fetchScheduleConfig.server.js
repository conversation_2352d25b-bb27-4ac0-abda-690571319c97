import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import { loggerError, loggerInfo } from '../helper/loggerHelper';

export const fetchScheduleConfig = async (admin) => {
    try {
        const shop = await shopDetails(admin);
        const storeName = shop?.data?.shop?.name;
        loggerInfo("Fetch schedule settings from DB", storeName);

        const { data: storeData, error: storeDataError } = await supabase
            .from('stores')
            .select('*')
            .eq('store_name', storeName);

        if (storeDataError) {
            loggerError('Something went wrong of storeData not found', storeName, storeDataError.message);
            return {
                status: 400,
                message: "Store Data Not found",
                data: null
            };
        }

        if (storeData && storeData.length > 0) {
            const { data: scheduleData, error: scheduleDataError } = await supabase
                .from('preferences')
                .select('*')
                .eq('store_id', storeData[0].id);

            if (scheduleDataError) {
                loggerError("Something went wrong to fetch the schedule Data from DB", storeName, scheduleDataError.message);
                return {
                    status: 400,
                    message: "Something went wrong",
                    data: null
                };
            }

            const config = scheduleData[0];
            loggerInfo("Schedule configuration fetched successfully from DB", storeName);
            return {
                status: 200,
                message: "Schedule configuration fetched successfully",
                data: {
                    frequency: config.scheduled_frequency,
                    count: config.number_of_pages,
                    impact: config.impact_type,
                    product: config.product_attributes,
                    seasonal: config.seasonal_attributes,
                    customer: config.customer_attributes,
                    market: config.market_attributes,
                    urlSync: storeData[0].url_synced,
                    scheduleStatus: config.scheduled_status,
                    location: config.location_attributes,
                    automaticUpdate: storeData[0].automatic_update
                },
                shopDomain: shop?.data?.shop?.myshopifyDomain
            };
        }
    } catch (error) {
        loggerError("Error fetching schedule config", null, error.message);
        return {
            status: 500,
            message: "Error fetching schedule config",
            data: null
        };
    }
};
