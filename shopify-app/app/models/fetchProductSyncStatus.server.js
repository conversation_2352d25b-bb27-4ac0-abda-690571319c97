import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import {loggerError, loggerInfo} from '../helper/loggerHelper'
import { engineBaseUrl, engineAPIVersion, collectionRoute, syncRoute, syncAllProduct, productStatusSubRoute } from "../configs/config";
import axios from 'axios'
import dotenv from 'dotenv'
import { ContactLink, contactEmail } from "../configs/config";
import { ServerClient } from "postmark";


dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN;

const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
const senderMail = process.env.COLLECTION_SENDER_MAIL;
async function sendCollectionEmail(toEmail, emailData) {
    try {
        

        const response = await client.sendEmailWithTemplate({
            From: senderMail,
            To: toEmail, // Use the passed email instead of hardcoding
            TemplateId: process.env.PRODUCT_SYNC_ID,
            TemplateModel: emailData, // Use the properly formatted object
            Tag: "CollectionNotification",
            TrackOpens: true,
            TrackLinks: "HtmlAndText"
        });
        loggerInfo("Email sent successfully", response.To);
        return response;
    } catch (error) {
        loggerError("Error sending email", error.message);
        throw error;
    }
}


export const fetchProductSyncStatusFromAgent = async (admin, id) => {
    const shop = await shopDetails(admin)
    const storeName = shop?.data?.shop?.name;
    const storeEmail = shop?.data?.shop?.email
    loggerInfo("Fetch product sync status from Agent DB", storeName, id)
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError("Store Data Not found (Fetch Product Sync Status )", storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        const engineAPIToken = storeData[0].engine_api_token;
        const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${syncAllProduct}${productStatusSubRoute}/${id}`
        const productSyncStatus = await axios.get(endPoint, {
            headers: {
                'X-PROACTIVE-TOKEN': engineAPIToken,
                'X-BINCHA-APP-TOKEN': appToken
            }
        })
        const emailData = {
            contact_info: contactEmail,
            storeName: storeName
        }
        if(productSyncStatus.data.data.status === 'COMPLETED'){
            await sendCollectionEmail(storeEmail, emailData)
        }
        loggerInfo("Product Sync status fetched successfully", storeName, id);
        return{
            status: productSyncStatus.data.status_code,
            message: productSyncStatus.data.message,
            data: {
                syncStatus: productSyncStatus.data.data.status,
                syncedProducts: productSyncStatus.data.data?.sync_stats?.synced_products
            }
        }

    }catch(error){
        loggerError('Something went wrong to fetch the task status of Product sync from Agent DB', storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}