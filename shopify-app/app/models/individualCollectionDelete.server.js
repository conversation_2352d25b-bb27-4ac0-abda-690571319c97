import { shopDetails } from "../helper/shopDetails";
import { supabase } from "../db/supabase_insert_helper";
import { collectionGID } from "../configs/config";
import { isCollectionAvailable } from "../helper/checkCollectionAvailable";
import { collectionsDelete } from "../helper/collectionsDelete";
import { loggerInfo, loggerError } from "../helper/loggerHelper";

export const individualCollectionDelete = async (admin, id) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(
    `Collection Delete function is initialized (Collection Delete)`,storeName,  id
  );
  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError(
        'Something went wrong storeData not found (Collection Delete)',storeName, storeDataError.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
    if (storeData && storeData.length > 0) {
      const { data: collectionData, error: collectionDataError } =
        await supabase
          .from("collections")
          .select("*")
          .eq("store_id", storeData[0].id)
          .eq("id", id);
      if (collectionDataError) {
        loggerError(
          'Something went wrong to get the collection Details (Collection Delete)',storeName, collectionDataError.message,
        );
        return {
          status: 400,
          message: "Something went wrong",
          data: null,
        };
      }
      if (collectionData && collectionData.length > 0) {
        const collectionGlobalId = await isCollectionAvailable(
          admin,
          collectionData[0].collection_id,
        );
        if (collectionGlobalId !== null) {
          //  Mutation for delete the collection From shopify database
          const collectionDeleteFromShopify = await collectionsDelete(
            admin,
            collectionGlobalId,
          );
          if(collectionDeleteFromShopify.length > 0){
            loggerError('Something went wrong (Collection Delete)',storeName, collectionDeleteFromShopify[0].message);
            return {
                status:400,
                message:'Something went wrong',
                data: null
            }
          }
        }
        const { data: deleteData, error: deleteDataError } = await supabase
          .from("collections")
          .delete()
          .eq("id", id)
          .eq("store_id", storeData[0].id);
        if (deleteDataError) {
          loggerError(
            'Something went wrong to delete the collection from DB (Collection Delete)',storeName, deleteDataError.message,
          );
          return {
            status: 400,
            message: "Something went wrong",
            data: null,
          };
        }
        loggerInfo(
          `Collection Deleted successfully (Collection Delete)`,storeName,id
        );
        return {
          status: 200,
          message: "Collection Deleted Successfully",
          data: id,
        };
      }
    }
  } catch (error) {
    loggerError(
      'Something went wrong to delete the collection (Collection Delete)',storeName, error.message
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
