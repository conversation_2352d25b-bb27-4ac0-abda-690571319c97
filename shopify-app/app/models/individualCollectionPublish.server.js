import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import {collectionGID, contactEmail, productGID} from '../configs/config'
import { removeGid } from "../helper/removeGID";
import {loggerInfo, loggerError} from '../helper/loggerHelper'
import { ContactLink } from "../configs/config";
import dotenv from 'dotenv'
import { ServerClient } from "postmark";

dotenv.config();

const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
const senderMail = process.env.COLLECTION_SENDER_MAIL;
const contanctLink = ContactLink

async function sendPublishedCollectionEmail(storeEmail, emailData) {
    try {
        const response = await client.sendEmailWithTemplate({
            From: senderMail,
            To: storeEmail,
            TemplateId: process.env.PUBLISHED_COLLECTION_ID,
            TemplateModel: emailData,
            Tag: "PublishedCollectionNotification",
            TrackOpens: true,
            TrackLinks: "HtmlAndText"
        });
        loggerInfo(`✅ Published notification email sent successfully to ${storeEmail}`);
        return response;
    } catch (error) {
        loggerError(`❌ Error sending published notification email to ${storeEmail}:`, error.message);
        throw error;
    }
}

export const individualCollectionPublish = async (admin, id) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    const storeDomain = shop?.data?.shop?.myshopifyDomain;
    const appHandle = shop?.data?.app?.handle
    loggerInfo(`Collection Publish function is initialized. (Collection Publish)`,storeName,  id);
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError('Something went wrong store details not found (Collection Publish)',storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }

        const {data: preferencesData, error: preferencesDataError} = await supabase.from('preferences').select('*').eq('store_id', storeData[0].id)
        if(preferencesDataError) {
            loggerError("Something went wrong or preferences data not available", storeName, preferencesData.message)
            return{
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }

        if(storeData && storeData.length > 0){
            const {data: collectionData, error: collectionDataError} = await supabase.from('collections').select('*').eq('id', id).eq('store_id', storeData[0].id);
            if(collectionDataError){
                loggerError('Something went wrong collection details not found (Collection Publish)',storeName, collectionDataError.message)
                return {
                    status: 400,
                    message: "Something went wrong",
                    data: null
                }
            }
            if(collectionData && collectionData.length > 0){
                const {collection_name, collection_url, product_details, collection_id, description} = collectionData[0];
                let collectionGlobalId = `${collectionGID}${collection_id}`

                const findCollectionResponse = await admin.graphql(`
                    {
                      collection(id: "${collectionGlobalId}") {
                        id
                        title
                      }
                    }
                  `);
                  const findResponse = await findCollectionResponse.json();
                  const isCollectionAvailable = findResponse?.data?.collection;

                // Collection Create and extract the collection Id from here 
                if(!isCollectionAvailable){
                    const createCollection = await admin.graphql(`
                            mutation collectionCreate($input: CollectionInput!){
                                collectionCreate(input: $input){
                                    userErrors{
                                        field
                                        message
                                    }
                                        collection{
                                            id
                                            title
                                            handle
                                            descriptionHtml
                                        }
                                }
                            }
                        `,{
                            variables: {
                                input: {
                                    title: collection_name,
                                    handle: collection_url,
                                    descriptionHtml: `<p>${description}</p>`
                                }
                            }
                        })
                        const iDResponse = await createCollection.json();
                        const userError = iDResponse?.data?.collectionCreate?.userErrors
                        collectionGlobalId = iDResponse?.data?.collectionCreate?.collection?.id;
                        if(userError && userError.length > 0 ){
                            loggerError('Something went wrong on create collection (Collection Publish)',storeName, userError.message);
                            return {
                                status: 400,
                                message: "Something went wrong",
                                data: null
                            }
                        }
                    
                        // Add Products Logic
                        const productIds = product_details.map((product) =>`${productGID}${product.id}` )
                        const addProducts = await admin.graphql(`
                                mutation collectionAddProducts($id: ID!, $productIds: [ID!]!){
                                    collectionAddProducts(id: $id, productIds: $productIds){
                                        collection{
                                            id
                                            handle
                                            title
                                        }
                                        userErrors{
                                            field
                                            message
                                        }
                                    }
                                }
                            `,{
                                variables: {
                                    id: collectionGlobalId,
                                    productIds: productIds
                                }
                            })
                        const productResponse = await addProducts.json();
                        const productErrors = productResponse?.data?.collectionAddProducts?.userErrors;
                        if(productErrors && productErrors.length > 0){
                            loggerError('Something went wrong on add the products to collection (Collection Publish)',storeName, productErrors.message);
                            return {
                                status: 400,
                                message: "Something went wrong",
                                data: null
                            }
                        }
                }

                        // Fetch Publication for publish the collection in onlin store
                        const publications = await admin.graphql(`
                            {
                                publications(first: 100 catalogType: APP){
                                    edges{
                                        node{
                                            id
                                            name
                                            catalog{
                                                title
                                            }
                                        }
                                    }
                                }
                            }
                            `)
                        const publicationResponse = await publications.json();
                        const onlineStore = publicationResponse.data.publications.edges.find(({ node }) =>
                            node?.name?.toLowerCase().includes('online store')
                        );
                        const publicationId = onlineStore?.node?.id
                        if(!publicationId){
                            loggerError(`Something went wrong to the online store publication id is not found (Collection publish)`,storeName);
                            return {
                                status: 400,
                                message: "Something went wrong",
                                data: null
                            }
                        }

                        // Publish the collection to online store (Mutation)
                        const currentTime = new Date().toISOString();

                        const publishCollectionMutation = await admin.graphql(
                            `
                              mutation publishablePublish($id: ID!, $input: [PublicationInput!]!) {
                                publishablePublish(id: $id, input: $input) {
                                  publishable {
                                    ... on Collection {
                                      id
                                      title
                                      handle
                                      publishedOnPublication(publicationId: "${publicationId}")
                                    }
                                  }
                                  userErrors {
                                    field
                                    message
                                  }
                                }
                              }
                            `,
                            {
                              variables: {
                                id: collectionGlobalId,
                                input: [
                                  {
                                    publicationId: publicationId,
                                    publishDate: currentTime,
                                  },
                                ],
                              },
                            },
                          );
                            const publishedResponse = await publishCollectionMutation.json();
                            const publishError = publishedResponse?.data?.publishablePublish?.userErrors;
                            if(publishError && publishError.length > 0){
                                loggerError(`Something went wrong to publish the collection using publish mutation (Collection Publish)`,storeName,publishError.message );
                                return {
                                    status: 400,
                                    message: "something went wrong",
                                    data: null
                                }
                            }

                        const {data: updateData, error: updateError} = await supabase.from('collections').update({
                            status: 'published',
                            published_type: 'manual',
                            collection_id: removeGid(collectionGlobalId),
                            published_time: currentTime
                        }).eq('id', id).eq('store_id', storeData[0].id).select("*")
                        if(updateError && updateError.length > 0){
                            loggerError('Something went wrong to update the collection status on DB (Collection Publish)',storeName, updateError.message);
                            return {
                                status: 400,
                                message: "Something went wrong",
                                data: null
                            }
                        }
                        const emailStatus = preferencesData[0].email_enabled;
                        const collectionDetails = collectionData.length > 0 
                            ? collectionData.map(item => ({ name: item.collection_name, url: `https://${storeDomain}/collections${item.collection_url}` })) 
                            : [];
                        if (emailStatus) {
                            const emailData = {
                                store_name: storeName,
                                collections: collectionDetails, // Ensure this is always an array
                                ctaUrl: `https://admin.shopify.com/store/${storeName}/apps/${appHandle}/app/collection_drafts`, // Or generate dynamically
                                supportUrl: contactEmail,
                                supportLinkText: contactEmail
                            };
                            
                            try {
                                await sendPublishedCollectionEmail(shop?.data?.shop?.email, emailData);
                            } catch (emailError) {
                                loggerError(`❌ Failed to send published collection email to ${storeName}:`, emailError.message);
                            }
                        }
                        loggerInfo(`Collection Published Successfully (Collection Publish)`,storeName,  id);
                        return {
                            status: 200,
                            message: "Collection Published Successfully",
                            data: {
                                status: updateData[0].status,
                                publishedAt: updateData[0].published_time,
                                id: updateData[0].id
                            }
                        }
            }
        }
    }catch(error){
        loggerError('Something went wrong on publish the collection (Collection Publish)',storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}