import axios from 'axios'
import dotenv from 'dotenv'
import { loggerInfo, loggerError } from '../helper/loggerHelper'
import { supabase } from '../db/supabase_insert_helper'
import { engineBaseUrl, engineAPIVersion, collectionRoute, syncRoute, deleteRoute } from '../configs/config'

dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${deleteRoute}`

export const deletedProductSyncToAgent = async (shop, productId) => {
    loggerInfo ("Deleted Product Details sync function initialized", shop, productId);
    try{

        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_url', shop)
        if(storeDataError){
            loggerError('Store Details not found',shop, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }

        // ************** Testing EndPoint *************************
        const endPoint = `https://webhook.site/b95a807c-2ea1-4974-b993-55e3be0cfeea`
        const response = await axios.post(endPoint, productId)
        loggerInfo('Deleted Product synced successfully to agent', shop);
        return {
            status: 200,
            message: "Deleted Product synced successfully to agent",
            data: null
        }

        // Actual EndPoint Implementation
        // const apiToken = storeData[0].engine_api_token
        
        // const response = await axios.post(endPoint, productId, {
        //     headers: {
        //         "X-PROACTIVE-TOKEN": apiToken,
        //         "X-BINCHA-APP-TOKEN": appToken
        //     }
        // })

        // loggerInfo('Deleted Product synced successfully to agent', shop);
        // return {
        //     status: response.data.status_code,
        //     message: response.data.message,
        //     data: null
        // }
    
    }catch(error){
        loggerError("Something went wrong", shop, error.message)
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}