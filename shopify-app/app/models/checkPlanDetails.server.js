import { supabase } from "../db/supabase_insert_helper";
import { loggerInfo, loggerError } from "../helper/loggerHelper";
import { removeGid } from "../helper/removeGID";
import { shopDetails } from "./saveStoreData.server";
import { planDetails } from "../configs/planDetailsConfigs";
import { addDaysToCurrentTime } from "../helper/formatDateAndTime";

export const checkPlanDetails = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(`To check the plan subscription`, storeName);
  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError(
        "Something went wrong or store details are not found",
        storeName,
        storeDataError.message,
      );
      return {
        status: 400,
        message: "Store Details Not found",
        data: null,
      };
    }
    const { data: planData, error: planDataError } = await supabase
      .from("plans")
      .select("*")
      .eq("store_id", storeData[0].id);
    if (planDataError) {
      loggerError(
        "Plan details not available",
        storeName,
        planDataError.message,
      );
      return {
        status: 400,
        message: "Something went wrong or Plan Details not available",
        data: null,
      };
    }
    const shopData = storeData[0];
    const planName = planData[0];
    if (!shopData.is_free_planned || shopData.payment_status !== "ACTIVE") {
      const chargeIdDetailsFromShopify = await admin.graphql(`
            query{
              currentAppInstallation{
                activeSubscriptions{
                  id
                  status
                  test
                  trialDays
                  name
                  createdAt
                }
              }
            }
          `);
      const response = await chargeIdDetailsFromShopify.json();
      if (
        response?.data?.currentAppInstallation?.activeSubscriptions?.length > 0
      ) {
        const chargeId = removeGid(
          response?.data?.currentAppInstallation?.activeSubscriptions[0]?.id,
        );
        const paymentStatus =
          response?.data?.currentAppInstallation?.activeSubscriptions[0]
            ?.status;
        const planName =
          response?.data?.currentAppInstallation?.activeSubscriptions[0]?.name;
        const selectedPlan = planDetails.find(
          (plan) => plan.planName === planName,
        );
        const createdAt =
          response?.data?.currentAppInstallation?.activeSubscriptions[0]
            ?.createdAt;
        const currentDate = new Date();
        if (selectedPlan) {
          const { features } = selectedPlan;
          const planFlags = {
            is_free_planned: planName === "Free Plan",
            is_growth_planned: planName === "Growth Plan",
            is_advance_planned: planName === "Advanced Plan",
            is_automatic_plan: planName === "Pro",
          };

          const planCreatedAt =
            planName === "Free Plan" ? currentDate : createdAt;
          const planExpiresAt = addDaysToCurrentTime(planCreatedAt, 30);

          const { data: updateData, error: updateError } = await supabase
            .from("stores")
            .update({
              charge_id: chargeId,
              payment_status: paymentStatus,
              plan_created_at: planCreatedAt,
              plan_expires_at: planExpiresAt,
              onboarding_status: true,
              ...features,
              ...planFlags,
            })
            .eq("store_name", storeName)
            .select("*");
          if (updateError) {
            loggerError(
              "Something went wrong to update the latest payment details",
              storeName,
              updateError.message,
            );
            return {
              status: 400,
              message: "Something went wrong to update the payment status",
              data: null,
            };
          }
          const {data: plan, error: planError} = await supabase.from('plans').select('*').eq('store_id', storeData[0].id)
          if(planError){
            loggerError("Something went wrong to fetch the plan details", storeName, planError.message)
            return {
              status: 400,
              message: "Something went wrong",
              data: null
            }
          }
          if(plan[0].plan_name === 'Free Plan'){
            const {data: countUpdate, error: countUpdateError} = await supabase.from('stores').update({collection_generated_count: 0}).eq('store_name', storeName).select('*')
            if(countUpdateError){
              loggerError("Something went wrong to update the collection count", storeName, countUpdateError.message)
              return {
                status: 400,
                message: "Something went wrong",
                data: null
              }
            }
          }
          const { data: planUpdate, error: planUpdateError } = await supabase
            .from("plans")
            .update({ plan_name: selectedPlan.planName })
            .eq("store_id", storeData[0].id);
          if (planUpdateError) {
            loggerError(
              "Something went wrong to update the plan details on plan table",
              storeName,
              planUpdateError.message,
            );
            return {
              status: 400,
              message: "Something went wrong",
              data: null,
            };
          }
          loggerInfo("The payment status is updated on the table", storeName);
          return {
            status: 200,
            message: "Data updated successfully",
            data: {
              isFreePlan: updateData[0].is_free_planned,
              isGrowthPlan: updateData[0].is_growth_planned,
              isAdvancedPlan: updateData[0].is_advance_planned,
              isAutomaticPlan: updateData[0].is_automatic_plan,
              paymentStatus: updateData[0].payment_status,
              automaticUpdate: updateData[0].automatic_update,
            },
          };
        }
      }
    }
    return {
      status: 200,
      message: "Plan details fetched successfully",
      data: {
        isFreePlan: shopData.is_free_planned,
        isPaidPlan: planName.plan_name,
        isGrowthPlan: shopData.is_growth_planned,
        isAdvancedPlan: shopData.is_advance_planned,
        paymentStatus: shopData.payment_status,
        automaticUpdate: shopData.automatic_update,
      },
    };
  } catch (error) {
    loggerError(
      "Something went wrong to fetch the plan Details",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
