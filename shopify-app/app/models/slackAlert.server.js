import { loggerInfo, loggerError } from "../helper/loggerHelper";
import { shopDetails } from "../helper/shopDetails";
import dotenv from "dotenv";
import moment from "moment";
dotenv.config();

export const slackAlert = async (adminOrShop, operation, extraData = {}) => {
  let storeName, storeMail, storeDomain, contactEmail, timezone, phone;

  // Define operations that require admin access
  const requiresAdmin = ["install", "subscription_update", "feature_activation"];

  if (requiresAdmin.includes(operation)) {
    const shop = await shopDetails(adminOrShop);
    storeName = shop?.data?.shop?.name;
    storeMail = shop?.data?.shop?.email;
    storeDomain = shop?.data?.shop?.myshopifyDomain;
    contactEmail = shop?.data?.shop?.contactEmail;
    timezone = shop?.data?.shop?.ianaTimezone;
    phone = shop?.data?.shop?.billingAddress?.phone || "";
  } else {
    // Handle operations where admin is unavailable
    storeDomain = adminOrShop;
    storeName = "Unknown (Uninstalled)";
    storeMail = "N/A";
    contactEmail = "N/A";
    timezone = "N/A";
    phone = "N/A";
  }

  const currentTime = moment().format("MM/DD/YYYY hh:mm A");

  // Custom messages for different operations
  const operationMessages = {
    install: "😊 *New User Installed* 😊",
    uninstall: "😢 *User Uninstalled* 😢",
    subscription_update: "🔄 *Subscription Updated* 🔄",
    feature_activation: "🚀 *Feature Activated* 🚀",
    error: "⚠️ *Error Occurred* ⚠️",
  };

  const title = operationMessages[operation] || "🔔 *Notification* 🔔";

  let additionalDetails = "";
  if (operation === "subscription_update") {
    additionalDetails = `\n*Plan:* ${extraData.plan || "Unknown"}\n*Status:* ${
      extraData.status || "N/A"
    }`;
  } else if (operation === "feature_activation") {
    additionalDetails = `\n*Feature:* ${extraData.feature || "Unknown"}\n*Status:* ${
      extraData.status || "N/A"
    }`;
  } else if (operation === "error") {
    additionalDetails = `\n*Error Message:* ${extraData.message || "Unknown"}`;
  }

  const message = `*🛒 Rank Collections App 📝*\n\n> ┌────────────────────────────┐\n> │ ${title} │\n> └────────────────────────────┘\n\n*Shop Name:* ${storeName}\n*Store Domain:* 🌐 ${storeDomain}\n*Email:* 📧 ${storeMail}\n*Phone:* 📞 ${phone}\n*Timezone:* 🕒 ${timezone}\n*Customer Email:* 📩 ${contactEmail}\n*Date/Time:* ${currentTime}${additionalDetails}`;

  try {
    const endPoint = `${process.env.SLACK_WEBHOOK_URL}${process.env.SLACK_WEBHOOK_ACCESS_KEY}`;
    loggerInfo(`Sending to endPoint: ${endPoint}`, storeDomain);

    const response = await fetch(endPoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ text: message }),
    });

    if (!response.ok) {
      loggerError(`Slack webhook failed with status`, storeDomain, response);
    }

    loggerInfo(`Slack webhook sent successfully for ${operation}`, storeDomain);
    return {
      status: 200,
      message: `Slack webhook sent successfully for ${operation}`,
      data: null,
    };
  } catch (error) {
    loggerError(
      `Something went wrong while sending a message to Slack for ${operation}`,
      storeDomain,
      error.message
    );
    return {
      status: 400,
      message: `Something went wrong while sending a message to Slack for ${operation}`,
      data: null,
    };
  }
};
