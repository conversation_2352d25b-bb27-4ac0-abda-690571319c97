import axios from 'axios'
import dotenv from 'dotenv'
import { loggerError, loggerInfo } from '../helper/loggerHelper'
import { shopDetails } from '../helper/shopDetails'
import { supabase } from '../db/supabase_insert_helper'
import { collectionRoute, engineAPIVersion, engineBaseUrl, fetchSubroute, productCount } from '../configs/config'
import { getProductDetails } from './getProductDetails.server'
import { productsSyncToAgent } from './syncProductsToAgent.server'

dotenv.config();
const appToken = process.env.BINCHA_APP_TOKEN
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${fetchSubroute}${productCount}`

export const fetchProductCount = async (admin) => {
    const shop = await shopDetails(admin)
    const storeName = shop?.data?.shop?.name;
    loggerInfo('Fetch product count from Agent DB function is initialized', storeName)
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError('Store Data Not found (Fetch product count from Agent DB )', storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        const apiToken = storeData[0].engine_api_token;
        const response = await axios.get(endPoint, {
            headers: {
                "X-PROACTIVE-TOKEN": apiToken,
                "X-BINCHA-APP-TOKEN": appToken
            }
        })
        
        const agentDBProductCount = response.data.data.product_count;
        const localDBProductCount = storeData[0].product_count
        if(agentDBProductCount >= localDBProductCount){

            loggerInfo("Product count fetched successfully from Agent DB", storeName)
            return {
                status: response.data.status_code,
                message: response.data.message,
                data: null
            }
        }else {
            const productDetails = await getProductDetails(admin)
            const synctToAgentDB = await productsSyncToAgent(admin, productDetails?.data)
        }
    }catch(error){
        loggerError("Something went wrong to fetch the product count from the Agent DB", storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}