import {shopDetails} from '../helper/shopDetails'
import { supabase } from '../db/supabase_insert_helper';
import { loggerInfo, loggerError } from '../helper/loggerHelper'
import axios from 'axios';
import dotenv from 'dotenv';
import { engineAPIVersion, engineBaseUrl, collectionRoute, fetchSubroute, timeRoute } from '../configs/config';

dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN;

export const fetchBestScheduleTime = async (admin) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    loggerInfo('Fetch Best schedule Time from AI', storeName);
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError("Store Data Not found (fetch AI schedule time) ", storeName, storeDataError.message);
            return {
                status: 400,
                message: "Store Data not found",
                data: null
            }
        }
        const apiToken = storeData[0].engine_api_token;
        const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${fetchSubroute}${timeRoute}`;
        const scheudleTime = await axios.get(endPoint, {
            headers: {
                "X-PROACTIVE-TOKEN": apiToken,
                "X-BINCHA-APP-TOKEN": appToken
            }
        })
        
        loggerInfo("AI generated schedule time is fetched successfully from proactive API", storeName);
        return {
            status: scheudleTime.data.status_code,
            message: scheudleTime.data.message,
            data: scheudleTime.data.data
        }
    }catch(error){
        loggerError('Something went wrong to fetch the AI scheduled time from AI', storeName, error.message);
        return {
            status: 400,
            message: 'Something went wrong',
            data: null
        }
    }
}