import { shopDetails } from './saveStoreData.server';
import { loggerInfo, loggerError } from '../helper/loggerHelper';
import axios from 'axios';

/**
 * Fetches unique continent (region) names from REST Countries API.
 */
export const fetchRegions = async (admin) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    loggerInfo("Fetch the Regions function is initialized", storeName);

    try {
        const url = "https://restcountries.com/v3.1/all?fields=region";
        const response = await axios.get(url);
        const data = response.data;

        // Extract unique regions
        const regionSet = new Set(data.map((country) => country.region).filter(Boolean));
        const regions = Array.from(regionSet).sort();

        loggerInfo("The regions are fetched successfully", storeName);

        return {
            status: 200,
            message: "The regions are fetched successfully from REST Countries API",
            data: regions,
        };
    } catch (error) {
        loggerError("Something went wrong fetching the regions", storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null,
        };
    }
};




const regionMapping = {
    eu: "ECS", // Europe and Central Asia
    na: "NAC", // North America
    as: "EAS", // East Asia and Pacific
    sa: "LCN", // Latin America and Caribbean
    oc: "SSF", // Sub-Saharan Africa (Oceania not directly mapped)
    af: "MEA", // Middle East and North Africa
    an: "ANT", // Antarctica (Not typically available)
};

export const fetchCountries = async (admin, region) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    loggerInfo('Countries list fetching function is initialized.', storeName, region);

    try {
        const url = `https://restcountries.com/v3.1/region/${region}`;
        const response = await axios.get(url);
        const data = response.data;

        const countries = data.map((country) => ({
            name: country.name?.common || "",
            officialName: country.name?.official || "",
            cca2: country.cca2, // 2-letter country code
            cca3: country.cca3, // 3-letter country code
            region: country.region,
            subregion: country.subregion,
            flag: country.flags?.svg || "",
        })).sort((a, b) => a.name.localeCompare(b.name));

        loggerInfo("The countries list is fetched successfully for the selected region", storeName, region);

        return {
            status: 200,
            message: 'Countries fetched successfully',
            data: countries,
        };
    } catch (error) {
        loggerError("Something went wrong fetching countries by region", storeName, error.message);

        return {
            status: 400,
            message: "Something went wrong",
            data: null,
        };
    }
};


