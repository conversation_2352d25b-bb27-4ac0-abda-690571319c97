import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import { isCollectionAvailable } from "../helper/checkCollectionAvailable";
import { definition } from "../configs/metafieldDefinition";
import { removeGid } from "../helper/removeGID";
import { loggerInfo, loggerError } from "../helper/loggerHelper";

export const individualCollectionUpdate = async (
  admin,
  id,
  name,
  url,
  description,
  faqs
) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(
   `Individual Edit collection update function is initialized. (Individual Edit Collection and Update)`, storeName, {id, name, url, description, faqs}
  );
  let collectionGlobalId;
  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError(
        'Something went wrong store data not found (Individual Edit Collection and Update.)',storeName, storeDataError.message
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
    if (storeData && storeData.length > 0) {
      const { data: collectionData, error: collectionDataError } =
        await supabase
          .from("collections")
          .select("*")
          .eq("id", id)
          .eq("store_id", storeData[0].id);
      if (collectionDataError) {
        loggerError(
          'Something went wrong or collection details not found (Individual Edit Collection and Update)',storeName, collectionDataError.message
        );
        return {
          status: 400,
          message: "Something went wrong",
          data: null,
        };
      }
      const collectionId = collectionData[0].collection_id;
      collectionGlobalId = await isCollectionAvailable(admin, collectionId);
      if (collectionGlobalId !== null) {
        const collectionUpdateToShopify = await admin.graphql(
          `
                    mutation updateCollection($input: CollectionInput!) {
                      collectionUpdate(input: $input) {
                        collection {
                          id
                          title
                          handle
                          descriptionHtml
                        }
                        userErrors {
                          message
                          field
                        }
                      }
                    }`,
          {
            variables: {
              input: {
                id: collectionGlobalId,
                title: name,
                handle: url,
                descriptionHtml: `<p>${description}</p>`,
              },
            },
          },
        );

        const response = await collectionUpdateToShopify.json();
        const updateError =
          response?.data?.collectionUpdate?.collection?.userErrors;
        if (updateError && updateError.length > 0) {
          loggerError(
            'Something went wrong to update the collection in shopify database (Individual Edit Collection and Update)',storeName, updateError.message
          );
          return {
            status: 400,
            message: `Errors occurred: ${errors}`,
            data: null,
          };
        }
        const FAQUpdate = await admin.graphql(
          `mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
                      metafieldsSet(metafields: $metafields) {
                        metafields {
                          key
                          namespace
                          value
                          createdAt
                          updatedAt
                        }
                        userErrors {
                          field
                          message
                          code
                        }
                      }
                    }`,
          {
            variables: {
              metafields: [
                {
                  key: definition.key,
                  namespace: definition.namespace,
                  ownerId: `gid://shopify/Collection/${collectionId}`,
                  type: "json",
                  value: faqs,
                },
              ],
            },
          },
        );

        const faqResponse = await FAQUpdate.json();
        const faqError = faqResponse?.data?.metafieldsSet?.userError;
        if (faqError && faqError.length > 0) {
          loggerError(
            'Something went wrong on update the FAQ in collection metafield (Collection Edit and Update)',storeName, faqError[0].message
          );
          return {
            status: 400,
            message: "SomeThing went wrong",
            data: null,
          };
        }
      }

      const { data: updateData, error: updateDataError } = await supabase
        .from("collections")
        .update({
          collection_name: name,
          collection_url: url,
          description: description,
        })
        .eq("id", id)
        .eq("store_id", storeData[0].id)
        .select("*");
      if (updateDataError) {
        loggerError(
          'Something went wrong to update the collection in DB (Individual Edit collection and update)',storeName, updateDataError.message
        );
        return {
          status: 400,
          message: "Something went wrong",
          data: null,
        };
      }
      loggerInfo(
        `Collection update successfully (Individual Edit Collection and Update)`,storeName, {id, name, url, description, faqs}
      );
      return {
        status: 200,
        message: "Collection Updated Successfully",
        data: {
          id: updateData[0].id,
          name: updateData[0].collection_name,
          url: updateData[0].collection_url,
          description: updateData[0].description,
        },
      };
    }
  } catch (error) {
    loggerError(
      'Something went wrong (Individual Edit Collection and Update)',storeName, error.message
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
