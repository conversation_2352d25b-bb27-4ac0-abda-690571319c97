import { shopDetails } from "./saveStoreData.server";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { supabase } from "../db/supabase_insert_helper";
import axios from "axios";
import {
  engineBaseUrl,
  engineAPIVersion,
  collectionRoute,
  syncRoute,
  schedulingRount,
} from "../configs/config";
import dotenv from "dotenv";

dotenv.config();

const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${schedulingRount}`;

export const synctSchedulingInputsToAgent = async (
  admin,
  attributes,
  scheduleSettings,
) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  const storeDomain = shop?.data?.shop?.myshopifyDomain;
  loggerInfo("Sync the scheduling Inputs to agent <PERSON>", storeName, {
    attributes, scheduleSettings
  });
  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError(
        "Something went wrong or Store Data Not found",
        storeName,
        storeDataError.message,
      );
      return {
        status: 400,
        message: "Store Data Not found",
        data: null,
      };
    }
    const {data: planName, error: planNameError} = await supabase.from('plans').select('*').eq('store_id', storeData[0].id)
    if(planNameError){
        loggerError("Something went wrong or Plan Details not found", storeName, planNameError.message);
        return {
            status: 400,
            message: "Plan details not found",
            data: null
        }
    }
    const xApiToken = storeData[0].engine_api_token;
    
    const user_metadata = {
      domain: storeDomain,
      payment_status: storeData[0].payment_status === 'ACTIVE' ? 'paid' : 'free',
      plan_type: "monthly",
      plan_name: planName[0].plan_name,
    };
    const parsedAttributes = JSON.parse(attributes);
    const parsedScheduleSettings = JSON.parse(scheduleSettings);
    const payLoad = {
      user_metadata,
      attributes: parsedAttributes,
      schedule_settings: parsedScheduleSettings,
    };
    
    const { data: updateData, error: updateError } = await supabase
      .from("preferences")
      .update({
        scheduled_status: true,
        scheduled_frequency: parsedScheduleSettings.frequency,
        scheduled_time: parsedScheduleSettings.start_date,
        next_generated_time: parsedScheduleSettings.next_generated_date,
        next_scheduled_time: parsedScheduleSettings.next_scheduled_date,
        next_published_time: parsedScheduleSettings.next_published_date,
        number_of_pages: parsedScheduleSettings.count,
        impact_type: parsedScheduleSettings.impact_type,
        product_attributes: parsedAttributes.product,
        seasonal_attributes: parsedAttributes.seasonal,
        customer_attributes: parsedAttributes.customer,
        market_attributes: parsedAttributes.market,
        location_attributes: parsedAttributes.location
      })
      .eq("store_id", storeData[0].id)
      .select();
    if (updateError) {
      loggerError(
        "Something went wrong to update the Scheduled settings to Local DB",
        storeName,
        updateError.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
    
    const syncSchedule = await axios.post(endPoint, payLoad, {
      headers: {
        "Content-type": "application/json",
        "X-PROACTIVE-TOKEN": xApiToken,
        "x-BINCHA-APP-TOKEN": process.env.BINCHA_APP_TOKEN,
      },
    });
    
    loggerInfo("Schedule Settings saved on Agent DB and Local DB successfully", storeName, {attributes, scheduleSettings})
    return {
      status: syncSchedule.data.status_code,
      message: syncSchedule.data.message,
      data: syncSchedule.data.data,
    };
  } catch (error) {
    loggerError(
      "Something went wrong to update the scheduling Inputs to agent DB",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
