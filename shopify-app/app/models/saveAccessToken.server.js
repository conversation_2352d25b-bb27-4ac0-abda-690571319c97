import crypto, { randomBytes } from "crypto";
import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import {loggerInfo, loggerError} from '../helper/loggerHelper'
import axios from 'axios'
import { engineBaseUrl, engineAPIVersion, engineAuth, engineInstall } from "../configs/config";
import dotenv from 'dotenv';

export const saveAccessToken = async (admin, length) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  const storeDomain = shop?.data?.shop?.myshopifyDomain
  loggerInfo("Save access token function is initialized...",storeName, null);
  try {
    const accessToken = admin?.rest?.session?.accessToken;
    
    let encrypted;
    let key;
    let iv;

    if (accessToken) {
      key = randomBytes(32); // 32 bytes for AES-256
      iv = randomBytes(16); // 16 bytes for AES-256-CBC

      const algorithm = "aes-256-cbc";

      const cipher = crypto.createCipheriv(algorithm, key, iv);
      encrypted = cipher.update(accessToken, "utf8", "hex");
      encrypted += cipher.final("hex");
    } else {
      loggerError("The access token is not available.",storeName, );
      return {
        status: 400,
        message: "Access token is not available.",
        data: null,
      };
    }

    // Install and fetch the api token from the generator engine AI ********************************************
    const endPoint = `${engineBaseUrl}${engineAPIVersion}${engineAuth}${engineInstall}`
    const payload = {
      shop_domain: storeDomain,
      app_type: "rankCollections"
    };
    const response = await axios.post(endPoint, payload, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // *********************************************************************************************************
    // Save the encrypted token, key, and iv to the database
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);

    if (storeDataError) {
      loggerError('The store details are not found or something went wrong.',storeName,
        storeDataError.message
      );
      return {
        status: 400,
        message: "Something went wrong or store data is not found.",
        data: null,
      };
    }

    if (storeData && storeData.length > 0) {
      const { data: updatedStoreData, error: updatedStoreDataError } =
        await supabase
          .from("stores")
          .update({
            access_token: encrypted,
            encryption_key: key.toString("hex"), // Storing key as hex
            iv: iv.toString("hex"), // Storing iv as hex
            is_store_connected: true,
            product_count: length,
            engine_api_token: response.data.data.api_token
          }).select('*')
          .eq("store_name", storeName);

      if (updatedStoreDataError) {
        loggerError('Something went wrong saving the details in the DB.',storeName,
          updatedStoreDataError.message
        );
        return {
          status: 400,
          message: "Something went wrong saving the details in the DB.",
          data: null,
        };
      }
      loggerInfo("The access token details saved successfully.",storeName, null);
      return {
        status: 200,
        message: "AccessToken saved successfully.",
        data: {storeConnected: updatedStoreData[0].is_store_connected, productsCount: updatedStoreData[0].product_count}
      };
    }

  } catch (error) {
    loggerError('Something went wrong saving the access token details.',storeName,
      error.message
    );
    return {
      status: 400,
      message: "Something went wrong saving the access token details.",
      data: null,
    };
  }
};
