import { shopDetails } from "./saveStoreData.server";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { supabase } from "../db/supabase_insert_helper";
import {collectionGID} from '../configs/config'
import { collectionFetchById } from "../helper/collectionFetchQueryById";

export const collectionFetch = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo("Collection product details fetch by collection Id", storeName);

  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);

    if (storeDataError) {
      loggerError("Something went wrong or Store Data not found", storeName, storeDataError.message);
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }

    if (storeData && storeData.length > 0) {
      const { data: collectionData, error: collectionDataError } = await supabase
        .from("collections")
        .select("*")
        .eq("status", "published")
        .eq("store_id", storeData[0].id);

      if (collectionDataError) {
        loggerError("Something went wrong or published Collection not available", storeName, collectionDataError.message);
        return {
          status: 400,
          message: "Something went wrong",
          data: null,
        };
      }

      if (collectionData && collectionData.length > 0) {
        const collectionQueries = [];

        for (const collection of collectionData) {
          const collectionId = `${collectionGID}${collection.collection_id}`;
            
          const collectionQuery = await collectionFetchById(admin, collectionId)
          collectionQueries.push(collectionQuery)
          
        }

        return {
          status: 200,
          message: "Collection Details fetched successfully",
          data: collectionQueries,
        };
      }
    }
  } catch (error) {
    loggerError("Something went wrong to fetch collection details by Collection ID", storeName, error.message);
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
