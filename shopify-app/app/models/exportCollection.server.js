import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "../helper/shopDetails";
import { requiredHeader } from "../configs/config";
import moment from 'moment';
import {loggerInfo, loggerError}  from '../helper/loggerHelper'

export const exportCollection = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(`Export collection function is Initialized. (Export Collection)`,storeName,  null);
  try{
    const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
    if(storeDataError){
      loggerError('Something went wrong store Data Not found (Export Collections)',storeName, storeDataError.message);
      return {
        status: 400,
        message: 'Something went wrong',
        data: null
      }
    }
    if(storeData && storeData.length > 0){
      const {data: collectionData, error: collectionDataError} = await supabase.from('collections').select('*').eq('store_id', storeData[0].id);
      if(collectionDataError){
        loggerError('Something Went wrong collection Details not found (Export Collections)',storeName, collectionDataError.message);
        return {
          status: 400,
          message: "Something went wrong",
          data: null
        }
      }
      const headers = requiredHeader
      const csvRows = [headers.join(",")];
      const formatRow = (collection, product) => {
        const collectionKeywords = Array.isArray(collection.keywords) ? collection.keywords.join("; ") : "";
        return [
          `"${collection.collection_name || ""}"`,
          `"${collection.collection_url || ""}"`,
          `"${collection.description || ""}"`,
          `"${collectionKeywords}"`,
          `"${product.name || ""}"`,
          `"${product.handle || ""}"`,
          `"${product.id || ""}"`,
        ].join(",");
      };      
        collectionData.forEach((collection) => {
          collection.product_details.forEach((product) => {
            csvRows.push(formatRow(collection, product));
          });
        });
        const csvContent = csvRows.join("\n");
        const encodedUri = encodeURI(`data:text/csv;charset=utf-8,${csvContent}`);
        loggerInfo(`Collections are exported successfully. (Export Collections)`,storeName,  null);
        return {
          status: 200,
          message: "Collections are exported successfully.",
          data: {
            url: encodedUri,
            fileName: `collections_${moment().format("YYYYMMDD_HHmmss")}.csv`,
          },
        };
    }
  }catch(error){
    loggerError('Something went wrong (Export Collections)',storeName, error.message)
    return {
      status: 400,
      message: 'Something Went wrong',
      data: null
    }
  }
}