import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import { loggerError, loggerInfo } from "../helper/loggerHelper";

export const googleAccountLogout = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo("Google account logout function is initialized",storeName,  null)
  try{

  const { data: storeData, error: storeDataError } = await supabase
    .from("stores")
    .update({
      google_is_logged_in: false,
      google_analytics_key: null,
      google_encrypted_iv: null,
      google_account_id: null,
      google_property_id: null,
    })
    .eq("store_name", storeName)
    .select("*");
  if (storeDataError) {
    loggerError(
      'Something went wong to logout the account',storeName, storeDataError.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
  loggerInfo(`Account logout successfully.`,storeName,   null);
  return {
    status: 200,
    message: "Logout successfully",
    data: storeData[0].google_is_logged_in,
  }
}catch(error){
  loggerError('Something went wrong on google account logout',storeName, error.message);
  return {
    status: 400,
    message: "Something went wrong",
    data: null
  }
}
};
