import { supabase } from "../db/supabase_insert_helper";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { planDetails } from "../configs/planDetailsConfigs";
import { shopDetails } from "./saveStoreData.server";
import { removeGid } from "../helper/removeGID";
import { addDaysToCurrentTime } from "../helper/formatDateAndTime";
import { testCharge } from "../configs/config";

export const pricingPlan = async (admin, planKey) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  const appHandle = shop?.data?.app?.handle;
  loggerInfo("Plan upgrade function initialized", storeName, planKey);

  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);

    if (storeDataError || !storeData?.length) {
      loggerError(
        "Store details not found",
        storeName,
        storeDataError?.message || "No store data found",
      );
      return {
        status: 400,
        message: "Store details not found",
        data: null,
      };
    }

    const storeId = storeData[0].id;
    const selectedPlan = planDetails.find((plan) => plan.planKey === planKey);

    if (!selectedPlan) {
      loggerError(
        "Invalid plan key provided",
        storeName,
        `Plan key ${planKey} not found`,
      );
      return {
        status: 400,
        message: "Invalid plan selected",
        data: null,
      };
    }

    if (selectedPlan.planKey !== "freePlan") {
      loggerInfo(
        "Initiating paid plan upgrade",
        storeName,
        selectedPlan.planKey,
      );
      return await handlePaidPlan(admin, storeName, appHandle, selectedPlan);
    }

    loggerInfo("Initiating free plan upgrade", storeName, selectedPlan.planKey);
    return await handleFreePlan(admin, storeName, storeId, selectedPlan);
  } catch (error) {
    loggerError(
      "Unexpected error in pricing plan update",
      storeName,
      error.message,
    );
    return {
      status: 500,
      message: "An unexpected error occurred while updating the plan",
      data: null,
    };
  }
};

const handlePaidPlan = async (admin, storeName, appHandle, selectedPlan) => {
  try {
    loggerInfo("Creating paid subscription", storeName, selectedPlan.planName);

    const response = await admin.graphql(
      `mutation AppSubscriptionCreate($name: String!, $lineItems: [AppSubscriptionLineItemInput!]!, $returnUrl: URL!, $test: Boolean!, $trialDays: Int!) {
        appSubscriptionCreate(name: $name, returnUrl: $returnUrl, lineItems: $lineItems, test: $test, trialDays: $trialDays) {
          userErrors { field message }
          appSubscription { id status createdAt trialDays test }
          confirmationUrl
        }
      }`,
      {
        variables: {
          name: selectedPlan.planName,
          returnUrl: `https://admin.shopify.com/store/${storeName}/apps/${appHandle}/app`,
          lineItems: [
            {
              plan: {
                appRecurringPricingDetails: {
                  price: { amount: selectedPlan.amount, currencyCode: "USD" },
                  interval: "EVERY_30_DAYS",
                },
              },
            },
          ],
          test: testCharge,
          trialDays: selectedPlan.trailDays,
        },
      },
    );

    const data = await response.json();

    if (data?.data?.appSubscriptionCreate?.userErrors?.length) {
      const errorMessages = data.data.appSubscriptionCreate.userErrors
        .map((err) => err.message)
        .join(", ");
      loggerError("Failed to create subscription", storeName, errorMessages);
      return {
        status: 400,
        message: "Failed to create subscription: " + errorMessages,
        data: null,
      };
    }

    loggerInfo(
      "Subscription created successfully",
      storeName,
      selectedPlan.planName,
    );
    return {
      status: 200,
      message: "Subscription created successfully",
      data: data.data.appSubscriptionCreate.confirmationUrl,
    };
  } catch (error) {
    loggerError(
      "Failed to process paid plan subscription",
      storeName,
      error.message,
    );
    return {
      status: 500,
      message: "Failed to process paid plan subscription",
      data: null,
    };
  }
};

const handleFreePlan = async (admin, storeName, storeId, selectedPlan) => {
  try {
    const { data: existingPlan, error: existingPlanError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);

    if (existingPlanError) {
      loggerError(
        "Error checking existing plans",
        storeName,
        existingPlanError.message,
      );
      return {
        status: 400,
        message: "Failed to check existing plan details",
        data: null,
      };
    }

    const chargeId = existingPlan?.[0]?.charge_id;
    if (chargeId) {
      loggerInfo("Cancelling existing subscription", storeName, chargeId);
      const cancelResult = await cancelExistingSubscription(admin, storeName, chargeId);
      if (cancelResult.status !== 200) {
        return cancelResult;
      }
    }

    const formattedTimestamp = new Date().toISOString().replace('T', ' ').replace('Z', '+00');
    // Update plans table
    const { error: updatePlanError } = await supabase
  .from("plans")
  .update(
    { 
      plan_name: selectedPlan.planName, 
      updated_at: formattedTimestamp
    },
    // Ensures update on duplicate store_id
  ).eq('store_id', storeId);

    if (updatePlanError) {
      loggerError(
        "Failed to update plan",
        storeName,
        updatePlanError.message,
      );
      return {
        status: 400,
        message: "Failed to update plan details",
        data: null,
      };
    }
    const currentDate = new Date();
    const planCreatedAt = currentDate;
    const planExpiresAt = addDaysToCurrentTime(planCreatedAt, 30);
    // Update stores table with free plan details
    const { data: updateData, error: updateStoreError } = await supabase
      .from("stores")
      .update({
        ...selectedPlan.features,
        onboarding_status: true,
        is_free_planned: true,
        is_growth_planned: false,
        is_advance_planned: false,
        is_automatic_plan: false,
        charge_id: null,
        payment_status: null,
        collection_generated_count: chargeId === null ? 0 : selectedPlan.features.collection_count,
        plan_created_at: planCreatedAt,
        plan_expires_at: planExpiresAt
      })
      .eq("store_name", storeName)
      .select("*");

    if (updateStoreError) {
      loggerError(
        "Failed to update store features",
        storeName,
        updateStoreError.message,
      );
      return {
        status: 400,
        message: "Failed to update store features",
        data: null,
      };
    }
    const {data: preferences, error: preferencesError} = await supabase.from('preferences').update({
      scheduled_status: false
    }).eq('store_id', storeId)
    if(preferencesError){
      loggerError("Something went wrong to update the preferences table", storeName, preferencesError.message)
      return {
        status: 400,
        message: "Something went wrong",
        data: null
      }
    }

    loggerInfo(
      "Free plan update completed successfully",
      storeName,
      selectedPlan.planKey,
    );
    return {
      status: 200,
      message: "Plan and store features successfully updated",
      data: updateData[0].is_free_planned,
    };
  } catch (error) {
    loggerError(
      "Unexpected error in free plan update",
      storeName,
      error.message,
    );
    return {
      status: 500,
      message: "An unexpected error occurred while updating to free plan",
      data: null,
    };
  }
};

const cancelExistingSubscription = async (admin, storeName, chargeId) => {
  try {
    loggerInfo("Initiating subscription cancellation", storeName, chargeId);

    const response = await admin.graphql(
      `mutation AppSubscriptionCancel($id: ID!, $prorate: Boolean) {
        appSubscriptionCancel(id: $id, prorate: $prorate) {
          userErrors { field message }
          appSubscription { id status }
        }
      }`,
      {
        variables: {
          id: `gid://shopify/AppSubscription/${chargeId}`,
          prorate: true,
        },
      },
    );

    const data = await response.json();

    if (data?.data?.appSubscriptionCancel?.userErrors?.length) {
      const errorMessage = data.data.appSubscriptionCancel.userErrors
        .map((err) => err.message)
        .join(", ");
      loggerError("Subscription cancellation failed", storeName, errorMessage);
      return {
        status: 400,
        message: "Failed to cancel subscription: " + errorMessage,
        data: null,
      };
    }

    const cancelledChargeId = data?.data?.appSubscriptionCancel?.appSubscription?.id;
    const removeId = removeGid(cancelledChargeId);

    loggerInfo("Subscription cancelled successfully", storeName, chargeId);
    return {
      status: 200,
      message: "Subscription cancelled successfully",
      data: removeId !== null ? removeId : null
    };
  } catch (error) {
    loggerError("Error in subscription cancellation", storeName, error.message);
    return {
      status: 500,
      message: "Failed to process subscription cancellation",
      data: null,
    };
  }
};
