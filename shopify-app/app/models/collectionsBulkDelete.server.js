import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import { isCollectionAvailable } from "../helper/checkCollectionAvailable";
import { collectionsDelete } from "../helper/collectionsDelete";
import { loggerInfo, loggerError } from "../helper/loggerHelper";

export const collectionsBulkDelete = async (admin, ids) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(
    `Collections Bulk Delete function is initialized (Collections Bulk Delete)`,
    storeName,
    ids,
  );

  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError(
        "Something went wrong, shop details not found (Collections Bulk Delete)",
        storeName,
        storeDataError.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
    const idsToBeDelete = ids.split(",").map(Number);
    if (storeData && storeData.length > 0) {
      const deletedIds = [];

      for (const id of idsToBeDelete) {
        const { data: collectionData, error: collectionDataError } =
          await supabase
            .from("collections")
            .select("*")
            .eq("id", id)
            .eq("store_id", storeData[0].id);
        if (collectionDataError) {
          loggerError(
            "Something went wrong, collection details not found",
            storeName,
            collectionDataError.message,
          );
          return {
            status: 400,
            message: "Something went wrong",
            data: null,
          };
        }

        if (collectionData.length > 0) {
          const collectionGlobalId = await isCollectionAvailable(
            admin,
            collectionData[0].collection_id,
          );
          if (collectionGlobalId !== null) {
            const collectionDeleteFromShopify = await collectionsDelete(
              admin,
              collectionGlobalId,
            );
            if (collectionDeleteFromShopify.length > 0) {
              loggerError(
                "Something went wrong on deleting the collection from Shopify (Collections Bulk Delete)",
                storeName,
                collectionDeleteFromShopify[0].message,
              );
              return {
                status: 400,
                message: "Something went wrong",
                data: null,
              };
            }
          }

          const { data: deleteData, error: deleteError } = await supabase
            .from("collections")
            .delete()
            .eq("id", id)
            .eq("store_id", storeData[0].id);
          if (deleteError) {
            loggerError(
              "Something went wrong on deleting the collection from DB (Collections Bulk Delete)",
              storeName,
              deleteError.message,
            );
            return {
              status: 400,
              message: "Something went wrong",
              data: null,
            };
          }

          deletedIds.push(id);
        }
      }

      loggerInfo(
        `The selected collections are deleted successfully (Collections Bulk Delete)`,
        storeName,
        ids,
      );
      return {
        status: 200,
        message: "The selected collections were deleted successfully",
        data: deletedIds,
      };
    }
  } catch (error) {
    loggerError(
      "Something went wrong (Collections Bulk Delete)",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
