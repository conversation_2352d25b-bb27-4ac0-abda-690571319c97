import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { shopDetails } from "./saveStoreData.server";
import { collectionFetchById } from "../helper/collectionFetchQueryById";
import { supabase } from "../db/supabase_insert_helper";
import { collectionGID } from "../configs/config";

export const fetchSingleCollectionDetails = async (admin, collectionId) => {
    const shop = await shopDetails(admin)
    const storeName = shop?.data?.shop?.name;
    loggerInfo("Fetch the collection Details for single collection (published collection)", storeName, collectionId);
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError("Store Data not found", storeName, storeDataError.message)
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        const {data: collectionData, error: collectionDataError} = await supabase.from('collections').select('*').eq('store_id', storeData[0].id).eq('id', collectionId)
        if(collectionDataError){
            loggerError("Something went wrong to fetch the collection data (Delete product from collection DB)", storeName, collectionDataError.message)
            return {
                status: 400,
                message: 'something went wrong',
                data: null
            }
        }
        const collectionShopifyId = `${collectionGID}${collectionData[0].collection_id}`
        const collectionQuery = await collectionFetchById(admin, collectionShopifyId)
        const updatedCollectionQuery = {
            ...collectionQuery,
            collectionId: collectionData[0].id,
            views: collectionData[0].view_count,
            status: collectionData[0].status,
            sortValue: collectionData[0].sorting_value,
            updatedAt: collectionData[0].updated_at
        };
        loggerInfo("The collection Details fetched successfully", storeName, collectionId)
        return {
            status: 200,
            message: "The collectiion details fetched successfully",
            data: updatedCollectionQuery
        }
    }catch(error){
        loggerError('Something went wrong to fetch the collection Details by collectionId', storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}