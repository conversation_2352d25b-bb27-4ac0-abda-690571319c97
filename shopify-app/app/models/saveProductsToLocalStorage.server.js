import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "../helper/shopDetails";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { getProductDetails } from "./getProductDetails.server";

export const saveProductDetailsToLocalDB = async (admin, from, formattedProduct) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    
    loggerInfo("Save product details to local DB is initialized", storeName);

    try {
        const productDetails = from === 'overall' ? await getProductDetails(admin) : formattedProduct;
        const productData = productDetails.data;
        
        if (!productData || productData.length === 0) {
            loggerError("No products are found", storeName);
            return {
                status: 400,
                message: "Something went wrong or No products were found",
                data: null
            };
        }

        // Fetch store details
        const { data: storeData, error: storeDataError } = await supabase
            .from('stores')
            .select('*')
            .eq('store_name', storeName);

        if (storeDataError || !storeData || storeData.length === 0) {
            loggerError("Store Data not found (Save product details to local DB)", storeName, storeDataError?.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            };
        }

        const storeId = storeData[0].id;
        const productIds = productData.map(product => product.id);

        loggerInfo(`Processing ${productIds.length} products for store: ${storeName}`, storeName);
       

        // Check if we have any products to process
        if (productIds.length === 0) {
            loggerInfo("No product IDs to check against database", storeName);
            return { status: 200, message: "No products to process", data: null };
        }

        // Process products in batches to avoid potential DB query limitations
        const batchSize = 100; // Adjust based on your DB performance
        let existingProductIds = new Set();
        let batchErrors = [];

        for (let i = 0; i < productIds.length; i += batchSize) {
            const batchIds = productIds.slice(i, i + batchSize);
            loggerInfo(`Processing batch ${Math.floor(i/batchSize) + 1} with ${batchIds.length} products`, storeName);
            
            // Modified query to filter by both shopify_gid AND store_id
            const { data: batchExistingProducts, error: batchError } = await supabase
                .from('productstable')
                .select('shopify_gid')
                .in('shopify_gid', batchIds)
                .eq('store_id', storeId); // Add this line to filter by store_id
                
            if (batchError) {
                const errorMsg = `Error fetching batch ${Math.floor(i/batchSize) + 1} of existing products: ${batchError.message}`;
                loggerError(errorMsg, storeName, batchError);
                batchErrors.push(errorMsg);
                continue; // Skip this batch but continue with others
            }
            
            if (batchExistingProducts && batchExistingProducts.length > 0) {
                batchExistingProducts.forEach(p => existingProductIds.add(p.shopify_gid));
            }
            
            loggerInfo(`Found ${batchExistingProducts?.length || 0} existing products in batch ${Math.floor(i/batchSize) + 1}`, storeName);
        }

        if (batchErrors.length > 0 && batchErrors.length === Math.ceil(productIds.length / batchSize)) {
            // All batches failed
            loggerError("All batches failed when fetching existing products", storeName, batchErrors.join('; '));
            return {
                status: 400,
                message: "Failed to fetch existing products from database",
                data: null
            };
        }

        // Filter new products that are not already in the database
        const newProductsToInsert = productData
            .filter(product => !existingProductIds.has(product.id))
            .map(product => ({
                shopify_gid: product.id,
                title: product.title,
                description: product.description || null,
                price: parseFloat(product.variants[0]?.node?.price) || 0, // Get price from first variant
                handle: product.handle,
                tags: product.tags || [],
                vendor: product.vendor,
                store_id: storeId,
                variants: product.variants, // Store variants array
                image_url: product.image_url || null,
                product_url: product.product_url || null,
                created_at: new Date(product.createdAt).toISOString(),
                updated_at: new Date().toISOString(),
            }));

        loggerInfo(`Found ${newProductsToInsert.length} new products to insert`, storeName);

        if (newProductsToInsert.length === 0) {
            loggerInfo("No new products to insert, all products already exist", storeName);
            return { status: 200, message: "No new products to insert", data: null };
        }

        // Insert new products in batches
        const insertBatchSize = 50; // Adjust based on your DB performance
        let insertErrors = [];
        let insertedProducts = [];

        for (let i = 0; i < newProductsToInsert.length; i += insertBatchSize) {
            const batchToInsert = newProductsToInsert.slice(i, i + insertBatchSize);
            loggerInfo(`Inserting batch ${Math.floor(i/insertBatchSize) + 1} with ${batchToInsert.length} products`, storeName);
            
            const { data: insertedBatch, error: insertError } = await supabase
                .from('productstable')
                .insert(batchToInsert)
                .select();

            if (insertError) {
                const errorMsg = `Error inserting batch ${Math.floor(i/insertBatchSize) + 1} of products: ${insertError.message}`;
                loggerError(errorMsg, storeName, insertError);
                insertErrors.push(errorMsg);
                continue; // Skip this batch but continue with others
            }
            
            if (insertedBatch) {
                insertedProducts = insertedProducts.concat(insertedBatch);
            }
            
            loggerInfo(`Successfully inserted batch ${Math.floor(i/insertBatchSize) + 1}`, storeName);
        }

        if (insertErrors.length > 0) {
            if (insertErrors.length === Math.ceil(newProductsToInsert.length / insertBatchSize)) {
                // All insert batches failed
                loggerError("All insert batches failed", storeName, insertErrors.join('; '));
                return { 
                    status: 400, 
                    message: "Failed to save any product details", 
                    data: null 
                };
            } else {
                // Some insert batches failed
                loggerError("Some insert batches failed", storeName, insertErrors.join('; '));
                return { 
                    status: 207, // Partial success
                    message: "Some products were saved successfully, but others failed", 
                    data: insertedProducts 
                };
            }
        }

        loggerInfo(`Successfully saved ${insertedProducts.length} new product details`, storeName);
        return { 
            status: 200, 
            message: "New product details saved successfully", 
            data: insertedProducts 
        };

    } catch (error) {
        loggerError("Something went wrong while saving product details to Local DB", storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        };
    }
};