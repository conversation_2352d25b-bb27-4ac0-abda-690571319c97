import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import pino from "pino";
import { decrypt, gscDecrypt } from "../helper/encryptionAndDecription";
import { loggerError, loggerInfo } from "../helper/loggerHelper";

const logger = pino();

export const fetchSettingsData = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = await shop?.data?.shop?.name;

  loggerInfo("To get the email and shuffle data from DB", storeName);

  // Fetch store data
  const { data: storeData, error: storeDataError } = await supabase
    .from("stores")
    .select("*")
    .eq("store_name", storeName);

  if (storeDataError) {
    loggerError(
      `Something went wrong or store Data not found`,
      storeName,
      storeDataError.message
    );
    return {
      status: 400,
      message: "Something went wrong or store data not found",
      data: null,
    };
  }

  if (!storeData || storeData.length === 0) {
    loggerError("No store data found for", storeName);
    return {
      status: 400,
      message: "Store data not found",
      data: null,
    };
  }

  // Fetch settings data
  const { data: settingsData, error: settingsDataError } = await supabase
    .from("preferences")
    .select("*")
    .eq("store_id", storeData[0].id);

  if (settingsDataError) {
    loggerError(`Something went wrong`, storeName, settingsDataError.message);
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }

  if (!settingsData || settingsData.length === 0) {
    loggerError(`No settings data found for store`, storeName);
    return {
      status: 400,
      message: "Settings data not found",
      data: null,
    };
  }

  loggerInfo("Settings Data fetched successfully", storeName);

  // Fetch GSC data
  const isGSCLoggedIn = storeData[0].is_gsc_logged_in;
  let dscDecryptedData = null;

  try {
    const gscUserData = JSON.parse(storeData[0].gsc_user_data);
    dscDecryptedData = await gscDecrypt(gscUserData);
  } catch (error) {
    loggerError("Error decrypting GSC user data", storeName, error.message);
  }

  // Decrypt GA4 data only if key and IV are present
  let decryptedData = null;
  let parsedDecryptedData = null;
  let combinedAccount = [];
  let combinedAccountId = [];
  let userName = null;
  let userPicture = null;

  if (storeData[0].google_analytics_key && storeData[0].google_encrypted_iv) {
    try {
      decryptedData = await decrypt(
        storeData[0].google_analytics_key,
        storeData[0].google_encrypted_iv
      );
      parsedDecryptedData = JSON.parse(decryptedData);
    } catch (error) {
      loggerError("Error parsing decrypted GA4 data", storeName, error.message);
    }
  } else {
    loggerError("GA4 decryption data missing", storeName);
  }

  const googleConnectCheck = storeData[0].google_is_logged_in;

  // Process GA4 data if available
  const ga4Data = parsedDecryptedData?.ga4;
  if (Array.isArray(ga4Data)) {
    ga4Data.forEach((account) => {
      const accountName = account.accountName;
      const accountId = account.accountId;
      combinedAccount.push(`(${accountName}) - ${accountId}`);
      combinedAccountId.push(accountId);
    });

    userName = parsedDecryptedData?.userProfile?.email || null;
    userPicture = parsedDecryptedData?.userProfile?.picture || null;
  } else {
    loggerError("GA4 data missing or not an array", storeName);
  }

  return {
    status: 200,
    message: "Settings data fetched successfully.",
    data: {
      emailEnabled:
        settingsData[0].email_enabled === true ? "enabled" : "disabled",
      shuffleEnabled: settingsData[0].auto_shuffle_enabled,
      googleConnectCheck,
      userName,
      userPicture,
      combinedAccount,
      combinedAccountId,
      isGSCLoggedIn,
      dscDecryptedData,
      isGSCFeatureEnabled: storeData[0].is_gsc_feature_enabled,
      isMailReportEnabled: storeData[0].is_mail_report_enabled,
      isWeeklyReportEnabled: settingsData[0].weekly_report_enabled
    },
  };
};



export const saveEmailStatus = async (admin, status) => {
  const shop = await shopDetails(admin);
  const storeName = await shop?.data?.shop?.name;
  loggerInfo("Email status change function is initialized.", storeName, status);
  const emailStatus = status === "enabled" ? true : false;
  const { data: storeData, error: storeDataError } = await supabase
    .from("stores")
    .select("*")
    .eq("store_name", storeName);
  if (storeDataError) {
    loggerError(
      "Something went wrong or storeData not found",
      storeName,
      storeDataError.message,
    );
    return {
      status: 400,
      message: "Something went wrong or store data not found.",
      data: null,
    };
  }
  if (storeData && storeData.length > 0) {
    const { data: preferences, error: preferencesError } = await supabase
      .from("preferences")
      .update({ email_enabled: emailStatus })
      .eq("store_id", storeData[0].id)
      .select("*");
    if (preferencesError) {
      loggerError("Something went wrong", storeName, preferencesError.message);
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
    loggerInfo(`Email status successfully updated.`, storeName, status);
    return {
      status: 200,
      message: "Email status successfully updated.",
      data: emailStatus === true ? "enabled" : "disabled",
    };
  }
};

export const saveShuffleStatus = async (admin, value) => {
  const shop = await shopDetails(admin);
  const storeName = await shop?.data?.shop?.name;
  loggerInfo("auto shuffle function initiated.", storeName, value);
  const { data: storeData, error: storeDataError } = await supabase
    .from("stores")
    .select("*")
    .eq("store_name", storeName);
  if (storeDataError) {
    loggerError(
      "Something went wrong or storeData not found",
      storeName,
      storeDataError.message,
    );
    return {
      status: 400,
      message: "Something went wrong or store data not found.",
      data: null,
    };
  }
  if (storeData && storeData.length > 0) {
    const { data: shuffleUpdate, error: shuffleUpdateError } = await supabase
      .from("preferences")
      .update({ auto_shuffle_enabled: value })
      .eq("store_id", storeData[0].id)
      .select("*");
    if (shuffleUpdateError) {
      loggerError(
        "Something went wrong",
        storeName,
        shuffleUpdateError.message,
      );
      return {
        status: 400,
        message: "Something went wrong to update the status",
        data: null,
      };
    }
    loggerInfo("Shuffle status updated successfully.", storeName, value);
    return {
      status: 200,
      message: "Shuffle status updated successfully.",
      data: shuffleUpdate[0].auto_shuffle_enabled,
    };
  }
};

export const selectPropertyId = async (admin, accountId) => {
  const shop = await shopDetails(admin);
  const storeName = await shop?.data?.shop?.name;
  loggerInfo(
    "Google account Property account fetching function is initialized. ",
    storeName,
    accountId,
  );
  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError("Something went wrong", storeName, storeDataError.message);
      return {
        status: 400,
        message: "Something went wrong.",
        data: null,
      };
    }

    if (storeData && storeData.length > 0) {
      const decryptedData = await decrypt(
        storeData[0].google_analytics_key,
        storeData[0].google_encrypted_iv,
      );
      const parsedDecryptedData = JSON.parse(decryptedData);
      const propertyLabel = [];
      let propertyId = [];
      parsedDecryptedData.ga4.forEach((account) => {
        if (account.accountId === accountId) {
          account.properties.forEach((prop) => {
            // Push the property name as label and property ID as value
            propertyLabel.push(`${prop.propertyName} - ${prop.propertyId}`);
            propertyId.push(prop.propertyId);
          });
        }
      });

      loggerInfo(
        "The property value fetched successfully.",
        storeName,
        accountId,
      );
      return {
        status: 200,
        message: "Property Id fetched successfully.",
        data: {
          label: propertyLabel,
          value: propertyId,
        },
      };
    }
  } catch (error) {
    loggerError(
      "Something went wrong to fetch the property value",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};

export const saveGoogleAccount = async (admin, accountId, propertyId) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo("Save google account process starting...", storeName, {
    accountId,
    propertyId,
  });
  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .update({ google_account_id: accountId, google_property_id: propertyId })
      .eq("store_name", storeName)
      .select("*");

    if (storeDataError) {
      loggerError("Something went wrong", storeName, storeDataError.message);
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }

    // Check if storeData is empty
    if (!storeData || storeData.length === 0) {
      logger.warn(`${storeName}: No data found to update.`);
      return {
        status: 404,
        message: "No data found to update",
        data: null,
      };
    }

    const decryptedData = await decrypt(
      storeData[0]?.google_analytics_key,
      storeData[0]?.google_encrypted_iv,
    );

    // Ensure decryptedData is valid before parsing
    if (!decryptedData) {
      loggerError("Decryption failed.", storeName);
      return {
        status: 500,
        message: "Decryption failed",
        data: null,
      };
    }

    const parsedDecryptedData = JSON.parse(decryptedData);
    let accountLabel = [];
    let propertyLabel = [];

    parsedDecryptedData.ga4.forEach((account) => {
      if (account.accountId === accountId) {
        accountLabel.push(`(${account.accountName}) - ${account.accountId}`);
        account.properties.forEach((prop) => {
          if (prop.propertyId === propertyId) {
            propertyLabel.push(`${prop.propertyName} - ${prop.propertyId}`);
          }
        });
      }
    });

    
    loggerInfo("The Google account details saved successfully.", storeName, {
      accountId,
      propertyId,
    });

    return {
      status: 200,
      message: "Successfully updated",
      data: {
        accountLabel,
        accountId,
        propertyLabel,
        propertyId,
      },
    };
  } catch (error) {
    loggerError(
      "Something went wrong to save the google account Details",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};

export const saveWeeklyReportStatus = async (admin, value) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo("Save weekly report status process starting...", storeName, value);
  try{
    const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
    if(storeDataError){
      loggerError("Something went wrong", storeName, storeDataError.message);
      return {
        status: 400,
        message: "Something went wrong",
        data: null
      }
    }
    const {data: preferenceData, error: preferenceError} = await supabase.from('preferences').update({
      weekly_report_enabled: value === 'enabled' ? true: false
    }).eq('store_id', storeData[0].id).select('*');
    if(preferenceError){
      loggerError("Something went wrong", storeName, preferenceError.message);
      return {
        status: 400,
        message: "Something went wrong",
        data: null
      }
    }
    loggerInfo("Weekly report status saved successfully.", storeName, value);
    return {
      status: 200,
      message: "Successfully updated",
      data: preferenceData[0].weekly_report_enabled
    }
  }catch(error){
    loggerError("Something went wrong", storeName, error.message);
    return {
      status: 400,
      message: "Something went wrong",
      data: null
    }
  }
}
