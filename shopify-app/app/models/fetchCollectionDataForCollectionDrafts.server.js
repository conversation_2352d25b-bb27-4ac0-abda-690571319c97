import { shopDetails } from '../helper/shopDetails';
import { supabase } from '../db/supabase_insert_helper';
import { collectionGID } from '../configs/config';
import {loggerInfo, loggerError} from '../helper/loggerHelper'

export const collectionDataFetchForCollectionDrafts = async (admin, page = 1, limit, option, searchValue) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    const storeDomain = shop?.data?.shop?.myshopifyDomain
    loggerInfo(`Data fetch for Collection Drafts (Collection Data Fetch for Collection Drafts)`,storeName,  {searchValue, page, limit, option});
    
    try {
        const { data: storeData, error: storeDataError } = await supabase
            .from('stores')
            .select('*')
            .eq("store_name", storeName);

        if (storeDataError) {
            loggerError('Something went wrong on shop details fetch (Collection Data Fetch for Collection Drafts)',storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            };
        }

        if (storeData && storeData.length > 0) {
            const offset = (page - 1) * limit;

            let query = supabase
                .from('collections')
                .select('*')
                .eq('store_id', storeData[0].id)
                .order('created_at', {ascending: false});

            // Apply the option filter if provided
            if (option && option !== 'all') {
                query = query.eq('status', option);
            }

            // Apply the search filter if a search value is provided
            if (searchValue && searchValue.trim() !== '') {
                query = query.ilike('collection_name', `%${searchValue}%`);
            }

            const { data: collectionData, error: collectionDataError } = await query.range(offset, offset + limit - 1);

            if (collectionDataError) {
                loggerError('Something went wrong to fetch the collection details (Collection Data Fetch for Collection Drafts)',storeName, collectionDataError.message);
                return {
                    status: 400,
                    message: "Something went wrong",
                    data: null
                };
            }

            // Count total collections with the same filters
            let countQuery = supabase
                .from('collections')
                .select('*', { count: 'exact' })
                .eq('store_id', storeData[0].id);

            if (option && option !== 'all') {
                countQuery = countQuery.eq('status', option);
            }

            // Only apply search filter for count if searchValue is not empty
            if (searchValue && searchValue.trim() !== '') {
                countQuery = countQuery.ilike('collection_name', `%${searchValue}%`);
            }

            const { count } = await countQuery;

            for (let collection of collectionData) {
                const collectionGlobalId = `${collectionGID}${collection.collection_id}`
                try {
                  const collectionMetaFieldResponse = await admin.graphql(
                    `
                      {
                        collection(id: "${collectionGlobalId}") {
                          metafields(first: 10) {
                            edges {
                              node {
                                value
                              }
                            }
                          }
                        }
                      }
                    `,
                  );
        
                  const response = await collectionMetaFieldResponse.json();
        
                  // Extract only `node.value` for each metafield
                  const metafieldValues = response?.data?.collection?.metafields?.edges.map(
                    (edge) => edge.node.value,
                  );
                  collection.metafields = metafieldValues; // Append metafield values to each collection
                } catch (error) {
                  loggerError('Error fetching metafields for collection',storeName, error.message);
                  collection.metafields = []; // If there’s an error, assign empty metafields array
                }
              }

            loggerInfo(`Collection data fetched successfully for Collection Drafts (Collection Data Fetch for Collection Drafts)`,storeName,  {searchValue, page, limit, option});
            return {
                status: 200,
                message: "Collection Data Fetched successfully",
                data: { collectionData, totalCount: count, storeDomain: storeDomain,  }
            };
        }

        return {
            status: 404,
            message: "No data found",
            data: null
        };

    } catch (error) {
        loggerError('Something went wrong (Collection Data Fetch for Collection Drafts)',storeName,  error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        };
    }
};