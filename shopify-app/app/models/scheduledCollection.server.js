import { supabase } from "../db/supabase_insert_helper";
import pino from "pino";
import jwt from "jsonwebtoken";
import postmark from "postmark";
import dotenv from "dotenv";
dotenv.config();

const logger = pino();

import {loggerError, loggerInfo} from '../helper/loggerHelper'

export const changeStatusToSchedule = async (token) => {
  const secret = process.env.JWT_SECRET_KEY;
  const decoded = jwt.verify(token, secret);

  const storeName = decoded.storeName;
  const storeUUID = decoded.uuid;
  loggerInfo('Schedule collection function is initialized',storeName,  null)
  try {

    // Fetch store data
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName)
      .eq("uuid", storeUUID);

    if (storeDataError) {
      loggerError('Error fetching store details.',storeName, storeDataError.message);
      return {
        status: 400,
        message: "Error fetching store details",
        data: null,
      };
    }

    if (storeData && storeData.length > 0) {
      // Update collection status
      const { data: collectionData, error: collectionDataError } =
        await supabase
          .from("collections")
          .update({ status: "scheduled" }) // Update method should be used here
          .eq("store_id", storeData[0].id)
          .eq("status", "generated")
          .select("*"); // Ensure the correct records are updated

      if (collectionDataError) {
        loggerError('Error updating collection status.',storeName, collectionDataError.message);
        return {
          status: 400,
          message: "Error updating collection status",
          data: null,
        };
      }

      const client = new postmark.ServerClient(process.env.POST_MARK_API);
      const sendEmailWithTemplate = async () => {
        // Map collection data to get the necessary details for the email
        const collections = collectionData.map((collection, index) => ({
          serial_number: index + 1, // Generate serial number
          collection_name: collection.collection_name || "Untitled Collection",
        }));

        try {
          const emailResponse = await client.sendEmailWithTemplate({
            From: process.env.FROM_EMAIL,
            To: "<EMAIL>",
            templateId: process.env.SCHEDULED_TEMPLATE_ID,
            TemplateModel: {
              collection_count: collections.length,
              collections: collections,
            },
          });

          loggerInfo(`Email sent successfully:, ${emailResponse}`,storeName,  null);
        } catch (emailError) {
          loggerError('Error sending email',storeName, emailError.message);
        }
      };

      // Call the sendEmailWithTemplate function to send the email
      if (collectionData.length > 0) {
        await sendEmailWithTemplate();
      }

      loggerInfo("Collection status changed to scheduled successfully.",storeName,  null);
      return {
        status: 200,
        message: "Collection status changed to scheduled successfully.",
        data: collectionData,
      };
    } else {
      loggerError("No store data found for the given parameters.",storeName, );
      return {
        status: 404,
        message: "No store found with the provided details",
        data: null,
      };
    }
  } catch (err) {
    loggerError('Unexpected error occurred.',storeName, err.message);
    return {
      status: 500,
      message: "Unexpected error occurred",
      data: null,
    };
  }
};
