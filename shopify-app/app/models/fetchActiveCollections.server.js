import {shopDetails} from '../helper/shopDetails'
import { loggerInfo, loggerError } from '../helper/loggerHelper';

export const fetchActiveCollections = async (admin) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    loggerInfo(
      "Fetch All active collections from the Shopify side (Fetch all Collections)",
      storeName
    );
  
    try {
      let allCollections = [];
      let hasNextPage = true;
      let cursor = null;
  
      while (hasNextPage) {
        const collections = await admin.graphql(
          `
          query($cursor: String) {
            collections(first: 10, after: $cursor) {
              nodes {
                id
              }
              pageInfo {
                hasNextPage
                endCursor
              }
            }
          }
          `,
          { variables: { cursor } }
        );
  
        const response = await collections.json();
        
  
        if (response?.data?.collections) {
          allCollections.push(...response.data.collections.nodes);
          hasNextPage = response.data.collections.pageInfo.hasNextPage;
          cursor = response.data.collections.pageInfo.endCursor;
        } else {
          hasNextPage = false;
        }
  

      }
      
      loggerInfo(
        "All collections are fetched successfully from Shopify side (Fetch All Collections)",
        storeName
      );
  
      return {
        status: 200,
        message: "All collections are fetched successfully",
        data: allCollections.length, // Return the full list
      };
    } catch (error) {
      loggerError(
        "Something went wrong while fetching active collections from Shopify (Fetch All Active Collections)",
        storeName,
        error.message
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
  };
  