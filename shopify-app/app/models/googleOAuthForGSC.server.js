import { gscOAuthClient, redirectURL } from "../configs/googleOAuthClient";
import { shopDetails } from "./saveStoreData.server";
import { redirect } from "@remix-run/node";
import axios from "axios";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { encrypt } from "../helper/encryptionAndDecription";
import { supabase } from "../db/supabase_insert_helper";
import { 
  engineBaseUrl, 
  engineAPIVersion, 
  collectionRoute, 
  syncRoute, 
  gscDataSync,
  extractCollection 
} from "../configs/config";
import dotenv from 'dotenv';

dotenv.config();
const appToken = process.env.SHOPIFY_APP_TOKEN;

// Helper Functions
const getRedirectUrl = (storeName, handle) => {
  return `https://admin.shopify.com/store/${storeName}/apps/${handle}/app/settings`;
};

const formatDate = (date) => date.toISOString().split("T")[0];

// Google API Functions
const fetchUserDetails = async (accessToken) => {
  try {
    const response = await axios.get("https://www.googleapis.com/oauth2/v2/userinfo", {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    return {
      name: response.data.name,
      email: response.data.email,
      picture: response.data.picture,
    };
  } catch (error) {
    console.error("Error fetching GSC user details:", error);
    throw new Error("Failed to fetch GSC user details");
  }
};

const fetchVerifiedSites = async (accessToken) => {
  try {
    const response = await axios.get("https://www.googleapis.com/webmasters/v3/sites", {
      headers: { Authorization: `Bearer ${accessToken}` },
    });
    return response.data?.siteEntry?.map((site) => site.siteUrl) || [];
  } catch (error) {
    console.error("Error fetching GSC sites:", error);
    throw new Error("Failed to fetch GSC sites");
  }
};

const fetchSearchAnalytics = async (accessToken, siteURL, startDate, endDate) => {
  try {
    const response = await axios.post(
      `https://www.googleapis.com/webmasters/v3/sites/${encodeURIComponent(siteURL)}/searchAnalytics/query`,
      {
        startDate,
        endDate,
        dimensions: ["query", "page", "country", "device", "date"],
        rowLimit: 20000
      },
      { 
        headers: { 
          Authorization: `Bearer ${accessToken}`, 
          "Content-Type": "application/json" 
        } 
      }
    );

    return response.data.rows?.map((row) => ({
      query: row.keys[0],
      pageUrl: row.keys[1],
      country: row.keys[2],
      device: row.keys[3],
      date: row.keys[4],
      clicks: row.clicks,
      impressions: row.impressions,
      ctr: row.ctr,
      position: row.position
    })) || [];
  } catch (error) {
    console.error("Error fetching GSC data:", error);
    throw new Error("Failed to fetch GSC data");
  }
};

// Database Operations
const saveGSCDataToDatabase = async (storeId, gscData, storeName) => {
  loggerInfo("Saving fetched GSC data", storeName);

  try {
    // Fetch collections for the store
    const { data: collectionData, error: collectionDataError } = await supabase
      .from('collections')
      .select('*')
      .eq('store_id', storeId);

    if (collectionDataError) {
      loggerError("Something went wrong while fetching the collection data", storeName, collectionDataError.message);
      return { status: 400, message: 'Something went wrong', data: null };
    }

    if (!collectionData || collectionData.length === 0) {
      console.warn("No collections found for store", storeId);
      return { status: 400, message: "No collections found", data: null };
    }

    // Process each GSC data row
    for (const dataRow of gscData) {
      try {
        const collectionPath = extractCollectionPath(dataRow.pageUrl);
        
        if (!collectionPath) {
          continue;
        }
        
        // Find a matching collection using the formatted path
        const matchingCollection = collectionData.find(collection => 
          collectionPath === collection.collection_url
        );

        if (matchingCollection) {
          await updateOrInsertGscData(storeId, matchingCollection, dataRow, storeName);
        }
      } catch (error) {
        loggerError(`Error processing GSC data row: ${error.message}`, storeName);
        console.error("Error processing GSC data row:", error);
        continue;
      }
    }

    loggerInfo("Finished processing all GSC data rows");
    return { status: 200, message: 'Successfully saved GSC data', data: null };
  } catch (error) {
    loggerError("Error saving fetched GSC data", storeName, error.message);
    console.error("Unexpected error in saveGSCData:", error);
    return { status: 400, message: "Error saving fetched GSC data", data: null };
  }
};

const extractCollectionPath = (pageUrl) => {
  try {
    const urlObj = new URL(pageUrl);
    const pathname = urlObj.pathname;
    
    if (pathname.includes(extractCollection)) {
      const collectionsIndex = pathname.indexOf(extractCollection);
      const afterCollections = pathname.substring(collectionsIndex + extractCollection.length);
      
      // Extract just the collection handle without any trailing parts
      const nextSlashIndex = afterCollections.indexOf('/');
      const collectionHandle = nextSlashIndex !== -1 
        ? afterCollections.substring(0, nextSlashIndex) 
        : afterCollections;
        
      // Format to match database format: "/collection-handle"
      return `/${collectionHandle}`;
    }
    return null;
  } catch (error) {
    console.error("Error extracting collection path:", error);
    return null;
  }
};

const updateOrInsertGscData = async (storeId, matchingCollection, dataRow, storeName) => {
  const gscDataEntry = {
    store_id: storeId,
    collection_id: matchingCollection.id,
    query: dataRow.query,
    page_url: dataRow.pageUrl,
    country: dataRow.country,
    device: dataRow.device,
    clicks: dataRow.clicks,
    impressions: dataRow.impressions,
    ctr: Math.round(dataRow.ctr * 100),
    position: Math.round(dataRow.position),
    updated_at: new Date().toISOString()
  };

  const { data: existingData, error: existingError } = await supabase
    .from('gscdata')
    .select('id')
    .eq('store_id', storeId)
    .eq('collection_id', matchingCollection.id)
    .eq('query', dataRow.query)
    .eq('page_url', dataRow.pageUrl)
    .eq('country', dataRow.country)
    .eq('device', dataRow.device);
  
  if (existingError) {
    loggerError("Error checking existing GSC data", storeName, existingError.message);
    return;
  }

  if (existingData && existingData.length > 0) {
    const { error: updateError } = await supabase
      .from('gscdata')
      .update(gscDataEntry)
      .eq('id', existingData[0].id);

    if (updateError) {
      loggerError("Error updating GSC data", storeName, updateError.message);
    } else {
      loggerInfo(`Updated GSC data for collection: ${matchingCollection.collection_name}`, storeName);
    }
  } else {
    const { error: insertError } = await supabase
      .from('gscdata')
      .insert(gscDataEntry);

    if (insertError) {
      loggerError("Error inserting GSC data", storeName, insertError.message);
    } else {
      loggerInfo(`Inserted new GSC data for collection: ${matchingCollection.collection_name}`, storeName);
    }
  }
};

// External API Operations
const syncGSCDataToAgent = async (data, storeName) => {
  loggerInfo("Syncing GSC data to Agent", storeName);
  try {
    // For now using webhook.site as mentioned in the original code
    const endPoint = 'https://webhook.site/e7e7a55b-5b63-43ac-9cd9-39188a9db526';
    // const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${gscDataSync}`
    
    // Uncomment for actual implementation with Proactive API
    /*
    const {data: storeData, error: storeDataError} = await supabase
      .from('stores')
      .select('*')
      .eq('store_name', storeName);
      
    if(storeDataError){
      loggerError(`Something went wrong or store Data not found`, storeName, storeDataError.message);
      return {
        status: 400,
        message: "Something went wrong or store data not found",
        data: null
      }
    }
    
    const engineAPIToken = storeData[0].engine_api_token;
    const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${gscDataSync}`;
    
    const response = await axios.post(endPoint, data, {
      headers: {
        "X-PROACTIVE-TOKEN": engineAPIToken,
        "X-BINCHA-APP-TOKEN": appToken
      }
    });
    */
    
    const response = await axios.post(endPoint, data);
    
    loggerInfo("GSC data is synced to agent successfully", storeName);
    return {
      status: 200,
      message: "GSC data is synced to agent successfully",
      data: null
    };
  } catch(error) {
    loggerError("Something went wrong to send the GSC Data to agent", storeName, error.message);
    return {
      status: 400,
      message: "Something went wrong to send the GSC Data to agent",
      data: null
    };
  }
};

// Main Public Functions
export const googleOAuthForGSC = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  const appHandle = shop?.data?.app?.handle;
  const storeDomain = shop?.data?.shop?.myshopifyDomain;

  // const storeName = 'cassataa';
  // const appHandle = 'test-collection';
  // const storeDomain = 'cassataa.com';

  loggerInfo("Google OAuth for GSC function is initialized", storeName, null);

  try {
    const scope = [
      "https://www.googleapis.com/auth/webmasters.readonly",
      "https://www.googleapis.com/auth/userinfo.profile",
      "https://www.googleapis.com/auth/userinfo.email"
    ];
    const state = JSON.stringify({ 
      shop: storeName, 
      handle: appHandle, 
      storeDomain: storeDomain 
    });

    const authUrl = gscOAuthClient.generateAuthUrl({
      access_type: "offline",
      scope: scope,
      state: state,
      include_granted_scopes: true,
      redirect_uri: redirectURL,
      prompt: "consent",
      response_type: "code",
    });

    loggerInfo(
      "Google OAuth for GSC function successfully navigated to the Consent screen",
      storeName
    );

    return {
      status: 200,
      message: "Successfully navigated to the Consent screen",
      data: authUrl,
    };
  } catch (error) {
    loggerError("Something went wrong", storeName, error.message);
    console.error("OAuth Error:", error);
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};

export const handleGoogleCallbackForGSC = async (request) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  const parsedState = JSON.parse(state);
  const storeDomain = parsedState.storeDomain;
  const redirectUrl = getRedirectUrl(parsedState.shop, parsedState.handle);

  // const parsedState = {
  //   shop: 'cassataa',
  //   handle: 'test-collection',
  // }
  // const storeDomain = 'cassataa.com';
  
  try {
    if (!code || !parsedState.shop || !parsedState.handle) {
      console.error("OAuth Callback Error: Missing parameters");
      return redirect(redirectUrl);
      // return redirect (`https://admin.shopify.com/store/binary-dev-store/apps/${parsedState.handle}/app/settings`);
    }

    // Get tokens from OAuth code
    const { tokens } = await gscOAuthClient.getToken(code);
    gscOAuthClient.setCredentials(tokens);

    // Get user details and save to database
    const userDetails = await fetchUserDetails(tokens.access_token);
    const userWithTokens = {
      ...userDetails,
      access_token: tokens.access_token,
      refresh_token: tokens.refresh_token,
    };
    
    const encryptedData = encrypt(JSON.stringify(userWithTokens));

    const { data: storeData, error: storeDataError } = await supabase
      .from('stores')
      .update({
        gsc_user_data: encryptedData, 
        is_gsc_logged_in: true
      })
      .eq('store_name', parsedState.shop)
      .select('*');
      
    if (storeDataError) {
      loggerError("Something went wrong or store data not found", parsedState.shop, storeDataError.message);
    }

    // Find the matching GSC site for this store
    const sites = await fetchVerifiedSites(tokens.access_token);
    const matchingSite = sites.find((site) => site.includes(storeDomain));

    if (!matchingSite) {
      console.warn(`No matching GSC site found for store domain: ${storeDomain}`);
      return redirect(redirectUrl);
      // return redirect (`https://admin.shopify.com/store/binary-dev-store/apps/${parsedState.handle}/app/settings`);
    }

    // Get GSC data for the last 30 days
    const endDate = new Date();
    endDate.setDate(endDate.getDate() - 1); // Yesterday
    
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);
    
    const gscData = await fetchSearchAnalytics(
      tokens.access_token, 
      matchingSite, 
      formatDate(startDate), 
      formatDate(endDate)
    );

    // Save the GSC data to database
    await saveGSCDataToDatabase(storeData[0].id, gscData, parsedState.shop);
    
    // Sync GSC data to agent
    await syncGSCDataToAgent(gscData, parsedState.shop);
    
    return redirect(redirectUrl);
    // return redirect (`https://admin.shopify.com/store/binary-dev-store/apps/${parsedState.handle}/app/settings`);
  } catch (error) {
    console.error("OAuth Callback Error:", error);
    return redirect(redirectUrl);
    // return redirect (`https://admin.shopify.com/store/binary-dev-store/apps/${parsedState.handle}/app/settings`);
  }
};
