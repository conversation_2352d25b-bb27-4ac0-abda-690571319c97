import { shopDetails } from "../helper/shopDetails";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { supabase } from "../db/supabase_insert_helper";
import { collectionGID } from "../configs/config";

export const updateCollectionSortingValue = async (
  admin,
  id,
  option,
  repositionedProducts = null,
) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;

  loggerInfo(
    "Updating the collection sort value to DB and Online store",
    storeName,
    { id, option, hasRepositionedProducts: !!repositionedProducts },
  );

  try {
    // Update collection sort value in database
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);

    if (storeDataError) {
      loggerError(
        "Store data not found or something went wrong (Update the sorting value)",
        storeName,
        storeDataError.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }

    const { data: collectionData, error: collectionDataError } = await supabase
      .from("collections")
      .update({ sorting_value: option })
      .eq("id", id)
      .eq("store_id", storeData[0].id)
      .select("*");

    if (collectionDataError) {
      loggerError(
        "Something went wrong to update the sorting value in DB",
        storeName,
        collectionDataError.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }

    const collectionId = `${collectionGID}${collectionData[0].collection_id}`;

    // First update collection sort order to MANUAL
    const sortUpdate = await admin.graphql(
      `
        mutation updateCollectionrules($input: CollectionInput!){
          collectionUpdate(input: $input){
            userErrors{
              field
              message
            }
          }
        }
      `,
      {
        variables: {
          input: {
            id: collectionId,
            sortOrder: option,
          },
        },
      },
    );

    const response = await sortUpdate.json();
    const userError = response?.data?.collectionUpdate?.collection?.userErrors;

    if (userError?.length > 0) {
      loggerError(
        "Something went wrong to update the sort order in Online store",
        storeName,
        userError.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }

    // Then handle manual sorting with repositioned products
    if (option === "MANUAL" && repositionedProducts?.length > 0) {
      loggerInfo("Updating product positions for manual sort", storeName, {
        collectionId,
        productsCount: repositionedProducts.length,
      });

      const moves = repositionedProducts.map((product) => ({
        id: product.id,
        newPosition: String(product.position),
      }));

      const result = await admin.graphql(
        `
          mutation collectionReorderProducts($id: ID!, $moves: [MoveInput!]!) {
            collectionReorderProducts(id: $id, moves: $moves) {
              job {
                id
              }
              userErrors {
                field
                message
              }
            }
          }
        `,
        {
          variables: {
            id: collectionId,
            moves: moves,
          },
        },
      );

      const reorderResponse = await result.json();
      const userErrors =
        reorderResponse?.data?.collectionReorderProducts?.userErrors;

      if (userErrors?.length > 0) {
        loggerError(
          "Error updating product positions for manual sort",
          storeName,
          { errors: userErrors },
        );
        return {
          status: 400,
          message: "Failed to update product positions",
          data: null,
        };
      }
    }

    loggerInfo(
      "Successfully update the sort order to DB and Online store",
      storeName,
      { id, option },
    );

    return {
      status: 200,
      message: "Sort order updated successfully",
      data: option,
    };
  } catch (error) {
    loggerError(
      "Something went wrong to update the sortinga value to DB and Online store",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
