import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import {loggerInfo, loggerError} from '../helper/loggerHelper'
import { addDaysToCurrentTime } from "../helper/formatDateAndTime";

export const checkCollectionLimit = async (admin) => {
    const shop = await shopDetails(admin)
    const storeName = shop?.data?.shop?.name;
    loggerInfo("Fetch the collection limit function is initialized", storeName)
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError("Store data not found (Collection Limit Check)", storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        const collectionCount = storeData[0].collection_count;
        let generatedCollectionCount = storeData[0].collection_generated_count 
        let remainingCounts = collectionCount - generatedCollectionCount;
        const planExpiresAt = storeData[0].plan_expires_at;
        const currentDate = new Date();
        if(currentDate >= planExpiresAt){
            loggerInfo("Plan is updated", storeName);
            const nextExpireDate = addDaysToCurrentTime(currentDate, 30);
            const {data: updatePlanStatus, error: updatePlanStatusError} = await supabase.from('stores').update({collection_generated_count: null, plan_expires_at: nextExpireDate}).eq('store_name', storeName).select('*');
            if(updatePlanStatusError){
                loggerError("Something went wrong to update the plan", storeName, updatePlanStatusError.message);
                return {
                    status: 400,
                    message: "Something went wrong",
                    data: null
                }
            }
            generatedCollectionCount = updatePlanStatus[0].collection_generated_count;
        }
        remainingCounts = collectionCount - generatedCollectionCount;
        loggerInfo("Collection limit is fetched successfully", storeName);
        return {
            status: 200,
            message: "Collection limit fetche successfully",
            data: remainingCounts
        }
    }catch(error){
        loggerError("Something went wrong to fetch the collection Limit", storeName)
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}