import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { shopDetails } from "./saveStoreData.server";
import { collectionRemoveProduct } from "../helper/collectionRemoveProduct";
import { supabase } from "../db/supabase_insert_helper";
import { removeGid } from "../helper/removeGID";
import { collectionGID } from "../configs/config";

export const productDeleteFromCollection = async (
  admin,
  collectionId,
  productId,
) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo("Product delete from collection using query", storeName, {
    collectionId,
    productId,
  });

  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError("Store Data not found", storeName, storeDataError.message);
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
    const id = removeGid(productId);
    const { data: collectionData, error: collectionDataError } = await supabase
      .from("collections")
      .select("*")
      .eq("store_id", storeData[0].id)
      .eq("id", collectionId);
    if (collectionDataError) {
      loggerError(
        "Something went wrong to fetch the collection data (Delete product from collection DB)",
        storeName,
        collectionDataError.message,
      );
      return {
        status: 400,
        message: "something went wrong",
        data: null,
      };
    }
    if (collectionData && collectionData.length > 0) {
      const collectionShopifyId = collectionData[0].collection_id;

      let productDetails = collectionData[0].product_details;
      productDetails = productDetails.filter((product) => product.id !== id);

      const updatedAt = new Date().toISOString();

      const { data: productUpdate, error: productUpdateError } = await supabase
        .from("collections")
        .update({
          product_details: productDetails,
          updated_at: updatedAt, // Set the current timestamp
        })
        .eq("store_id", storeData[0].id)
        .eq("id", collectionId).select("*");
      if (productUpdateError) {
        loggerError(
          "Something went wrong to delete the product from the collection ",
          storeName,
          productUpdateError.message,
        );
        return {
          status: 400,
          message: "Something went wrong",
          data: null,
        };
      }
      if (collectionShopifyId !== null) {
        const deleteProduct = await collectionRemoveProduct(
          admin,
          `${collectionGID}${collectionShopifyId}`,
          productId,
        );
        if (deleteProduct.status === 400) {
          return {
            status: deleteProduct.status,
            message: deleteProduct.message,
            data: null,
          };
        }
      }
      const updatedTime = productUpdate[0].updated_at
      loggerInfo("Product deleted successfully from DB", storeName, {
        productId,
        collectionId,
      });
      return {
        status: 200,
        message: "Product deleted from DB successfully",
        data: {
          id,
          productId,
          collectionId,
           updatedTime 
        },
      };
    }
  } catch (error) {
    loggerError(
      "Something went wrong to delete the product from the Collection",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
