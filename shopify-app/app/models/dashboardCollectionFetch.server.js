import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import moment from "moment";
import { loggerError, loggerInfo } from "../helper/loggerHelper";

export const dashboardCollectionFetch = async (admin, date) => {
  try {
    const shopData = await shopDetails(admin);
    const storeName = shopData?.data?.shop?.name;

    loggerInfo(`Fetch collection details for dashboard.`,storeName, date);

    // Fetch the store details
    const { data, error } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);

    if (error) {
      loggerError('Something went wrong. The store details are not available.',storeName,
        error.message
      );
      return {
        status: 400,
        message: "Something went wrong. The store details are not available.",
        data: null,
      };
    }

    if (data && data.length > 0) {
      const storeId = data[0].id;
      let startDate, endDate;

      switch (date) {
        case "today":
          startDate = moment().startOf("day").toISOString();
          endDate = moment().endOf("day").toISOString();
          break;
        case "thisWeek":
          startDate = moment().startOf("week").toISOString();
          endDate = moment().endOf("week").toISOString();
          break;
        case "thisMonth":
          startDate = moment().startOf("month").toISOString();
          endDate = moment().endOf("month").toISOString();
          break;
        case "thisYear":
          startDate = moment().startOf("year").toISOString();
          endDate = moment().endOf("year").toISOString();
          break;
        case "overall":
          // No date filtering for overall
          startDate = null;
          endDate = null;
          break;
        default:
          startDate = moment().startOf("day").toISOString();
          endDate = moment().endOf("day").toISOString();
      }

      let collectionQuery = supabase
        .from("collections")
        .select("*")
        .eq("store_id", storeId);

      if (startDate && endDate) {
        collectionQuery = collectionQuery
          .gte("generated_time", startDate)
          .lte("generated_time", endDate);
      }

      const { data: collectionData, error: collectionError } =
        await collectionQuery;

      if (collectionError) {
        loggerError('Something went wrong. Collection details are not available.',storeName,
          collectionError.message
        );
        return {
          status: 400,
          message:
            "Something went wrong. Collection details are not available.",
          data: null,
        };
      } else {
        loggerInfo("Collection details are fetched successfully.",storeName,  date);

        return {
          status: 200,
          message: "Collection details are fetched successfully.",
          data: {
            collectionData: collectionData,
            storeDomain: data[0].store_url,
          },
        };
      }
    } else {
      loggerError("No store data found.",storeName);
      return {
        status: 400,
        message: "No store data found.",
        data: null,
      };
    }
  } catch (error) {
    loggerError('An unexpected error occurred while fetching collection details.',storeName,
      error.message
    );
    return {
      status: 500,
      message: "An unexpected error occurred. Please try again later.",
      data: null,
    };
  }
};
