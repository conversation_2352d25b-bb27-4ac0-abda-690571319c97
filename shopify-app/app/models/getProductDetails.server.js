import { shopDetails } from "./saveStoreData.server";
import { definition, pageCreate } from "../configs/metafieldDefinition";
import { loggerInfo, loggerError } from "../helper/loggerHelper";



export const getProductDetails = async (admin, from) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    loggerInfo(`Get product details function is called (To get Product details`,storeName);

    let allProducts = [];
    let productCursor = null;

    try {
        do {
            const productDetails = await admin.graphql(`
                query GetProducts($productCursor: String) {
                    products(first: 10, after: $productCursor) {
                        edges {
                            cursor
                            node {
                                id
                                title
                                handle
                                priceRangeV2{
                                    minVariantPrice{
                                        amount
                                    }
                                }
                                totalInventory
                                tags
                                vendor
                                description
                                createdAt
                                onlineStorePreviewUrl
                                media(first:1){
                                  edges{
                                    node{
                                      preview{
                                        image{
                                          url
                                        }
                                      }
                                    }
                                  }
                                }
                                variants(first: 250) {
                                    edges {
                                        cursor
                                        node {
                                            id
                                            title
                                            price
                                            position
                                            displayName

                                            
                                        }
                                    }
                                    pageInfo {
                                        hasNextPage
                                        endCursor
                                    }
                                }
                            }
                        }
                        pageInfo {
                            hasNextPage
                            endCursor
                        }
                    }
                }`, { variables: { productCursor } });

            const response = await productDetails.json();
            const products = response.data.products;

            // Process each product and its variants
            for (const product of products.edges) {
                const productNode = product.node;
                const product_url = productNode.onlineStorePreviewUrl
                const image_url = productNode.media.edges.length > 0
                    ? productNode.media.edges[0].node.preview.image.url
                    : null;
                let allVariants = productNode.variants.edges; // Start with the fetched variants

                // If there are more variants, handle pagination for them
                let variantCursor = productNode.variants.pageInfo.hasNextPage ? productNode.variants.pageInfo.endCursor : null;

                while (variantCursor) {
                    const variantDetails = await admin.graphql(`
                        query GetVariants($variantCursor: String, $productId: ID!) {
                            product(id: $productId) {
                                variants(first: 10, after: $variantCursor) {
                                    edges {
                                        cursor
                                        node {
                                            id
                                            title
                                            price
                                            position
                                            displayName
                                        }
                                    }
                                    pageInfo {
                                        hasNextPage
                                        endCursor
                                    }
                                }
                            }
                        }`, { variables: { variantCursor, productId: productNode.id } });

                    const variantResponse = await variantDetails.json();
                    const variants = variantResponse.data.product.variants;

                    allVariants = allVariants.concat(variants.edges);
                    variantCursor = variants.pageInfo.hasNextPage ? variants.pageInfo.endCursor : null;
                }

                const {media, onlineStorePreviewUrl, ...restProductNode} = productNode
                // Add the complete product object with all its variants to the allProducts array
                allProducts.push({
                    ...restProductNode,
                    variants: allVariants,
                    product_url,
                    image_url
                });
            }

            // Update the cursor for the next iteration.
            productCursor = products.pageInfo.hasNextPage ? products.pageInfo.endCursor : null;

        } while (productCursor);
        loggerInfo("Products fetched successfully",storeName,  null)
        return {
            status: 200,
            message: 'Product details retrieved successfully',
            data: allProducts,
        };
        
    } catch (error) {
        loggerError(`Get the product details (To get the product details)`,storeName, error.message, );
        return {
            status: 400,
            message: 'Something went wrong',
            data: null,
        };
    }
}

export const collectionMetafieldCreate = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;

  try {
    // Step 1: Check for existing metafield definitions
    const existingDefinitionsResponse = await admin.graphql(
      `
        query {
          metafieldDefinitions(first: 10, ownerType: COLLECTION) {
            edges {
              node {
                name
              }
            }
          }
        }
      `,
    );

    // Parse the response if needed
    const existingDefinitions = await existingDefinitionsResponse.json();
    

    // Check if any definitions exist
    const definitionExists =
      existingDefinitions.data?.metafieldDefinitions?.edges?.some(
        (edge) => edge.node.name === definition.name,
      ) || false; // Default to false if edges is undefined

    // Step 2: Create the metafield definition only if it doesn't exist
    if (!definitionExists) {
      const createResponse = await admin.graphql(
        `
          mutation CreateMetafieldDefinition($definition: MetafieldDefinitionInput!) {
            metafieldDefinitionCreate(definition: $definition) {
              createdDefinition {
                id
                name
              }
              userErrors {
                field
                message
                code
              }
            }
          }
        `,
        {
          variables: {
            definition: {
              name: definition.name,
              namespace: definition.namespace,
              key: definition.key,
              description: definition.description,
              type: definition.type,
              ownerType: definition.ownerType,
            },
          },
        },
      );

      // Parse the creation response
      const createdDefinition = await createResponse.json();
      const userErrors =
        createdDefinition.data?.metafieldDefinitionCreate?.createdDefinition
          .userErrors;

      if (userErrors && userErrors.length > 0) {
        loggerError(
          `User Errors during metafield creation`,storeName, userErrors.message,  
        );
      } else {
        const metafieldId =
          createdDefinition.data.metafieldDefinitionCreate.createdDefinition.id;

        // Step 3: Pin the Metafield Definition
        const pinResponse = await admin.graphql(
          `
          mutation metafieldDefinitionPin($definitionId: ID!) {
            metafieldDefinitionPin(definitionId: $definitionId) {
              pinnedDefinition {
                name
                key
                namespace
                pinnedPosition
              }
              userErrors {
                field
                message
              }
            }
          }`,
          {
            variables: { definitionId: metafieldId },
          },
        );

        // Parse the pinning response
        const pinUserErrors = await pinResponse.json();
        const pinErrors = pinUserErrors.data?.metafieldDefinitionPin.userErrors;

        if (pinErrors && pinErrors.length > 0) {
          loggerError(
            `Errors during metafield pinning`,storeName, pinErrors.message,  
          );
        } else {
          loggerInfo(
            `Collection Metafield Definition created and pinned successfully.`,storeName
          );
        }
      }
    } else {
      loggerInfo(
        `Collection Metafield Definition already exists. (Skipping creation.)`,storeName,  null
      );
    }
  } catch (error) {
    loggerError(
      `Something went wrong (Metafield Definition Creation)`,storeName, error.message, 
    );
  }
};



