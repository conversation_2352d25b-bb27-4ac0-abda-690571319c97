import { google } from "googleapis";
import dotenv from "dotenv";
import { shopDetails } from "./saveStoreData.server";
import { redirect } from "@remix-run/node";
import { supabase } from "../db/supabase_insert_helper";
import jwt from "jsonwebtoken";
import pino from "pino";
import axios from "axios";
import { encrypt } from "../helper/encryptionAndDecription";
import { loggerError, loggerInfo } from "../helper/loggerHelper";

dotenv.config();
const appUrl = process.env.SHOPIFY_APP_URL

export const getGoogleOauth = async (request, admin) => {
  const shop = await shopDetails(admin);

  if (!shop) {
    return {
      status: 400,
      message: "Shop details not found.",
      data: null,
    };
  }

  const storeName = shop.data.shop.name;
  const appHandle = shop.data.app.handle;
  const redirect_url =
    `${appUrl}/google-callback`;

  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    redirect_url, // Use static redirect URI
  );

  const scopes = [
    "https://www.googleapis.com/auth/userinfo.profile",
    "https://www.googleapis.com/auth/userinfo.email",
    "https://www.googleapis.com/auth/analytics.readonly",
    "https://www.googleapis.com/auth/analytics",
    "https://www.googleapis.com/auth/analytics.manage.users.readonly",
  ];

  const authUrl = oauth2Client.generateAuthUrl({
    access_type: "offline",
    scope: scopes,
    include_granted_scopes: true,
    response_type: "code",
    prompt: "consent",
    // Pass state with dynamic parameters
    redirect_uri: redirect_url,
    state: JSON.stringify({ shop: storeName, handle: appHandle }),
  });

  return {
    status: 200,
    message: "Auth URL is generated.",
    data: authUrl,
  };
};

export const handleGoogleCallback = async (request) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  let parsedState;
  parsedState = JSON.parse(state);

  if (!code || !state) {
    // return {
    //     status: 400,
    //     message: "Authorization code or state is missing.",
    //     data: null,
    // };
    return redirect(
      `https://admin.shopify.com/store/${parsedState.shop}/apps/${parsedState.handle}/app/settings`,
    );
  }

  try {
  } catch (e) {
    loggerError("State Parameter is invalid", parsedState.shop, e.message)
    return {
      status: 400,
      message: "Invalid state parameter.",
      data: null,
    };
  }

  const redirect_url =
    `${appUrl}/google-callback`; // Update with your actual redirect URL

  const oauth2Client = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    redirect_url,
  );

  try {
    const { tokens } = await oauth2Client.getToken(code);
    oauth2Client.setCredentials(tokens);

    const userInfoRes = await axios.get(
      "https://www.googleapis.com/oauth2/v2/userinfo",
      {
        headers: {
          Authorization: `Bearer ${tokens.access_token}`,
        },
      },
    );
    const userProfile = userInfoRes.data;
    loggerInfo("The userProfile is", parsedState.shop);

    // Set up Google Analytics API for UA
    const analytics = google.analytics({
      version: "v3",
      auth: oauth2Client,
    });

    // Fetch UA Accounts
    const uaAccountsRes = await analytics.management.accounts.list();
    const uaAccounts = uaAccountsRes.data.items || [];

    const uaData = [];

    if (uaAccounts.length > 0) {
      for (const account of uaAccounts) {
        const accountId = account.id;

        const accountData = {
          accountId,
          accountName: account.name,
          properties: [],
        };

        // Get properties for this UA account
        const propertiesRes = await analytics.management.webproperties.list({
          accountId: accountId,
        });
        const properties = propertiesRes.data.items || [];

        if (properties.length > 0) {
          for (const property of properties) {
            const propertyId = property.id;

            accountData.properties.push({
              propertyId,
              propertyName: property.name,
            });
          }
        } else {
          loggerInfo(`No UA properties found for account ID ${accountId}.`, parsedState.shop);
        }

        uaData.push(accountData);
      }
    } else {
      loggerInfo("No UA accounts found for this user.", parsedState.shop);
    }

    // Set up Google Analytics Admin API for GA4
    const analyticsAdmin = google.analyticsadmin({
      version: "v1beta",
      auth: oauth2Client,
    });

    // Fetch GA4 Accounts
    const ga4AccountsRes = await analyticsAdmin.accounts.list();
    const ga4Accounts = ga4AccountsRes.data.accounts || [];

    const ga4Data = [];

    if (ga4Accounts.length > 0) {
      for (const ga4Account of ga4Accounts) {
        const ga4AccountId = ga4Account.name.split("/")[1]; // Extract GA4 account ID

        const accountData = {
          accountId: ga4AccountId,
          accountName: ga4Account.displayName,
          properties: [],
        };

        // Get GA4 properties for this account
        const propertiesRes = await analyticsAdmin.properties.list({
          filter: `parent:accounts/${ga4AccountId}`,
        });
        const properties = propertiesRes.data.properties || [];

        if (properties.length > 0) {
          for (const property of properties) {
            const propertyId = property.name.split("/")[1]; // Extract property ID
            let measurementId = property.measurementId;

            // If measurementId is undefined, try to fetch it
            if (!measurementId) {
              try {
                const dataStreamRes =
                  await analyticsAdmin.properties.dataStreams.list({
                    parent: `properties/${propertyId}`,
                  });
                const webStream = dataStreamRes.data.dataStreams.find(
                  (stream) => stream.type === "WEB_DATA_STREAM",
                );
                if (webStream) {
                  measurementId = webStream.webStreamData.measurementId;
                }
              } catch (error) {
                loggerError(
                  `Error fetching measurement ID for property ${propertyId}:`, parsedState.shop,
                  error.message,
                );
              }
            }

            accountData.properties.push({
              propertyId,
              propertyName: property.displayName,
              measurementId: measurementId || null,
            });
          }
        } else {
          loggerInfo(
            `No GA4 properties found for account ID ${ga4AccountId}.`,parsedState.shop
          );
        }

        ga4Data.push(accountData);
      }
    } else {
      loggerInfo("No GA4 accounts found for this user.", parsedState.shop);
    }

    // Prepare the response data
    const responseData = {
      userProfile,
      ua: uaData,
      ga4: ga4Data,
      accessToken: tokens.access_token,
      refreshToken: tokens.refresh_token,
    };
  
    const dataToEncrypt = JSON.stringify(responseData);

    // Encrypt the data
    const encryptedData = encrypt(dataToEncrypt);

    // Store to the database
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores") // Replace with your table name
      .update({
        google_is_logged_in: true,
        google_analytics_key: encryptedData.content, // Store the encrypted content
        google_encrypted_iv: encryptedData.iv, // Store the IV
      })
      .eq("store_name", parsedState.shop); // Update the correct row based on your logic

    if (storeDataError) {
      loggerError("Error storing data:",parsedState.shop, storeDataError.message);
      return {
        status: 400,
        message: "Failed to store data",
        data: null,
      };
    }
    loggerInfo("Data stored successfully", parsedState.shop);

    // Redirect after processing
    return redirect(
      `https://admin.shopify.com/store/${parsedState.shop}/apps/${parsedState.handle}/app/settings`,
    );
  } catch (error) {
    loggerError(
      "Error exchanging code for tokens or fetching analytics data:",parsedState.shop,
      error.message,
    );
    return {
      status: 500,
      message: "Failed to exchange code for tokens or fetch analytics data.",
      data: null,
    };
  }
};
