import { supabase } from "../db/supabase_insert_helper";
import { v4 as uuidv4 } from "uuid";

import { loggerInfo, loggerError } from "../helper/loggerHelper";
import { slackAlert } from "./slackAlert.server";
import { welcomeMailSent } from "./welcomeEmailSent.server";

export const shopDetails = async (admin) => {
  let storeName;
  try {
    // Check if admin has graphql method
    if (!admin || typeof admin.graphql !== 'function') {
      loggerError("Invalid admin object provided to shopDetails", null, "admin.graphql is not a function");
      return {
        data: {
          shop: {
            name: "unknown",
            email: "unknown",
            myshopifyDomain: "unknown"
          }
        }
      };
    }

    const response = await admin.graphql(`
      query {
        shop {
          name
          email
          myshopifyDomain
          contactEmail
          ianaTimezone
          billingAddress {
            countryCodeV2
            phone
          }
        }
        app {
          handle
        }
      }
    `);

    const data = await response.json();
    storeName = data?.data?.shop?.name;
    return data;
  } catch (error) {
    loggerError("Failed to fetch shop details", storeName, error.message);
    // Return a default object to prevent further errors
    return {
      data: {
        shop: {
          name: storeName || "unknown",
          email: "unknown",
          myshopifyDomain: "unknown"
        }
      }
    };
  }
};

export const saveStoreData = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  const storeEmail = shop?.data?.shop?.email;
  const storeUrl = shop?.data?.shop?.myshopifyDomain;
  loggerInfo(
    "To save the Store and Preferences Data in database.",
    storeName,
    null,
  );
  try {
    const { data: existStore, error: existStoreError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (existStoreError) {
      loggerError(
        "The store data is not found or something went wrong to fetch the store data from the DB.",
        storeName,
        existStoreError.message,
      );
      return {
        status: 400,
        message:
          "Something went wrong to get the store data from the database.",
        data: null,
      };
    }
    if (existStore.length === 0) {
      const storeDetails = {
        uuid: uuidv4(),
        store_name: storeName,
        store_email: storeEmail,
        store_url: storeUrl,
        onboarding_status: false,
        reinstalled_user: false,
      };
      const { data: saveStore, error: saveStoreError } = await supabase
        .from("stores")
        .insert([storeDetails])
        .select("*");
      if (saveStoreError) {
        loggerError(
          "Something went wrong to store the storeData in database.",
          storeName,
          saveStoreError.message,
        );
        return {
          status: 400,
          message: "Someting went wrong to store the storeData in database.",
          data: null,
        };
      }
      if (saveStore && saveStore.length > 0) {
        const preferencesDetails = {
          uuid: uuidv4(),
          store_id: saveStore[0].id,
          scheduled_status: false,
          email_enabled: false,
          auto_shuffle_enabled: false,
        };
        const { data: prefrences, error: prefrencesError } = await supabase
          .from("preferences")
          .insert([preferencesDetails]);
        if (prefrencesError) {
          loggerError(
            "Something went wront to save the preferences data into preferences table.",
            storeName,
            prefrencesError.message,
          );
          return {
            status: 400,
            message:
              "Something went wrong to save the preferences data into preferences table.",
            data: null,
          };
        }
        const { data: planData, error: planError } = await supabase
          .from("plans")
          .insert({ store_id: saveStore[0].id });
        if (planError) {
          loggerError(
            "something went wrong to insert the store id in the plans table",
            storeName,
            planError.message,
          );
          return {
            status: 400,
            message: "Something went wrong",
            data: null,
          };
        }
      }
      await slackAlert(admin, 'install')
      await welcomeMailSent(admin)
      loggerInfo(
        "The store and preferences table is successfully updated with initial Data.",
        storeName,
        { storeName, storeUrl, storeEmail },
      );
      return {
        status: 200,
        message: "The data hase been updated successfully in Database.",
        data: null,
      };
    } else {
      loggerInfo("The store details are already there.", storeName, {
        storeName,
        storeUrl,
        storeEmail,
      });
      return {
        status: 200,
        message: "The store and preferences datas aleady there.",
        data: null,
      };
    }
  } catch (error) {
    loggerError(
      "Something went wrong to save the store and preferences Data in Database.",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message:
        "Something went wrong to update the store and preferences Data in database.",
      data: null,
    };
  }
};
