import { shopDetails } from "./saveStoreData.server";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import { collectionGID } from "../configs/config";
import { supabase } from "../db/supabase_insert_helper";

export const addProductToSingleCollection = async (
  admin,
  collectionId,
  productId,
) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(
    "Add products to the existing collection ( Collection Details Page )",
    storeName,
    { collectionId, productId },
  );
  try {
    const productIds = JSON.parse(productId);

    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError("Store data not found", storeName, storeDataError.message);
      return {
        status: 400,
        message: "Store Data not found",
        data: null,
      };
    }
    const { data: collectionData, error: collectionDataError } = await supabase
      .from("collections")
      .select("*")
      .eq("store_id", storeData[0].id)
      .eq("id", collectionId);
    if (collectionDataError) {
      loggerError(
        "collection Data not found",
        storeName,
        collectionDataError.message,
      );
      return {
        status: 400,
        message: "Collection Data not found",
        data: null,
      };
    }
    const collectionShopifyId = `${collectionGID}${collectionData[0].collection_id}`;

    let allProducts = [];
    let dbProducts = [];
    let updatedAt;
    const addProduct = await admin.graphql(
      `
            mutation collectionAddProducts($id: ID!, $productIds: [ID!]!){
                collectionAddProducts(id: $id, productIds: $productIds){
                    userErrors{
                        field
                        message
                    }
                }

            }
        `,
      {
        variables: {
          id: collectionShopifyId,
          productIds: productIds,
        },
      },
    );
    const response = await addProduct.json();

    const userError = response?.data?.collectionAddProducts?.userErrors[0];

    if (userError && userError.length > 0) {
      loggerError(
        "Something went wrong on the mutation to add the products to existing collection ( Collection Details Page )",
        storeName,
        userError?.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    } else {
      for (const id of productIds) {
        const productDetails = await admin.graphql(`
                query{
                    product(id: "${id}"){
                        
                                id
                                title
                                handle
                                totalInventory
                                priceRangeV2{
                                    minVariantPrice{
                                        amount
                                    }
                                
                              
                        }
                        media(first:1){
                                    
                                        nodes{
                                            preview{
                                                image{

                                                url
                                                }
                                            }
                                        }
                                    
                                }
                    }
                }
            `);
        const response = await productDetails.json();

        const productResponse = response?.data?.product;

        const formattedProduct = {
          id: productResponse.id,
          title: productResponse.title,

          totalInventory: productResponse.totalInventory,
          price: productResponse.priceRangeV2.minVariantPrice.amount,
          image: productResponse?.media?.nodes[0]?.preview?.image?.url,
        };

        allProducts.push(formattedProduct);

        const formattedForDB = {
          id: productResponse.id,
          name: productResponse.title,
          handle: productResponse.handle,
        };

        // Fetch existing product_details from the collection
        const { data: existingCollection, error: existingError } =
          await supabase
            .from("collections")
            .select("product_details")
            .eq("id", collectionId)
            .single();

        if (existingError) {
          loggerError(
            "Failed to fetch existing collection data",
            storeName,
            existingError.message,
          );
          return {
            status: 400,
            message: "Failed to fetch existing collection data",
            data: null,
          };
        }

        // Ensure product_details is an array (if null, default to an empty array)
        const existingProducts = existingCollection?.product_details || [];

        // Append the new product while avoiding duplicates
        const updatedProductDetails = [...existingProducts, formattedForDB];

        // Update collection with the new product_details array
        const { data: updatedCollection, error: updateError } = await supabase
          .from("collections")
          .update({
            product_details: updatedProductDetails,
            updated_at: new Date().toISOString(),
          })
          .eq("id", collectionId);

        if (updateError) {
          loggerError(
            "Failed to update collection with new product details",
            storeName,
            updateError.message,
          );
          return {
            status: 400,
            message: "Failed to update collection with new product details",
            data: null,
          };
        }
        dbProducts.push(formattedForDB);
        updatedAt = collectionData[0].udated_at;
      }

      loggerInfo(
        "Products added to the existing collection ( Collection Details Page )",
        storeName,
        { collectionId, productId },
      );
      return {
        status: 200,
        message: "Products added to the existing collection",
        data: { allProducts, dbProducts, collectionId, updatedAt },
      };
    }
  } catch (error) {
    loggerError(
      "Something went wrong to add the products to exisiting Collection ( Collection Details Page )",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
