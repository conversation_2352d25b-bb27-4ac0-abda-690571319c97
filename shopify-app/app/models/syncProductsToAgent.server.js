import { supabase } from '../db/supabase_insert_helper';
import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { shopDetails } from './saveStoreData.server';
import axios from 'axios';
import { engineBaseUrl, engineAPIVersion, collectionRoute, syncRoute, syncAllProduct } from '../configs/config';
import dotenv from 'dotenv';

dotenv.config();
const appToken = process.env.BINCHA_APP_TOKEN;

export const productsSyncToAgent = async (admin, productDetails, from) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${syncAllProduct}`;
    loggerInfo("Products Details sync function initialized", storeName, null);

    

    try {
        // Fetch store details
        const { data: storeData, error: storeDataError } = await supabase
            .from('stores')
            .select('*')
            .eq('store_name', storeName);

        if (storeDataError || !storeData.length) {
            loggerError('Store Details not found', storeName, storeDataError?.message);
            return { status: 400, message: "Something went wrong", data: null };
        }

        const engineAPIToken = storeData[0].engine_api_token;
        const parsedProducts = typeof productDetails === 'string' ? JSON.parse(productDetails) : productDetails;
        const productCount = parsedProducts.length;

        

        // Declare updateData outside the conditional block
        let updateData = null;

        if (from !== 'webhook') {
            const { data, error: productCountError } = await supabase
                .from('stores')
                .update({ is_product_synced: true, product_count: productCount })
                .eq('store_name', storeName)
                .select("*");

            if (productCountError) {
                loggerError('Failed to update product count', storeName, productCountError.message);
                return { status: 400, message: "Failed to update product count", data: null };
            }

            updateData = data;
            loggerInfo(`Product count updated to ${productCount} for ${storeName}`, storeName, null);
        }

        // Format product data
        const formattedProducts = parsedProducts.map((product) => ({
            ...product,
            variants: product.variants.map((variant) => ({
                cursor: variant.cursor,
                node: {
                    id: variant.node.id,
                    title: variant.node.title,
                    price: variant.node.price,
                    position: variant.node.position,
                    displayName: variant.node.displayName,
                }
            })),
        }));

        const productsToSend = from === 'webhook' ? parsedProducts : formattedProducts;

        // Send API request
        const productSync = await axios.post(endPoint, productsToSend, {
            headers: {
                'X-PROACTIVE-TOKEN': engineAPIToken,
                'X-BINCHA-APP-TOKEN': appToken
            }
        });

        const response = productSync.data.data.task_id;
        loggerInfo("Product Details synced with Agent Data is successful", storeName, null);

        return {
            status: productSync.data.status_code,
            message: "Product Analysed Started. Once it is done we will send mail to you. Please wait",
            data: {
                isProductSynced: updateData?.[0]?.is_product_synced ?? null,
                task_id: response
            }
        };

    } catch (error) {
        loggerError('Something went wrong with the product sync function', storeName, error.message);
        return { status: 400, message: "Something went wrong", data: null };
    }
};
