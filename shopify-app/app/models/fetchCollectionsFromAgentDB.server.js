import {shopDetails} from '../helper/shopDetails'
import {supabase} from '../db/supabase_insert_helper'
import axios from 'axios'
import dotenv from 'dotenv'
import {loggerInfo, loggerError} from '../helper/loggerHelper'
import {engineBaseUrl, engineAPIVersion,collectionRoute, fetchSubroute,collectionFetch  } from '../configs/config'

dotenv.config();
const appToken = process.env.BINCHA_APP_TOKEN
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${fetchSubroute}${collectionFetch}`

export const fetchCollectionFromAgentDB = async (admin, count) => {
    const shop = await shopDetails(admin)
    const storeName = shop?.data?.shop?.name;
    loggerInfo("Fetch collection from Agent DB function is started", storeName, count)
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError("Store Data Not found (Fetch collections from Agent DB)", storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        
        const apiToken = storeData[0].engine_api_token
        const response = await axios.get(`${endPoint}?count=${count}`, {
            headers: {
                'X-PROACTIVE-TOKEN': apiToken,
                'X-BINCHA-APP-TOKEN': appToken
            }
        })
        loggerInfo("The collections fetched from Agent DB is successfully", storeName, count);
        return {
            status: 200,
            message: "Collection fetched successfully from Agent DB",
            data: response.data
        }
    }catch(error){
        loggerError("Something went wrong to fetch the collections from Agent DB", storeName, error.message)
        return{
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}