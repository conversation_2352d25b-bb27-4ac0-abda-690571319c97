import axios from "axios";
import dotenv from "dotenv";
import { shopDetails } from "../helper/shopDetails";
import { supabase } from "../db/supabase_insert_helper";
import {
  engineBaseUrl,
  engineAPIVersion,
  collectionRoute,
  syncRoute,
  competitorUrlRoute,
} from "../configs/config";
import { loggerInfo, loggerError } from "../helper/loggerHelper";

dotenv.config();
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${competitorUrlRoute}`;
const appToken = process.env.BINCHA_APP_TOKEN;

export const syncCompetitorURL = async (admin, url) => {
  const shop = await shopDetails(admin);

  const storeName = shop?.data?.shop?.name;
  loggerInfo("Sync Competitory url to agent <PERSON>", storeName, { url });
  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);
    if (storeDataError) {
      loggerError(
        "Something went wrong or store data not found",
        storeName,
        storeDataError.message,
      );
      return {
        status: 400,
        message: "Something went wrong or store data not found",
        data: null,
      };
    }
    const parsedUrl = JSON.parse(url);

    const payLoad = {
      competitors_domain: parsedUrl,
    };

    const response = await axios.post(endPoint, payLoad, {
      headers: {
        "X-PROACTIVE-TOKEN": storeData[0].engine_api_token,
        "X-BINCHA-APP-TOKEN": appToken,
      },
    });
    const { data: update, error: updateError } = await supabase
      .from("stores")
      .update({ url_synced: parsedUrl.length > 0 ? true : false })
      .eq("store_name", storeName)
      .select("*");
    if (updateError) {
      loggerError(
        "Something went wrong to update the Local DB for Url synced",
        storeName,
        updateError.message,
      );
      return {
        status: 400,
        message: "Something went wrong",
        data: null,
      };
    }
    loggerInfo("Competitor URL synced with Agent DB Successfully", storeName, {
      url,
    });
    return {
      status: response.data.status_code,
      message: response.data.message,
      data: update[0].url_synced,
    };
  } catch (error) {
    loggerError(
      "Something went wrong to sync the URL to Agent DB",
      storeName,
      error.message,
    );
    return {
      status: 400,
      message: "Something went wrong to Sync the URL to Agent DB",
      data: null,
    };
  }
};
