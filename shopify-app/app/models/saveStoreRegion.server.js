import { shopDetails } from "../helper/shopDetails";
import { supabase } from "../db/supabase_insert_helper";
import {loggerInfo, loggerError} from '../helper/loggerHelper'

export const saveStoreRegion = async (admin, storeType, storeRegion, storeCountry) => {
    const shop = await shopDetails(admin)
    const storeName = shop?.data?.shop?.name;
    loggerInfo("Save stores's type and region in database", storeName, {storeType, storeRegion, storeCountry});
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').update({
            store_type: storeType,
            store_located_region: storeRegion,
            store_located_country: storeCountry
        }).eq('store_name', storeName).select("*")
        if(storeDataError){
            loggerError("Something went wrong or store Data not found", storeName, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong store data not found",
                data: null
            }
        }
        loggerInfo("Store type and store region saved successfully in DB", storeName, {storeType, storeRegion, storeCountry});
        return {
            status: 200,
            message: "Successfully stored the store type and store region",
            data: {
                storeType: storeData[0].store_type,
                storeRegion: storeData[0].store_located_region,
                storeCountry: storeData[0].store_located_country
            }
        }
    }catch(error){
        loggerError("Something went wrong to save the store type and region in database.", storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}