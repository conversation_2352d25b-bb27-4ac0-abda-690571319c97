import { shopDetails } from "./saveStoreData.server";
import { supabase } from "../db/supabase_insert_helper";
import { loggerInfo, loggerError } from "../helper/loggerHelper";
import axios from 'axios'
import dotenv from 'dotenv';
import { engineAPIVersion, engineBaseUrl, collectionRoute, manualCreateionRoute, manualCreationCallback } from "../configs/config";

dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN;
const appUrl = process.env.SHOPIFY_APP_URL
const manualCreationEndpoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${collectionRoute}${manualCreateionRoute}`


export const manualGenerate = async (admin, competitorUrl, attributes) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    loggerInfo("Manual collection creation function is initialized", storeName);
    try{
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError('Store Data not found or somthing went wrong', storeName, storeDataError.message);
            return {
                status: 400,
                message: "Store Data Not found",
                data: null
            }
        }
        const parsedAttributes = JSON.parse(attributes)
        const apiToken = storeData[0].engine_api_token;
        const uuid = storeData[0].uuid
        const callbackUrl = `${appUrl}${manualCreationCallback}/${uuid}`
        
        const payLoad = {
            
            callback_url: callbackUrl,
            is_manual_creation: true,
            keywords_origin_type: competitorUrl,
            attributes: parsedAttributes,
            // enforce_strict_collection_creation: "False"
        }

        
        
        const manualCreation = await axios.post(manualCreationEndpoint, payLoad, {
            headers: {
                "X-PROACTIVE-TOKEN": apiToken,
                "X-BINCHA-APP-TOKEN": appToken
            }
        })
        const {data: collectionJobData, error: collectionJobDataError} = await supabase.from('collection_jobs').insert({
            store_id: storeData[0].id,
            task_id: manualCreation.data.data.task_id,
            attributes: payLoad.attributes,
            task_status: 'started',
            origin: 'app',
            updated_at: new Date().toISOString()
        })
        if(collectionJobDataError){
            loggerError('Something went wrong to create the collection job', storeName, collectionJobDataError.message);
            return {
                status: 400,
                message: 'something went wrong',
                data: null
            }
        }
        loggerInfo("Manual collection creation function is successfully trigger the Agent Proactive API", storeName);
        return {
            status: manualCreation.data.status_code,
            message: manualCreation.data.message,
            data: manualCreation.data.data
        }
    }catch(error){
        loggerError('Something went wrong to create the collections by Manual', storeName, error.message);
        return {
            status: 400,
            message: 'something went wrong',
            data: null
        }
    }
}