import { shopDetails } from "../helper/shopDetails";
import { supabase } from "../db/supabase_insert_helper";
import { collectionGID } from "../configs/config";
import {loggerInfo, loggerError} from '../helper/loggerHelper'

export const individualCollectionUnpublish = async (admin, id) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo(
    `collection Unpublish function is initialized (Collection Unpublish)`,storeName, id
  );

  try {
    // Fetch store details
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", storeName);

    if (storeDataError) {
      loggerError(
        "Something went wrong, store details not found. (Collection Unpublish)",storeName,storeDataError.message
      );
      return { status: 400, message: "Something went wrong", data: null };
    }

    if (storeData?.length > 0) {
      const storeId = storeData[0].id;

      // Fetch collection details
      const { data: collectionData, error: collectionDataError } = await supabase
        .from("collections")
        .select("*")
        .eq("id", id)
        .eq("store_id", storeId);

      if (collectionDataError) {
        loggerError(
          "Failed to fetch collection details (Collection Unpublish)",storeName, collectionDataError.message
        );
        return { status: 400, message: "Something went wrong", data: null };
      }

      if (collectionData?.length > 0) {
        const collectionGlobalId = `${collectionGID}${collectionData[0].collection_id}`;
        
        // Fetch collection details from Shopify
        const findCollectionResponse = await admin.graphql(`
          {
            collection(id: "${collectionGlobalId}") {
              id
              title
            }
          }
        `);
        const findResponse = await findCollectionResponse.json();
        const isCollectionAvailable = findResponse?.data?.collection;

        // If collection is not available, update the database and return
        if (!isCollectionAvailable) {
          const { data: updateData, error: updateError } = await supabase
            .from("collections")
            .update({
              status: "unpublished",
              published_time: null,
            })
            .eq("store_id", storeId)
            .eq("id", id)
            .select();

          if (updateError) {
            loggerError(
              'Failed to update collection status (Collection Unpublish)',storeName, updateError.message
            );
            return { status: 400, message: "Something went wrong", data: null };
          }

          loggerInfo( `Collection unpublished successfully.`,storeName, id);
          return {
            status: 200,
            message: "Unpublished successfully",
            data: {
              status: updateData[0].status,
              publishedAt: updateData[0].published_time,
              id: updateData[0].id,
            },
          };
        }

        // If collection is available, proceed with unpublish mutation
        const publicationsResponse = await admin.graphql(`
          {
            publications(first: 100 catalogType: APP) {
              edges {
                node {
                  id
                  name
                  catalog {
                    title
                  }
                }
              }
            }
          }
        `);
        const publicationResponse = await publicationsResponse.json();
        const onlineStore = publicationResponse.data.publications.edges.find(
          ({ node }) =>
            node?.name.toLowerCase().includes("online store"),
        );
        const publicationId = onlineStore?.node?.id;

        if (!publicationId) {
          loggerError(
           `Online store publication ID not found (Collection Unpublish)`,storeName
          );
          return { status: 400, message: "Something went wrong", data: null };
        }

        const unpublishCollectionResponse = await admin.graphql(
          `#graphql
            mutation publishableUnpublish($id: ID!, $input: [PublicationInput!]!) {
              publishableUnpublish(id: $id, input: $input) {
                publishable {
                  availablePublicationsCount { count }
                  resourcePublicationsCount { count }
                }
                shop { publicationCount }
                userErrors { field, message }
              }
            }`,
          {
            variables: {
              id: collectionGlobalId,
              input: [{ publicationId }],
            },
          },
        );
        const unpublishResponse = await unpublishCollectionResponse.json();
        const unpublishErrors =
          unpublishResponse?.data?.publishableUnpublish?.userErrors;

        if (unpublishErrors?.length > 0) {
          loggerError(
            'Failed to unpublish collection (Collection Unpublish)',storeName, unpublishErrors[0].message,
          );
          return { status: 400, message: "Something went wrong", data: null };
        }

        // Update the collection status in the database
        const { data: updateData, error: updateError } = await supabase
          .from("collections")
          .update({
            status: "unpublished",
            published_time: null,
          })
          .eq("id", id)
          .eq("store_id", storeId)
          .select();

        if (updateError) {
          loggerError(
            'Failed to update collection status (Collection Unpublish)',storeName, updateError.message,
          );
          return { status: 400, message: "Something went wrong", data: null };
        }

        loggerInfo(`Collection unpublished successfully.`,storeName,  id);
        return {
          status: 200,
          message: "Unpublished successfully",
          data: {
            status: updateData[0].status,
            publishedAt: updateData[0].published_time,
            id: updateData[0].id,
          },
        };
      }
    }
  } catch (error) {
    loggerError(
     'An error occurred during unpublish (Collection Unpublish)', storeName, error.message,
    );
    return { status: 400, message: "Something went wrong", data: null };
  }
};
