import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { supabase } from '../db/supabase_insert_helper';


export const mailUnsubscribe = async (storeName, source) => {

  loggerInfo(`Unsubscribe request for store: ${storeName}, source: ${source}`);

  // Map source to preference column
  const sourceColumnMap = {
    generated_mail: 'is_generated_mail_unsubscribed',
    scheduled_mail: 'is_scheduled_mail_unsubscribed',
    published_mail: 'is_published_mail_unsubscribed',
    weekly_mail: 'is_weekly_mail_unsubscribed',
  };

  const columnToUpdate = sourceColumnMap[source];

  if (!columnToUpdate) {
    loggerError(`Invalid source provided: ${source}`, storeName);
    return {
      status: 400,
      message: 'Invalid unsubscribe source provided',
      data: null,
    };
  }

  try {
    // Get the store entry
    const { data: storeData, error: storeDataError } = await supabase
      .from('stores')
      .select('id')
      .eq('store_name', storeName)
      .single();

    if (storeDataError || !storeData) {
      loggerError('Error fetching store data:', storeName, storeDataError?.message);
      return {
        status: 400,
        message: 'Error fetching store data',
        data: null,
      };
    }

    // Update the correct column in preferences table
    const { data: preferencesData, error: preferencesError } = await supabase
      .from('preferences')
      .update({ [columnToUpdate]: true })
      .eq('store_id', storeData.id); // Assuming 'store_id' is the foreign key in preferences

    if (preferencesError) {
      loggerError('Error updating preferences:', storeName, preferencesError.message);
      return {
        status: 400,
        message: 'Error updating preferences',
        data: null,
      };
    }

    return {
      status: 200,
      message: 'Unsubscribed successfully',
      data: preferencesData,
    };
  } catch (error) {
    loggerError('Exception in mailUnsubscribe:', storeName, error.message);
    return {
      status: 400,
      message: 'Exception occurred during unsubscribe',
      data: null,
    };
  }
};
