import { supabase } from '../db/supabase_insert_helper';
import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { removeGid } from '../helper/removeGID';
import { v4 as uuidv4 } from 'uuid';
import { formatHandle } from '../helper/formatHandle';
import { ServerClient } from "postmark";
import { ContactLink, contactEmail } from '../configs/config';
import dotenv from 'dotenv';
import {multiplePublishCollection} from '../helper/mulitplePublishedCollection'

dotenv.config();

const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
const senderMail = process.env.COLLECTION_SENDER_MAIL;
const contactLink = ContactLink;

async function sendCollectionEmail(toEmail, emailData) {
    try {
        const response = await client.sendEmailWithTemplate({
            From: senderMail,
            To: toEmail,
            TemplateId: process.env.GENERATED_COLLECTION_ID,
            TemplateModel: emailData,
            Tag: "CollectionNotification",
            TrackOpens: true,
            TrackLinks: "HtmlAndText"
        });

        loggerInfo("Email sent successfully", response.To);
        return response;
    } catch (error) {
        loggerError("Error sending email", error.message);
        throw error;
    }
}

export const saveFetchedCollection = async (id, data) => {
    try {
        // Fetch store details
        const { data: storeData, error: storeDataError } = await supabase
            .from('stores')
            .select('*')
            .eq('uuid', id);

        if (storeDataError || !storeData.length) {
            loggerError("Store data not found", storeDataError?.message);
            return {
                status: 400,
                message: "Store not found",
                data: null
            };
        }

        const storeId = storeData[0].id;
        const totalCollectionCount = parseInt(storeData[0].collection_count) || 0;
        const generatedCollectionCount = parseInt(storeData[0].collection_generated_count) || 0;

        // Calculate remaining slots
        const remainingSlots = totalCollectionCount - generatedCollectionCount;
        
        if (remainingSlots <= 0) {
            loggerInfo(`Collection limit reached: ${generatedCollectionCount}/${totalCollectionCount}`, storeData[0].store_name);
            return {
                status: 400,
                message: "Collection limit reached",
                data: null
            };
        }

        // Fetch existing products from the store
        const { data: productsData, error: productsDataError } = await supabase
            .from('productstable')
            .select('shopify_gid, title, handle')
            .eq('store_id', storeId);

        if (productsDataError) {
            loggerError("Products data not found", productsDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            };
        }

        // Fetch store preferences
        const { data: preferences, error: preferencesError } = await supabase
            .from('preferences')
            .select('*')
            .eq('store_id', storeId);

        if (preferencesError || !preferences.length) {
            loggerError("Preferences not found", preferencesError?.message);
            return {
                status: 400,
                message: "Preferences not found",
                data: null
            };
        }

        const emailStatus = preferences[0].email_enabled;
        const productMap = new Map(productsData.map(({ shopify_gid, title, handle }) =>
            [removeGid(shopify_gid), { name: title, handle: handle }]
        ));

        if (!Array.isArray(data.collections)) {
            loggerError("Invalid collections data", "Collections data is not an array");
            return {
                status: 400,
                message: "Invalid collections data",
                data: null
            };
        }

        // Only take the number of collections that fit within remaining slots
        const collectionsToProcess = data.collections.slice(0, remainingSlots);
        
        loggerInfo(`Processing ${collectionsToProcess.length} collections out of ${data.collections.length} received (Limit: ${remainingSlots} remaining)`, storeData[0].store_name);

        const collectionsToInsert = collectionsToProcess
    .map(collection => {
        const filteredProducts = collection.products_list
            .map(removeGid)
            .filter(productId => productMap.has(productId))
            .map(productId => ({ id: productId, ...productMap.get(productId) }));

        // Ignore collections with 0 products
        if (filteredProducts.length === 0) {
            loggerInfo(`Skipping collection "${collection.collection_title}" due to 0 products`, storeData[0].store_name);
            return null; // Mark for removal
        }

        return {
            store_id: storeId,
            uuid: uuidv4(),
            collection_name: collection.collection_title,
            collection_url: `/${formatHandle(collection.collection_title)}`,
            product_details: filteredProducts,
            keywords: collection.meta_keywords,
            description: collection.collection_description,
            status: "generated",
            generated_type: 'manual',
            task_id: data.task_id,
        };
    })
    .filter(Boolean); // Remove null values (collections with 0 products)


        // Insert collections
        const { error: insertError } = await supabase
            .from('collections')
            .insert(collectionsToInsert);

        if (insertError) {
            loggerError("Error inserting collections", insertError.message);
            return {
                status: 400,
                message: "Failed to insert collections",
                data: null
            };
        }

        // Update the generated count with only the number of collections actually inserted
        const newGeneratedCount = generatedCollectionCount + collectionsToInsert.length;
        const { error: updateError } = await supabase
            .from('stores')
            .update({ collection_generated_count: newGeneratedCount })
            .eq('uuid', id);

        if (updateError) {
            loggerError("Error updating generated collection count", updateError.message);
            return {
                status: 400,
                message: "Failed to update generated collection count",
                data: null
            };
        }

        const emailData = {
            store_name: storeData[0].store_name,
            collections: collectionsToInsert.map(collection => ({
                name: collection.collection_name
            })),
            ctaUrl: `https://admin.shopify.com/store/${storeData[0].store_name}/apps/rank-collections/app/collection_drafts`,
            supportUrl: contactEmail,
            supportLinkText: contactEmail
        };

        const {data: collectionJobData, error: collectionJobDataError} = await supabase.from('collection_jobs').select('*').eq('task_id', data.task_id).eq('store_id', storeId).eq('origin', 'admin');        
        if(collectionJobDataError){
            loggerError('Something went wrong to update the collection job', storeData[0].store_name, collectionJobDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        if(collectionJobData.length > 0){
            const {data: collectionData, error: collectionDataError} = await supabase.from('collections').select('*').eq('task_id', data.task_id).eq('store_id', storeId).eq('status', 'generated');    
            if(collectionDataError){
                loggerError('Something went wrong to update the collection job', storeData[0].store_name, collectionDataError.message);
                return {
                    status: 400,
                    message: "Something went wrong",
                    data: null
                }
            }
            const publishCollection = await multiplePublishCollection(storeId, collectionData)

        }
        

        try {
            if (emailStatus) {
                await sendCollectionEmail(storeData[0].store_email, emailData);
                loggerInfo("Collection notification email sent", storeData[0].store_name);
            }
        } catch (emailError) {
            loggerError("Failed to send email notification", emailError.message);
        }

        // Add more detailed logging
        loggerInfo(`Updated collection count: ${newGeneratedCount}/${totalCollectionCount} (Inserted ${collectionsToInsert.length} new collections)`, storeData[0].store_name);

        return {
            status: 200,
            message: `Successfully saved ${collectionsToInsert.length} collections`,
            data: {
                insertedCount: collectionsToInsert.length,
                totalCount: newGeneratedCount,
                limit: totalCollectionCount
            }
        };
    } catch (error) {
        loggerError("Unexpected error in saveFetchedCollection", error.message);
        return {
            status: 500,
            message: "Internal server error",
            data: null
        };
    }
};