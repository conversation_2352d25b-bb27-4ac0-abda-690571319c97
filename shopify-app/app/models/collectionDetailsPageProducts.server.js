// import { shopDetails } from "./saveStoreData.server";
// import { loggerError, loggerInfo } from "../helper/loggerHelper";

// export const getProductDetailsForCollectionDetails = async (admin) => {
//   const shop = await shopDetails(admin);
//   const storeName = shop?.data?.shop?.name;
//   loggerInfo("Get product details for collection Details page", storeName);

//   try {
//     let allProducts = [];
//     let hasNextPage = true;
//     let cursor = null; // Initialize cursor

//     while (hasNextPage) {
//       // Construct the GraphQL query with the cursor if it exists
//       const productDetails = await admin.graphql(
//         `
//                 query($cursor: String) {
//                     products(first: 20, after: $cursor) {
//                         edges {
//                             node {
//                                 id
//                                 title
//                                 totalInventory
//                                 handle
//                                 priceRangeV2{
//           minVariantPrice{
//             amount
//           }
//         }
//                                 media(first: 1) {
//                                     edges {
//                                         node {
//                                             preview {
//                                                 image {
//                                                     url
//                                                 }
//                                             }
//                                         }
//                                     }
//                                 }
//                             }
//                         }
//                         pageInfo {
//                             hasNextPage
//                             endCursor
//                         }
//                     }
//                 }
//             `,
//         {
//           variables: { cursor },
//         },
//       );

//       const response = await productDetails.json();

//       // Extract product information and add to allProducts
//       const products = response.data.products.edges.map((edge) => ({
//         id: edge.node.id,
//         title: edge.node.title,
//         handle: edge.node.handle,
//         totalInventory: edge.node.totalInventory,
//         price: edge.node.priceRangeV2.minVariantPrice.amount,
//         image: edge.node.media.edges[0]?.node.preview.image.url || null, // Handle cases where image might not exist
//       }));

//       allProducts = [...allProducts, ...products];

//       // Update cursor and hasNextPage for the next iteration
//       const pageInfo = response.data.products.pageInfo;
//       hasNextPage = pageInfo.hasNextPage;
//       cursor = pageInfo.endCursor; // Set cursor for the next request
//     }
//     loggerInfo(
//       "Product details are fetched successfully for collection Details page",
//       storeName,
//     );
//     return {
//       status: 200,
//       message: "Products fetched successfully",
//       data: allProducts,
//     };
//   } catch (error) {
//     loggerError(
//       "Something went wrong to get the product details for collection Details page",
//       storeName,
//       error.message,
//     );
//     return {
//       status: 400,
//       message: "Something went wrong",
//       data: null,
//     };
//   }
// };

import { shopDetails } from "./saveStoreData.server";
import { loggerError, loggerInfo } from "../helper/loggerHelper";

export const getProductDetailsForCollectionDetails = async (
  admin,
  cursor = null,
) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo("Get product details for collection Details page", storeName);

  try {
    // Validate cursor before passing it
    const validCursor =
      cursor && cursor !== "null" && cursor !== "undefined" ? cursor : null;

    // Construct the GraphQL query with a conditional cursor
    const productDetails = await admin.graphql(
      `
      query($cursor: String) {
        products(first: 10, after: $cursor) {
          edges {
            node {
              id
              title
              totalInventory
              handle
              priceRangeV2 {
                minVariantPrice {
                  amount
                }
              }
              media(first: 1) {
                edges {
                  node {
                    preview {
                      image {
                        url
                      }
                    }
                  }
                }
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
      }`,
      { variables: validCursor ? { cursor: validCursor } : {} },
    );

    const response = await productDetails.json();

    if (!response.data || !response.data.products) {
      throw new Error("Invalid response from Shopify API");
    }

    // Extract product information and add to the list
    const products = response.data.products.edges.map((edge) => ({
      id: edge.node.id,
      title: edge.node.title,
      handle: edge.node.handle,
      totalInventory: edge.node.totalInventory,
      price: edge.node.priceRangeV2.minVariantPrice.amount,
      image: edge.node.media.edges[0]?.node.preview.image.url || null, // Handle cases where image might not exist
    }));

    const pageInfo = response.data.products.pageInfo;

    loggerInfo("Product details fetched successfully", storeName);

    return {
      status: 200,
      message: "Products fetched successfully",
      data: {
        products,
        hasNextPage: pageInfo.hasNextPage,
        endCursor: pageInfo.endCursor, // Return cursor for next request
      },
    };
  } catch (error) {
    loggerError("Error fetching product details", storeName, error.message);
    return {
      status: 400,
      message: "Something went wrong",
      data: null,
    };
  }
};
