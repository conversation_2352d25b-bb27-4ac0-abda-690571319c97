import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { shopDetails } from './saveStoreData.server';
import { ServerClient } from 'postmark';
import dotenv from 'dotenv';

dotenv.config();

const postmarkClient = new ServerClient(process.env.POST_MARK_COLLECTION_API);
const senderMail = process.env.COLLECTION_SENDER_MAIL;
const welcomeTemplateId = process.env.WELCOME_TEMPLATE_ID;


export const welcomeMailSent = async (admin) => {
  try {
    const shop = await shopDetails(admin);
    const storeEmail = shop?.data?.shop?.email;
    const storeName = shop?.data?.shop?.name;

    if (!storeEmail || !storeName) {
      loggerError('Missing store email or store name.');
      return {
        status: 400,
        message: 'Missing store details for welcome email',
        data: null,
      };
    }

    const currentYear = new Date().getFullYear();

    await postmarkClient.sendEmailWithTemplate({
      From: senderMail,
      To: storeEmail,
      TemplateId: welcomeTemplateId,
      TemplateModel: {
        store_name: storeName,
        currentYear: currentYear,
      },
    });

    loggerInfo('Welcome email successfully sent to store:', storeName);

    return {
      status: 200,
      message: 'Welcome email sent successfully',
      data: null
    };
  } catch (error) {
    loggerError('Error sending welcome email:', error.message);
    return {
      status: 400,
      message: 'Error sending welcome email',
      data: null,
    };
  }
};
