import axios from 'axios';
import dotenv from 'dotenv';
import { supabase } from '../db/supabase_insert_helper';
import { loggerError, loggerInfo } from '../helper/loggerHelper';
import { engineBaseUrl, engineAPIVersion, collectionRoute, syncRoute, updateProductRoute } from '../configs/config';

dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN;
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${updateProductRoute}`;

export const syncUpdatedProductsToAgent = async (shop, productDetails) => {
    loggerInfo('Sync updated products to agent', shop, productDetails);
    try{

        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_url', shop);
        if(storeDataError){
            loggerError('Store data not found', shop, storeDataError.message);
            return {
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }

        // Test EndPoint Implementation
        const endPoint = `https://webhook.site/b95a807c-2ea1-4974-b993-55e3be0cfeea`;
        const response = await axios.post (endPoint, productDetails);
        loggerInfo('Sync updated products to agent', shop);
        return {
            status: 200,
            message: "Sync updated products to agent",
            data: null
        }

        // Actual EndPoint Implementation
        // const engineAPIToken = storeData[0].engine_api_token;
        // const parsedProducts = typeof productDetails === 'string' ? JSON.parse(productDetails) : productDetails;

        // const response = await axios.post(endPoint, parsedProducts, {
        //     headers: {
        //         "X-PROACTIVE-TOKEN": engineAPIToken,
        //         "X-BINCHA-APP-TOKEN": appToken
        //     }
        // })

        // loggerInfo('Sync updated products to agent', shop, response.data);
        // return {
        //     status: response.data.status_code,
        //     message: response.data.message,
        //     data: null
        // }

    }catch(error){
        loggerError('Something went wrong', shop, error.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }
}