import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./saveStoreData.server";
import{loggerInfo, loggerError} from '../helper/loggerHelper'

export const fetchOnboardingConfiguration = async (admin) => {
  const shop = await shopDetails(admin);
  const storeName = shop?.data?.shop?.name;
  loggerInfo("Fetching Onboarding Configuration function is initialized",storeName,  null);
  try{
    const appSubscriptionFetch = await admin.graphql(`
      
      query{
        currentAppInstallation{
          activeSubscriptions{
            id
            status
            name
          }
        }
      }`)
      const response = await appSubscriptionFetch.json();
      const paymentStatus = response?.data?.currentAppInstallation?.activeSubscriptions[0]?.status || null;
        const paymentId = response?.data?.currentAppInstallation?.activeSubscriptions[0]?.id || null
        const planName = response?.data?.currentAppInstallation?.activeSubscriptions[0]?.name || null
        
    const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
    if(storeDataError){
      loggerError('Store Details not found (Fetching Onboarding configuration)',storeName, storeDataError.message);
      return {
        status: 400,
        message: "Something went wrong on Fetching onboarding Configuration",
        data: null
      }
    }
    loggerInfo("Onboarding Configuration fetched successfully",storeName,  null);
    return {
      status: 200,
      message: "Onboarding configuration fetched successfully",
      data: {
        paymentId: paymentId,
        onboardStatus : storeData[0].onboarding_status,
        storeConnected: storeData[0].is_store_connected,
        productSynced: storeData[0].is_product_synced,
        productCount: storeData[0].product_count,
      }
    }
  }catch(error){
    loggerError('Something went wrong on the fetching onboarding configuration',storeName, error.message);
    return {
      status: 400,
      message: "Something went wrong",
      data: null
    }
  }
}

