import { shopDetails } from "./saveStoreData.server";
import { supabase } from "../db/supabase_insert_helper";
import dotenv from 'dotenv'
import axios from 'axios';
import { engineBaseUrl, engineAPIVersion, collectionRoute, taskStatusRoute } from "../configs/config";
import { loggerInfo, loggerError } from "../helper/loggerHelper";

dotenv.config();
const appToken = process.env.BINCHA_APP_TOKEN;
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${collectionRoute}${taskStatusRoute}`

export const fetchTaskStatus = async (admin, id) => {
    const shop = await shopDetails(admin);
    const storeName = shop?.data?.shop?.name;
    loggerInfo('Task status fetching function is initialized', storeName, {id});
    try{
        const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${collectionRoute}${taskStatusRoute}/${id}`
        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', storeName);
        if(storeDataError){
            loggerError("Store Data not found (Fetch Task Status)", storeName, storeDataError.message);
            return{
                status: 400,
                message: "Something went wrong",
                data: null
            }
        }
        const apiToken = storeData[0].engine_api_token;
        const taskStatus = await axios.get(endPoint, {
            headers: {
                'X-PROACTIVE-TOKEN': apiToken,
                'X-BINCHA-APP-TOKEN': appToken
            }
        })
        
        loggerInfo('Job status fetched successfully', storeName, {id});
        return {
            status: taskStatus.data.status_code,
            message: taskStatus.data.message,
            data: taskStatus.data.data.status
        }
    }catch(error){
        loggerError("Something went wrong to fetch the task status by it's id", storeName, error.message);
        return {
            status: 400,
            message: "Something went wrong to fetch job status",
            data: null
        }
    }
}
