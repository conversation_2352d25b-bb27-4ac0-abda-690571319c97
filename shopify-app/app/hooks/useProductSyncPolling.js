import { useState, useEffect, useCallback, useRef } from "react";
import { useSubmit, useActionData } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react"; // Import useAppBridge

const SYNC_TASK_ID_KEY = "rankCollections-product_sync_task_id";
const POLLING_INTERVAL = 10000; // 10 seconds

export function useProductSyncPolling(initialTaskId, setInitialTaskId) {
  const submit = useSubmit();
  const actionData = useActionData();
  const app = useAppBridge(); // Get app bridge instance

  const [syncTaskId, setSyncTaskId] = useState(initialTaskId);
  const [isPolling, setIsPolling] = useState(!!initialTaskId);
  const pollingTimeoutRef = useRef(null); // Ref to store timeout ID

  // Update state if initialTaskId changes (e.g., from initial data load)
  useEffect(() => {
    setSyncTaskId(initialTaskId);
    setIsPolling(!!initialTaskId);
    if (initialTaskId) {
      checkSyncStatus(initialTaskId); // Start checking immediately if we get an ID
    }
  }, [initialTaskId]);

  // Function to check task status via action
  const checkSyncStatus = useCallback(
    (idToCheck) => {
      if (!idToCheck) return;

      const formData = new FormData();
      formData.append("type", "fetch-product-sync");
      formData.append("id", idToCheck);
      submit(formData, { method: "post", replace: true });
    },
    [submit],
  );

  // Function to stop polling and clear task ID
  const stopPolling = useCallback(() => {
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current);
      pollingTimeoutRef.current = null;
    }
    localStorage.removeItem(SYNC_TASK_ID_KEY);
    setSyncTaskId(null);
    setIsPolling(false);
    // Also update the state in the parent hook/component if necessary
    if (setInitialTaskId) {
      setInitialTaskId(null);
    }
  }, [syncTaskId, setInitialTaskId]);

  // Effect to handle 'fetch-product-sync' action response
  useEffect(() => {
    // Only process if the action type matches and we are currently polling for a task
    if (actionData?.type === "fetch-product-sync" && syncTaskId && isPolling) {
      const status = actionData?.data?.syncStatus;
      const message = actionData?.message;
      const statusCode = actionData?.status;

      if (statusCode !== 200) {
        console.error("Product sync status check failed:", message);
        if (app && app.toast) {
          app.toast.show(message || "Failed to check product sync status", {
            isError: true,
          });
        }
        // Consider if we should stop polling on *any* error, or only specific ones.
        // For now, stopping polling on any failed check.
        stopPolling();
        return;
      }

      if (status === "COMPLETED" || status === "FAILED") {
        if (status === "FAILED") {
          if (app && app.toast) {
            app.toast.show(message || "Product synchronization failed.", {
              isError: true,
            });
          }
        } else if (status === "COMPLETED") {
          if (app && app.toast) {
            // Optionally show a success message, or just let it complete silently
            // shopify.toast.show("Product synchronization complete!");
          }
        }
        stopPolling(); // Stop polling on completion/failure
      } else if (status === "PROCESSING" || status === "QUEUED") {
        // Task is still running

        // Clear previous timeout just in case
        if (pollingTimeoutRef.current) clearTimeout(pollingTimeoutRef.current);
        // Schedule next check
        pollingTimeoutRef.current = setTimeout(
          () => checkSyncStatus(syncTaskId),
          POLLING_INTERVAL,
        );
      } else {
        // Handle unexpected status? Maybe stop polling.
        console.warn(`Received unexpected product sync status: ${status}`);
        stopPolling();
      }
    }
  }, [actionData, syncTaskId, isPolling, stopPolling, checkSyncStatus, app]);

  // Effect to clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
      }
    };
  }, []);

  return {
    syncTaskId, // The current sync task ID being polled (null if none)
    isSyncPolling: isPolling, // Whether product sync polling is currently active
    // No startPolling needed, it's triggered by initialTaskId prop
  };
}
