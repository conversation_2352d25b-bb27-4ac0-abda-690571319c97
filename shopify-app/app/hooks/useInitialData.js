import { useState, useEffect, useCallback } from "react";
import { useSubmit, useActionData } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react"; // Import useAppBridge

const CACHE_KEY = "rankCollections-initial-data";
const LAST_FETCH_KEY = "rankCollections-last-fetch-time";
const PRODUCT_COUNT_KEY = "productCount";
const PRODUCT_SYNC_ID_KEY = "rankCollections-product_sync_task_id";
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

export function useInitialData() {
  const submit = useSubmit();
  const actionData = useActionData();
  const app = useAppBridge(); // Get app bridge instance

  const [isLoading, setIsLoading] = useState(true); // Combined loading state
  const [isMetricsLoading, setIsMetricsLoading] = useState(false);
  const [shopDomain, setShopDomain] = useState("");
  const [scheduleConfigs, setScheduleConfigs] = useState({});
  const [conversionRate, setConversionRate] = useState();
  const [opportunity, setOpportunity] = useState();
  const [productCount, setProductCount] = useState(0);
  const [aiTime, setAITime] = useState([]);
  const [scheduleStatus, setScheduleStatus] = useState(false);
  const [automaticUpdate, setAutomaticUpdate] = useState(false);
  const [productSyncTaskId, setProductSyncTaskId] = useState(null); // Store the ID from local storage

  // Function to trigger initial data fetch
  const fetchInitialData = useCallback(() => {
    setIsLoading(true);
    const formData = new FormData();
    formData.append("type", "initial-fetch");
    const storedSyncId = localStorage.getItem(PRODUCT_SYNC_ID_KEY);
    if (storedSyncId) {
      formData.append("product_sync_id", storedSyncId);
      setProductSyncTaskId(storedSyncId); // Ensure state is updated
    }
    submit(formData, { method: "post", replace: true });
  }, [submit]);

  // Function to trigger metrics fetch if needed
  const fetchMetricsData = useCallback(() => {
    setIsMetricsLoading(true);
    const metricsFormData = new FormData();
    metricsFormData.append("type", "fetch-metrics");
    submit(metricsFormData, { method: "post", replace: true });
  }, [submit]);

  // Effect to load from cache or fetch on mount
  useEffect(() => {
    const storedData = localStorage.getItem(CACHE_KEY);
    const lastFetchTime = localStorage.getItem(LAST_FETCH_KEY);
    const storedProductCount = localStorage.getItem(PRODUCT_COUNT_KEY);
    const storedSyncId = localStorage.getItem(PRODUCT_SYNC_ID_KEY);

    setProductSyncTaskId(storedSyncId);
    if (storedProductCount) setProductCount(Number(storedProductCount)); // Use locally stored product count

    const now = Date.now();
    const shouldRefetch =
      !lastFetchTime || now - Number(lastFetchTime) > CACHE_DURATION;

    if (storedData && !shouldRefetch) {
      try {
        const parsedData = JSON.parse(storedData);
        setShopDomain(`https://${parsedData?.domain}`);
        setScheduleConfigs(parsedData?.data || {});
        setConversionRate(parsedData?.metrics?.conversion_rate);
        setOpportunity(parsedData?.metrics?.opportunity_score);
        setAITime(parsedData?.schedule?.data || []);
        setScheduleStatus(parsedData?.data?.scheduleStatus || false);
        setAutomaticUpdate(parsedData?.data?.automaticUpdate || false);
        setIsLoading(false); // Cache loaded

        // Check if metrics are missing and refetch only them
        if (
          !parsedData?.metrics?.conversion_rate ||
          !parsedData?.metrics?.opportunity_score
        ) {
          fetchMetricsData();
        }
      } catch (e) {
        console.error("Failed to parse cached data:", e);
        localStorage.removeItem(CACHE_KEY); // Clear corrupted cache
        fetchInitialData(); // Fetch fresh data
      }
    } else {
      fetchInitialData();
    }
  }, [fetchInitialData, fetchMetricsData]); // Dependencies

  // Effect to handle 'initial-fetch' action response
  useEffect(() => {
    if (actionData?.type === "initial-fetch") {
      if (actionData?.status === 200) {
        localStorage.setItem(CACHE_KEY, JSON.stringify(actionData));
        localStorage.setItem(LAST_FETCH_KEY, Date.now().toString());
        // Note: Product count comes from a separate loader/storage mechanism
        // localStorage.setItem(PRODUCT_COUNT_KEY, actionData?.productCount?.toString() || '0');

        setShopDomain(`https://${actionData?.domain}`);
        setScheduleConfigs(actionData?.data || {});
        setConversionRate(actionData?.metrics?.conversion_rate);
        setOpportunity(actionData?.metrics?.opportunity_score);
        // setProductCount(actionData?.productCount || 0);
        setAITime(actionData?.schedule?.data || []);
        setScheduleStatus(actionData?.data?.scheduleStatus || false);
        setAutomaticUpdate(actionData?.data?.automaticUpdate || false);

        // Handle product sync status from initial fetch
        const syncStatus = actionData?.productSyncStatus?.syncStatus;
        if (syncStatus === "COMPLETED" || syncStatus === "FAILED") {
          localStorage.removeItem(PRODUCT_SYNC_ID_KEY);
          setProductSyncTaskId(null); // Update state
        } else if (syncStatus === "PROCESSING" || syncStatus === "QUEUED") {
          setProductSyncTaskId(localStorage.getItem(PRODUCT_SYNC_ID_KEY)); // Ensure state has the ID
          // The useProductSyncPolling hook will handle polling
        } else {
          // No active sync task ID was provided or found
          setProductSyncTaskId(null);
        }
      } else {
        console.error("Initial fetch failed:", actionData?.message);
        if (app && app.toast) {
          app.toast.show(actionData?.message || "Failed to load initial data", {
            isError: true,
          });
        }
      }
      setIsLoading(false); // Stop global loading
    }
  }, [actionData, app]);

  // Effect to handle 'fetch-metrics' action response
  useEffect(() => {
    if (actionData?.type === "fetch-metrics") {
      if (actionData?.status === 200) {
        const storedData = JSON.parse(localStorage.getItem(CACHE_KEY) || "{}");
        storedData.metrics = actionData.data;
        localStorage.setItem(CACHE_KEY, JSON.stringify(storedData)); // Update cache

        setConversionRate(actionData?.data?.conversion_rate);
        setOpportunity(actionData?.data?.opportunity_score);
      } else {
        console.error("Metrics fetch failed:", actionData?.message);
        if (app && app.toast) {
          app.toast.show(
            actionData?.message || "Failed to load dashboard metrics",
            { isError: true },
          );
        }
      }
      setIsMetricsLoading(false);
    }
  }, [actionData, app]);

  // Function to manually update cached schedule status after successful sync
  const updateCachedScheduleConfig = useCallback((newConfigData) => {
    try {
      const storedData = JSON.parse(localStorage.getItem(CACHE_KEY) || "{}");
      // Merge new config data into the cached data structure
      const updatedData = {
        ...storedData,
        data: {
          ...(storedData.data || {}),
          scheduleStatus: true, // Mark as scheduled
          frequency: newConfigData?.schedule_settings?.frequency,
          count: newConfigData?.schedule_settings?.count,
          impact: newConfigData?.schedule_settings?.impact_type,
          customer: newConfigData?.attributes?.customer,
          location: newConfigData?.attributes?.location,
          market: newConfigData?.attributes?.market,
          product: newConfigData?.attributes?.product,
          seasonal: newConfigData?.attributes?.seasonal,
          urlSync: true, // Assuming sync implies URL is now synced
          automaticUpdate: true, // Assuming schedule sync enables this
        },
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(updatedData));
    } catch (e) {
      console.error("Failed to update cached schedule config:", e);
    }
  }, []);

  // Function to manually update cached URL sync status after successful sync
  const updateCachedUrlSyncStatus = useCallback((isSynced) => {
    try {
      const storedData = JSON.parse(localStorage.getItem(CACHE_KEY) || "{}");
      const updatedData = {
        ...storedData,
        data: {
          ...(storedData.data || {}),
          urlSync: isSynced,
        },
      };
      localStorage.setItem(CACHE_KEY, JSON.stringify(updatedData));
    } catch (e) {
      console.error("Failed to update cached URL sync status:", e);
    }
  }, []);

  return {
    isLoading,
    isMetricsLoading,
    shopDomain,
    scheduleConfigs,
    conversionRate,
    opportunity,
    productCount,
    aiTime,
    scheduleStatus,
    automaticUpdate,
    productSyncTaskId,
    setProductSyncTaskId, // Expose setter for polling hook
    updateCachedScheduleConfig, // Expose function to update cache after sync actions
    updateCachedUrlSyncStatus, // Expose function to update cache after URL sync
  };
}
