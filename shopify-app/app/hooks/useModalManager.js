import { useState, useCallback } from 'react';

export function useModalManager() {
    const [isCreateModalOpen, setCreateModalOpen] = useState(false);
    const [isAutoScheduleModalOpen, setAutoScheduleModalOpen] = useState(false);
    const [isScheduleCollectionModalOpen, setScheduleCollectionModalOpen] = useState(false);
    const [isUpgradeModalOpen, setUpgradeModalOpen] = useState(false);
    const [isLimitReachedModalOpen, setLimitReachedModalOpen] = useState(false);
    const [isInfoModalOpen, setInfoModalOpen] = useState(false);
    const [isProductSyncModalOpen, setProductSyncModalOpen] = useState(false);

    // State to track the context/source for the auto schedule modal
    const [autoScheduleSource, setAutoScheduleSource] = useState("");

    // --- Create Modal --- 
    const openCreateModal = useCallback(() => setCreateModalOpen(true), []);
    const closeCreateModal = useCallback(() => setCreateModalOpen(false), []);

    // --- Auto Schedule Modal ---
    const openAutoScheduleModal = useCallback((source) => {
        setCreateModalOpen(false); // Close create modal if opening this one
        setAutoScheduleSource(source);
        setAutoScheduleModalOpen(true);
    }, []);
    const closeAutoScheduleModal = useCallback(() => {
        setAutoScheduleModalOpen(false);
        setAutoScheduleSource(""); // Reset source on close
        // Reset any other state specific to this modal if needed, e.g., keywordOptions
    }, []);

    // --- Schedule Collection Modal ---
    const openScheduleCollectionModal = useCallback(() => setScheduleCollectionModalOpen(true), []);
    const closeScheduleCollectionModal = useCallback(() => setScheduleCollectionModalOpen(false), []);

    // --- Upgrade Modal ---
    const openUpgradeModal = useCallback(() => {
        setCreateModalOpen(false); // Close create modal if opening this one
        setUpgradeModalOpen(true);
    }, []);
    const closeUpgradeModal = useCallback(() => setUpgradeModalOpen(false), []);

    // --- Limit Reached Modal ---
    const openLimitReachedModal = useCallback(() => setLimitReachedModalOpen(true), []);
    const closeLimitReachedModal = useCallback(() => setLimitReachedModalOpen(false), []);

    // --- Info Modal (Task Running) ---
    const openInfoModal = useCallback(() => setInfoModalOpen(true), []);
    const closeInfoModal = useCallback(() => setInfoModalOpen(false), []);

    // --- Product Sync Modal ---
    const openProductSyncModal = useCallback(() => setProductSyncModalOpen(true), []);
    const closeProductSyncModal = useCallback(() => setProductSyncModalOpen(false), []);

    return {
        isCreateModalOpen,
        openCreateModal,
        closeCreateModal,
        isAutoScheduleModalOpen,
        openAutoScheduleModal,
        closeAutoScheduleModal,
        autoScheduleSource, // Expose source for conditional logic in AutoSchedule modal
        isScheduleCollectionModalOpen,
        openScheduleCollectionModal,
        closeScheduleCollectionModal,
        isUpgradeModalOpen,
        openUpgradeModal,
        closeUpgradeModal,
        isLimitReachedModalOpen,
        openLimitReachedModal,
        closeLimitReachedModal,
        isInfoModalOpen,
        openInfoModal,
        closeInfoModal,
        isProductSyncModalOpen,
        openProductSyncModal,
        closeProductSyncModal,
    };
} 