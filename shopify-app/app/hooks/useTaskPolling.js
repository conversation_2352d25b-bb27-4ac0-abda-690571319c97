import { useState, useEffect, useCallback, useRef } from "react";
import { useSubmit, useActionData } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react"; // Import useAppBridge

const TASK_ID_KEY = "manual-generate-task-id";
const POLLING_INTERVAL = 10000; // 10 seconds

export function useTaskPolling() {
  const submit = useSubmit();
  const actionData = useActionData();
  const app = useAppBridge(); // Get app bridge instance

  const [taskId, setTaskId] = useState(null);
  const [isPolling, setIsPolling] = useState(false);
  const pollingTimeoutRef = useRef(null); // Ref to store timeout ID

  // Function to initiate polling for a given task ID
  const startPolling = useCallback(
    (id) => {
      localStorage.setItem(TASK_ID_KEY, id); // Store the ID
      setTaskId(id);
      setIsPolling(true);
      // Initial check immediately
      checkTaskStatus(id);
    },
    [submit],
  ); // Removed checkTaskStatus from deps to avoid potential loops

  // Function to check task status via action
  const checkTaskStatus = useCallback(
    (idToCheck) => {
      if (!idToCheck) return;

      const formData = new FormData();
      formData.append("type", "task-status");
      formData.append("id", idToCheck);
      submit(formData, { method: "post", replace: true });
    },
    [submit],
  );

  // Function to stop polling and clear task ID
  const stopPolling = useCallback(() => {
    if (pollingTimeoutRef.current) {
      clearTimeout(pollingTimeoutRef.current);
      pollingTimeoutRef.current = null;
    }
    localStorage.removeItem(TASK_ID_KEY);
    setTaskId(null);
    setIsPolling(false);
  }, [taskId]);

  // Effect to load task ID from storage on mount
  useEffect(() => {
    const storedTaskId = localStorage.getItem(TASK_ID_KEY);
    if (storedTaskId) {
      startPolling(storedTaskId);
    }
  }, [startPolling]); // Run only on mount

  // Effect to handle 'task-status' action response
  useEffect(() => {
    if (actionData?.type === "task-status" && taskId) {
      // Only process if polling is active for a task

      const status = actionData?.data;
      const message = actionData?.message;
      const statusCode = actionData?.status;

      if (statusCode !== 200) {
        console.error("Task status check failed:", message);
        if (app && app.toast) {
          app.toast.show(message || "Failed to check task status", {
            isError: true,
          });
        }
        stopPolling(); // Stop polling on error
        return;
      }

      if (["COMPLETED", "FAILED", "NO_COLLECTIONS"].includes(status)) {
        if (status === "FAILED" || status === "NO_COLLECTIONS") {
          if (app && app.toast) {
            // Use a more specific message based on status if possible
            const toastMessage =
              status === "NO_COLLECTIONS"
                ? "No valid products found for the keywords."
                : message || "Collection generation failed.";
            app.toast.show(toastMessage, { isError: true });
          }
        } else if (status === "COMPLETED") {
          if (app && app.toast) {
            app.toast.show("Manual collection generation complete!");
          }
        }
        stopPolling(); // Stop polling on completion/failure
      } else {
        // Task is still running (e.g., PENDING, PROCESSING)

        // Clear previous timeout just in case
        if (pollingTimeoutRef.current) clearTimeout(pollingTimeoutRef.current);
        // Schedule next check
        pollingTimeoutRef.current = setTimeout(
          () => checkTaskStatus(taskId),
          POLLING_INTERVAL,
        );
      }
    }
  }, [actionData, taskId, stopPolling, checkTaskStatus, app]);

  // Effect to clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (pollingTimeoutRef.current) {
        clearTimeout(pollingTimeoutRef.current);
      }
    };
  }, []);

  return {
    taskId, // The current task ID being polled (null if none)
    isPolling, // Whether polling is currently active
    startPolling, // Function to initiate polling for a new task ID
  };
}
