import { useState, useEffect, useCallback } from "react";
import { useSubmit, useActionData } from "@remix-run/react";
import { useAppBridge } from "@shopify/app-bridge-react"; // Import useAppBridge
import { trackButtonClick } from "../helper/analytics"; // Assuming analytics helper is here
import content from "../locales/en.json";

export function useDashboardActions(
  shopName,
  closeModalCallback,
  startTaskPollingCallback,
  updateCachedScheduleCallback,
  updateCachedUrlSyncCallback,
) {
  const submit = useSubmit();
  const actionData = useActionData();
  const app = useAppBridge(); // Get app bridge instance

  // Loading states for different actions
  const [isScheduleLoading, setScheduleLoading] = useState(false);
  const [isCompetitorSyncLoading, setCompetitorSyncLoading] = useState(false);
  const [isManualGenerateLoading, setManualGenerateLoading] = useState(false);

  // State for selected schedule (from ScheduleCollections modal)
  const [selectedSchedule, setSelectedSchedule] = useState({});
  const [isUrlSynced, setIsUrlSynced] = useState(false); // Track URL sync status locally

  // --- Action Handlers ---

  const handleScheduleUpdateSelect = useCallback((schedule) => {
    setSelectedSchedule(schedule);
    // Typically, selecting a schedule closes the ScheduleCollections modal
    // and opens the AutoSchedule modal. The modal manager handles the opening,
    // but we might need to call the close callback here if it wasn't passed through.
    // Assuming the parent component handles modal flow based on state changes.
  }, []);

  const handleConfigSchedule = useCallback(
    (onUpdate) => {
      setScheduleLoading(true);
      const { schedule_settings, ...restOfOnUpdate } = onUpdate;
      const formData = new FormData();
      formData.append("type", "sync-schedule");
      formData.append("attributes", JSON.stringify(restOfOnUpdate));
      formData.append("schedule-settings", JSON.stringify(schedule_settings));
      submit(formData, { method: "post", replace: true });
      trackButtonClick("Schedule Update", "Home Page", { shop_name: shopName });
    },
    [submit, shopName],
  );

  const handleSyncCompetitorUrl = useCallback(
    (urlData) => {
      setCompetitorSyncLoading(true);
      const formData = new FormData();
      formData.append("type", "sync-url");
      formData.append("url", JSON.stringify(urlData.url)); // Assuming urlData is { url: '...' }
      submit(formData, { method: "post", replace: true });
      trackButtonClick("Competitor URL Sync", "Home Page", {
        shop_name: shopName,
      });
    },
    [submit, shopName],
  );

  const handleManualGenerate = useCallback(
    (manualCreatePayload) => {
      setManualGenerateLoading(true);
      const { competitorUrl, ...restofAttributes } = manualCreatePayload;
      const formData = new FormData();
      formData.append("type", "manual-generate");
      formData.append("competitorUrl", competitorUrl || ""); // Ensure it sends a value
      formData.append("attributes", JSON.stringify(restofAttributes));
      submit(formData, { method: "post", replace: true });
      trackButtonClick("Manually Generate the Collection", "Home Page", {
        shop_name: shopName,
      });
    },
    [submit, shopName],
  );

  // --- Effect to handle Action Responses ---

  useEffect(() => {
    if (!actionData) return;

    // Handle sync-schedule response
    if (actionData.type === "sync-schedule") {
      setScheduleLoading(false);
      if (actionData.status === 200) {
        if (app && app.toast)
          app.toast.show(
            actionData.message || "Schedule updated successfully!",
          );
        updateCachedScheduleCallback(actionData.data);
        if (closeModalCallback) closeModalCallback();
      } else {
        if (app && app.toast)
          app.toast.show(actionData.message || "Failed to update schedule", {
            isError: true,
          });
      }
    }

    // Handle sync-url response
    if (actionData.type === "sync-url") {
      setCompetitorSyncLoading(false);
      if (actionData.status === 200) {
        if (app && app.toast)
          app.toast.show(actionData.message || "Competitor URL synced!");
        setIsUrlSynced(actionData.data);
        updateCachedUrlSyncCallback(actionData.data);
      } else {
        if (app && app.toast)
          app.toast.show(actionData.message || "Failed to sync URL", {
            isError: true,
          });
      }
    }

    // Handle manual-generate response
    if (actionData.type === "manual-generate") {
      setManualGenerateLoading(false);
      if (actionData.status === 200 && actionData.data?.task_id) {
        if (app && app.toast)
          app.toast.show(
            content.Rank_collections.collection_generation.toast_2,
          );
        startTaskPollingCallback(actionData.data.task_id);
        if (closeModalCallback) closeModalCallback();
      } else {
        const errorMessage =
          actionData.status !== 200 ? actionData.data : actionData.message;
        if (app && app.toast)
          app.toast.show(
            errorMessage || "Failed to start collection generation",
            { isError: true },
          );
      }
    }
  }, [
    actionData,
    app,
    startTaskPollingCallback,
    closeModalCallback,
    updateCachedScheduleCallback,
    updateCachedUrlSyncCallback,
  ]);

  return {
    isScheduleLoading,
    isCompetitorSyncLoading,
    isManualGenerateLoading,
    selectedSchedule, // The schedule time selected in ScheduleCollections modal
    isUrlSynced, // Local state for URL sync status
    setIsUrlSynced, // Allow initial setting from useInitialData
    handleScheduleUpdateSelect, // When user selects a time in ScheduleCollections
    handleConfigSchedule, // Submits the final schedule config
    handleSyncCompetitorUrl, // Submits competitor URL
    handleManualGenerate, // Starts manual generation
  };
}
