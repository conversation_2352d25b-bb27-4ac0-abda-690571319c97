import { useEffect, useCallback, useState, useRef } from "react";
// import { authenticate } from "../shopify.server";
// import { json } from "@remix-run/node";
import {
  useLocation,
  useActionData,
  useLoaderData,
  useSubmit,
} from "@remix-run/react";
import {
  Text,
  Button,
  Card,
  Grid,
  InlineStack,
  BlockStack,
  Icon,
  Box,
  TextField,
  Select,
  Divider,
  Badge,
  Checkbox,
  Page,
  Spinner,
  Thumbnail,
  InlineGrid,
} from "@shopify/polaris";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  WandIcon,
  ClockIcon,
  ArrowDiagonalIcon,
  SearchIcon,
  PlusIcon,
  DeleteIcon,
  FileIcon,
} from "@shopify/polaris-icons";

import content from "../locales/en.json";
import feature from "../configs/featureFlag.json";
import { humanReadableTime } from "../helper/formatDateAndTime";

// Server imports
// import { fetchSinglecollection } from "../models/fetchSinglecollection.server";
// import { productDeleteFromCollection } from "../models/productsDeleteFromCollection.server";
// import { getproductsForcollection } from "../models/collectionPageProducts.server";
// import { addProductToSingleCollection } from "../models/addProductToSingleCollection.server";

// // Server-side handlers
// export const loader = async ({ request }) => {
//   const { session } = await authenticate.admin(request);
//   const shopName = session.shop;
//   return json({ shopName });
// };

// export const action = async ({ request }) => {
//   const { admin } = await authenticate.admin(request);
//   const formData = await request.formData();
//   const type = formData.get("type");
//   try {
// if (type === "fetch-collection-details") {
//   const collectionId = formData.get("id");
//   const actionData = await fetchSinglecollection(
//     admin,
//     collectionId,
//   );
//   const products = await getproductsForcollection(admin);

//   return json({
//     status: actionData.status || products.status,
//     message: actionData.message || products.message,
//     data: {
//       collectionDetail: actionData.data,
//       products: products,
//     },
//     type: type,
//   });
// } else if (type === "delete-product") {
//       const collectionId = formData.get("collectionId");
//       const productId = formData.get("productId");
//       const actionData = await productDeleteFromCollection(
//         admin,
//         collectionId,
//         productId,
//       );
//       return json({
//         status: actionData.status,
//         message: actionData.message,
//         data: actionData.data,
//         type: type,
//       });
//     } else if (type === "add-products") {
//       const collectionId = formData.get("collectionId");
//       const productId = formData.get("productId");
//       const actionData = await addProductToSingleCollection(
//         admin,
//         collectionId,
//         productId,
//       );
//       return json({
//         status: actionData.status,
//         message: actionData.message,
//         data: actionData.data,
//         type: type,
//       });
//     }
//   } catch (error) {
//     return json({
//       status: 400,
//       message: "Something went wrong",
//       data: null,
//     });
//   }
// };

export default function ProductsViewModalWithSEO({
  open,
  hide,
  collection,
  loading,
  products,
  scheduleUpdate,
  deleteProduct,
  deleteLoading,
  addProducts,
  addProductLoading,
  addProductHide,
  automatic,
  navigate,
  sortUpdate,
  sortLoading,
  sortValue,
  productLoading,
  addProductFetch,
}) {
  //   Hooks initialization
  const submit = useSubmit();
  const actionData = useActionData();
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  //   const collectionId = params.get("collectionId");

  // State management
  const [pageLoading, setPageLoading] = useState(false);

  const [selectedProduct, setSelectedProducts] = useState([]);
  const [selectedOption, setSelectedOption] = useState("all-products");
  const [selectedCollectionSettings, setSelectedCollectionSettings] =
    useState("");

  const [productDeleteModalOpen, setProductDeleteModalOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(null);
  const [productModalOpen, setProductModalOpen] = useState(false);

  // Computed values
  const inlineStackProducts = collection?.products?.filter(
    (item) => item.totalInventory > 0,
  )?.length;
  const outOfStockProduct = collection?.products?.filter(
    (item) => item.totalInventory === 0,
  )?.length;

  // Callbacks
  const handleSelectProducts = useCallback((id) => {
    setSelectedProducts((prev) =>
      prev.includes(id) ? prev.filter((item) => item !== id) : [...prev, id],
    );
  }, []);

  const handleOptionsChange = useCallback(
    (value) => setSelectedOption(value),
    [],
  );

  const handleCollectionSettingsChange = useCallback((attribute) => {
    setSelectedCollectionSettings((prev) =>
      prev.includes(attribute)
        ? prev.filter((item) => item !== attribute)
        : [...prev, attribute],
    );
  }, []);

  // Modal handlers
  const handleProductModalOpen = () => setProductModalOpen(true);

  useEffect(() => {
    // Check if loading was true and has now changed to false (operation completed)
    if (addProductLoading === false && productModalOpen) {
      handleProductModalClose();
    }
  }, [addProductLoading]);

  useEffect(() => {
    // Check if loading was true and has now changed to false (operation completed)
    if (addProductLoading === false && productModalOpen) {
      handleProductModalClose();
    }
  }, [addProductLoading]);

  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const handleUpgradeModalOpen = () => setUpgradeModalOpen(true);
  const handleUpgradeModalClose = () => setUpgradeModalOpen(false);

  const handleProductDeleteModalOpen = (id) => {
    setProductDeleteModalOpen(true);
    setSelectedIndex(id);
  };

  const handleProductDeleteModalClose = () => {
    setProductDeleteModalOpen(false);
    setSelectedIndex(null);
  };

  // Action handlers
  const handleDeleteSingleProduct = (id) => {
    const productToDelete = collection?.products.find(
      (product) => product.id === id,
    );

    if (!productToDelete) return; // Ensure the product exists before proceeding

    deleteProduct({
      id: productToDelete.id,
      collectionId: collection?.collectionId,
    });

    handleProductDeleteModalClose();
  };

  const handleAddProducts = () => {
    // setAddProductLoading(true);
    // const formData = new FormData();
    // formData.append("type", "add-products");
    // formData.append("collectionId", collectionId);
    // formData.append("productId", JSON.stringify(selectedProduct));
    // submit(formData, { method: "post" });
    addProducts({
      collectionId: collection?.collectionId,
      productIds: selectedProduct,
    });
  };

  // Effects
  //   useEffect(() => {
  //     setPageLoading(true);
  //     const formData = new FormData();
  //     formData.append("type", "fetch-collection-details");
  //     formData.append("id", collectionId);
  //     submit(formData, { method: "post" });
  //   }, []);

  //   useEffect(() => {
  //     if (
  //       actionData?.type === "fetch-collection-details" &&
  //       actionData?.status === 200
  //     ) {
  //       setcollection(actionData?.data?.collectionDetail);
  //       const existingProducts =
  //         actionData?.data?.collectionDetail?.products?.map((items) => items.id);
  //       const filterdProducts = actionData?.data?.products?.data?.filter(
  //         (product) => !existingProducts.includes(product.id),
  //       );
  //       setproducts(filterdProducts);
  //       setPageLoading(false);
  //     } else if (
  //       actionData?.type === "fetch-collection-details" &&
  //       actionData?.status === 200
  //     ) {
  //       shopify.toast.show(actionData?.message, { isError: true });
  //       setPageLoading(false);
  //     }
  //   }, [actionData]);

  //   useEffect(() => {
  //     if (actionData?.type === "delete-product" && actionData?.status === 200) {
  //       const productId = actionData?.data;
  //       setcollection((prev) => ({
  //         ...prev,
  //         products: prev.products.filter((product) => product.id !== productId),
  //       }));
  //       setdeleteLoading(false);
  //     } else if (
  //       actionData?.type === "delete-product" &&
  //       actionData?.status !== 200
  //     ) {
  //       setdeleteLoading(false);
  //       shopify.toast.show(actionData?.message, { isError: true });
  //     }
  //   }, [actionData]);

  //   useEffect(() => {
  //     if (actionData?.type === "add-products" && actionData?.status === 200) {
  //       setcollection((prevcollection) => ({
  //         ...prevcollection,
  //         products: [...prevcollection.products, ...actionData?.data],
  //       }));
  //       shopify.toast.show(actionData?.message);
  //       setAddProductLoading(false);
  //       handleProductModalClose();
  //     } else if (
  //       actionData?.type === "add-products" &&
  //       actionData?.status !== 200
  //     ) {
  //       shopify.toast.show(actionData?.message, { isError: true });
  //       setAddProductLoading(false);
  //     }
  //   }, [actionData]);

  // Filter options

  const handleScheduleUpdate = () => {
    scheduleUpdate({
      update: true,
    });
  };

  const [searchQuery, setSearchQuery] = useState("");
  const [filteredProducts, setFilteredProducts] = useState(
    collection?.products || [],
  );

  const handleSearchChange = useCallback((value) => {
    setSearchQuery(value.toLowerCase());
  }, []);

  useEffect(() => {
    if (!collection?.products) return;

    let filtered = collection.products || [];

    // Filter by stock status
    switch (selectedOption) {
      case "in-stock":
        filtered = filtered.filter((product) => product.totalInventory > 0);
        break;
      case "out-of-stock":
        filtered = filtered.filter((product) => product.totalInventory === 0);
        break;
      default:
        break;
    }

    // Filter by search query (title or price)
    if (searchQuery.trim() !== "") {
      filtered = filtered.filter(
        (product) =>
          product.title.toLowerCase().includes(searchQuery) ||
          product.price.toString().includes(searchQuery),
      );
    }

    setFilteredProducts(filtered);
  }, [selectedOption, searchQuery, collection]);

  const filterOptions =
    content.Rank_collections.product_details.filter_options.map((option) => ({
      label: option.label,
      value: option.value,
    }));

  const [selectedSortOptions, setSelectedSortOptions] = useState("");
  const handleChangeSortOrderOption = useCallback(
    (value) => setSelectedSortOptions(value),
    [],
  );
  const sortOrderOptions =
    content.Rank_collections.product_details.sort_order_options
      .map((item) => {
        // Normalize the value to match JSON keys
        const normalizedValue = item.value
          ?.trim()
          .toLowerCase()
          .replace(/_/g, " ");

        // Check if the option is disabled in feature flags
        const isDisabled =
          feature.product_details.sorting_options[normalizedValue] === false;

        // If it's disabled, return null; otherwise, return the normal option
        return isDisabled
          ? null
          : {
              label: item.label,
              value: item.value,
            };
      })
      .filter((item) => item !== null); // Remove null values

  // Manual sorting state and handlers
  const [manualProductPositions, setManualProductPositions] = useState({});
  const [positionError, setPositionError] = useState(null);
  const [productPreferences, setProductPreferences] = useState({});

  const validatePositions = useCallback(() => {
    if (selectedSortOptions !== "MANUAL") return true;

    const positionValues = Object.values(manualProductPositions);
    const uniquePositions = new Set(positionValues);

    if (uniquePositions.size !== positionValues.length) {
      setPositionError(
        "Duplicate position numbers found. Please ensure each product has a unique position.",
      );
      return false;
    }

    setPositionError(null);
    return true;
  }, [selectedSortOptions, manualProductPositions]);

  const handleManualProductPositionChange = useCallback(
    (productId, value) => {
      const numValue = Number(value);

      if (
        !isNaN(numValue) &&
        numValue >= 1 &&
        numValue <= filteredProducts.length
      ) {
        setManualProductPositions((prev) => ({
          ...prev,
          [productId]: numValue,
        }));
        // Clear any preference when manually changing position
        setProductPreferences((prev) => {
          const newPreferences = { ...prev };
          delete newPreferences[productId];
          return newPreferences;
        });
      }
    },
    [filteredProducts.length],
  );

  const handlePositionPreference = useCallback(
    (productId, preference) => {
      setProductPreferences((prev) => {
        // Clear all existing preferences
        const newPreferences = {};

        // If the clicked product already has this preference, remove it
        if (prev[productId] === preference) {
          return newPreferences;
        }

        // Set the new preference for the clicked product
        newPreferences[productId] = preference;
        return newPreferences;
      });

      setManualProductPositions((prev) => {
        const newPositions = { ...prev };
        if (preference === "top") {
          newPositions[productId] = 1;
          // Adjust other positions
          Object.keys(prev).forEach((id) => {
            if (id !== productId && prev[id] < prev[productId]) {
              newPositions[id] = prev[id] + 1;
            }
          });
        } else if (preference === "bottom") {
          newPositions[productId] = filteredProducts.length;
          // Adjust other positions
          Object.keys(prev).forEach((id) => {
            if (id !== productId && prev[id] > prev[productId]) {
              newPositions[id] = prev[id] - 1;
            }
          });
        }
        return newPositions;
      });
    },
    [filteredProducts.length],
  );

  const handleSortUpdate = useCallback(() => {
    if (selectedSortOptions === "MANUAL") {
      if (!validatePositions()) return;

      const repositionedProducts = filteredProducts.map((product) => ({
        id: product.id,
        position: manualProductPositions[product.id] || 0,
      }));

      // Sort based on position preference
      if (selectedSortOptions === "MANUAL") {
        repositionedProducts.sort((a, b) => a.position - b.position);
      } else {
        repositionedProducts.sort((a, b) => a.position - b.position);
      }

      sortUpdate({
        sort: selectedSortOptions,
        id: collection.collectionId,
        repositionedProducts,
      });
    } else {
      sortUpdate({
        sort: selectedSortOptions,
        id: collection.collectionId,
      });
    }
  }, [
    selectedSortOptions,
    collection,
    filteredProducts,
    manualProductPositions,
    sortUpdate,
    validatePositions,
  ]);

  // Initialize manual positions when sort option changes
  useEffect(() => {
    if (selectedSortOptions === "MANUAL" && filteredProducts?.length > 0) {
      const initialPositions = {};
      filteredProducts.forEach((product, index) => {
        initialPositions[product.id] = index + 1;
      });
      setManualProductPositions(initialPositions);
    }
  }, [selectedSortOptions, filteredProducts]);

  // Set initial sort option value from collection
  useEffect(() => {
    setSelectedSortOptions(collection?.sortValue);
  }, [collection]);

  // Child component with improved state management
  const containerRef = useRef(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const isScrollFetchingRef = useRef(false);
  const [canFetch, setCanFetch] = useState(true);
  const [updatedProducts, setUpdatedProducts] = useState([]);

  useEffect(() => {
    if (!productLoading && Array.isArray(products) && products.length > 0) {
      setUpdatedProducts((prev) => {
        const existingIds = new Set(prev.map((product) => product.id));
        const newProducts = products.filter(
          (product) => !existingIds.has(product.id),
        );
        return [...prev, ...newProducts];
      });

      // Stop fetching when products are updated
      setIsLoadingMore(false);
      isScrollFetchingRef.current = false;
      setCanFetch(true);
    }
  }, [products, productLoading]);

  // Reset states on mount/unmount
  useEffect(() => {
    return () => {
      isScrollFetchingRef.current = false;
      setIsLoadingMore(false);
    };
  }, []);

  // Improved scroll handler
  const handleScroll = () => {
    if (
      !containerRef.current ||
      !canFetch ||
      isLoadingMore ||
      productLoading ||
      isScrollFetchingRef.current
    )
      return;

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;

    if (scrollHeight - scrollTop - clientHeight < 100) {
      setIsLoadingMore(true);
      isScrollFetchingRef.current = true;
      setCanFetch(false);

      // Trigger fetch without .then(), rely on useEffect to update state
      addProductFetch();
    }
  };

  const handleProductModalClose = () => {
    // Reset modal-related states
    setProductModalOpen(false);
    setSelectedProducts([]);

    // Reset scroll fetching states
    setIsLoadingMore(false);
    isScrollFetchingRef.current = false;
    setCanFetch(true);
  };

  return (
    <Modal open={open} onHide={hide} variant="max">
      <Box style={{ padding: "10px" }}>
        <Page>
          <BlockStack gap="500">
            <InlineStack align="space-between" blockAlign="center">
              <BlockStack gap="100">
                {!loading ? (
                  <>
                    <Text variant="headingLg" as="h6" fontWeight="semibold">
                      {collection?.title}
                    </Text>
                    <Text variant="bodyMd" as="p" fontWeight="subdued">
                      {collection?.products?.length}{" "}
                      {content.Rank_collections.product_details.content_1}{" "}
                      {content.Rank_collections.product_details.content_2}{" "}
                      {humanReadableTime(collection.updatedAt)}
                    </Text>
                  </>
                ) : (
                  <Spinner />
                )}
              </BlockStack>

              {/* <Button
                icon={ClockIcon}
                variant="primary"
                size="large"
                onClick={
                  !automatic ? handleUpgradeModalOpen : handleScheduleUpdate
                }
              >
                {content.Rank_collections.product_details.button_1}
              </Button> */}
            </InlineStack>

            <Grid columns={{ xs: 1, sm: 3, md: 3, lg: 3, xl: 3 }} gap="500">
              <Card>
                <BlockStack gap="200">
                  <Text variant="bodyLg" as="p" fontWeight="subdued">
                    {content.Rank_collections.product_details.content_4}
                  </Text>
                  <Text variant="headingLg" as="h5" fontWeight="semibold">
                    {collection?.views === null ? 0 : collection?.views}
                  </Text>
                  {/* <InlineStack gap="100" blockAlign="center">
                <Box>
                  <Icon source={ArrowDiagonalIcon} tone="success" />
                </Box>
                <Text variant="bodyLg" as="p" tone="success">
                  +12% {content.Rank_collections.product_details.content_5}
                </Text>
              </InlineStack> */}
                </BlockStack>
              </Card>

              <Card width="100%">
                <BlockStack gap="200">
                  <Text variant="bodyLg" as="p" fontWeight="subdued">
                    {content.Rank_collections.product_details.heading_1}
                  </Text>
                  <Text variant="headingLg" as="h5" fontWeight="semibold">
                    {inlineStackProducts}
                  </Text>
                  {/* <InlineStack gap="100" blockAlign="center">
                  <Box>
                    <Icon source={ArrowDiagonalIcon} tone="success" />
                  </Box>
                  <Text variant="bodyLg" as="p" tone="success">
                    +8.3% vs previous
                  </Text>
                </InlineStack> */}
                </BlockStack>
              </Card>

              <Card>
                <BlockStack gap="200">
                  <Text variant="bodyLg" as="p" fontWeight="subdued">
                    {content.Rank_collections.product_details.heading_2}
                  </Text>
                  <Text variant="headingLg" as="h5" fontWeight="semibold">
                    {outOfStockProduct}
                  </Text>
                  {/* <InlineStack gap="100" blockAlign="center">
                  <Box>
                    <Icon source={ArrowDiagonalIcon} tone="success" />
                  </Box>
                  <Text variant="bodyLg" as="p" tone="success">
                    +0.5% vs previous
                  </Text>
                </InlineStack> */}
                </BlockStack>
              </Card>
            </Grid>
            <Grid>
              <Grid.Cell columnSpan={{ xs: 8, sm: 8, lg: 8, md: 8 }}>
                <Box style={{ height: "100%" }}>
                  <Card>
                    <BlockStack gap="300">
                      <InlineStack align="space-between" blockAlign="center">
                        <Text variant="headingLg" as="h6" fontWeight="semibold">
                          {content.Rank_collections.product_details.heading_3}
                        </Text>
                        <TextField
                          prefix={<Icon source={SearchIcon} />}
                          placeholder={
                            content.Rank_collections.product_details
                              .placeholder_1
                          }
                          type="search"
                          value={searchQuery}
                          onChange={handleSearchChange} // Search handler
                        />
                      </InlineStack>

                      <InlineStack align="space-between" blockAlign="center">
                        <Select
                          options={filterOptions}
                          value={selectedOption}
                          onChange={handleOptionsChange}
                        />
                        {!addProductHide && (
                          <Button
                            variant="plain"
                            icon={PlusIcon}
                            onClick={handleProductModalOpen}
                          >
                            {content.Rank_collections.product_details.button_2}
                          </Button>
                        )}
                      </InlineStack>

                      {!loading ? (
                        <Card>
                          <Box
                            style={{
                              overflowY: "scroll",
                              scrollbarWidth: "thin",
                              maxHeight: "440px",
                            }}
                          >
                            {filteredProducts?.length > 0 ? (
                              filteredProducts.map((items, index) => (
                                <BlockStack gap="300" key={index}>
                                  {selectedSortOptions === "MANUAL" && (
                                    <>
                                      <InlineStack
                                        gap="200"
                                        blockAlign="center"
                                      >
                                        <Box
                                          style={{
                                            width: "100px",
                                            marginRight: "10px",
                                          }}
                                        >
                                          <TextField
                                            label=""
                                            value={String(
                                              manualProductPositions[
                                                items.id
                                              ] || index + 1,
                                            )}
                                            type="number"
                                            min={1}
                                            max={filteredProducts?.length}
                                            onChange={(value) =>
                                              handleManualProductPositionChange(
                                                items.id,
                                                value,
                                              )
                                            }
                                            autoComplete="off"
                                            labelHidden
                                          />
                                        </Box>

                                        <InlineStack
                                          gap="200"
                                          blockAlign="center"
                                        >
                                          <Button
                                            pressed={
                                              productPreferences[items.id] ===
                                              "top"
                                            }
                                            onClick={() =>
                                              handlePositionPreference(
                                                items.id,
                                                "top",
                                              )
                                            }
                                          >
                                            Always in Top
                                          </Button>
                                          <Button
                                            pressed={
                                              productPreferences[items.id] ===
                                              "bottom"
                                            }
                                            onClick={() =>
                                              handlePositionPreference(
                                                items.id,
                                                "bottom",
                                              )
                                            }
                                          >
                                            Always in Bottom
                                          </Button>
                                        </InlineStack>
                                      </InlineStack>
                                    </>
                                  )}
                                  <InlineStack
                                    align="space-between"
                                    blockAlign="center"
                                    wrap={false}
                                  >
                                    <BlockStack gap="200">
                                      <Text
                                        variant="bodyMd"
                                        as="p"
                                        fontWeight="semibold"
                                      >
                                        {items.title}
                                      </Text>
                                      <Text
                                        variant="bodyMd"
                                        as="p"
                                        fontWeight="subdued"
                                      >
                                        ${items.price}, {items.totalInventory}{" "}
                                        {items.totalInventory > 0
                                          ? "in stock"
                                          : "out of stock"}
                                      </Text>
                                    </BlockStack>
                                    <InlineStack
                                      gap="400"
                                      blockAlign="center"
                                      wrap={false}
                                    >
                                      {/* <Badge tone="success">{content.Rank_collections.product_details.badge_1}</Badge> */}
                                      <Button
                                        icon={DeleteIcon}
                                        onClick={() =>
                                          handleProductDeleteModalOpen(items.id)
                                        }
                                        loading={deleteLoading}
                                      />
                                    </InlineStack>
                                  </InlineStack>
                                  <Divider />
                                </BlockStack>
                              ))
                            ) : (
                              <Text
                                variant="bodyMd"
                                as="p"
                                fontWeight="semibold"
                                alignment="center"
                              >
                                {
                                  content.Rank_collections.product_details
                                    .content_11
                                }
                              </Text>
                            )}
                          </Box>
                        </Card>
                      ) : (
                        <Spinner />
                      )}
                    </BlockStack>
                  </Card>
                </Box>
              </Grid.Cell>

              <Grid.Cell columnSpan={{ xs: 6, md: 4, lg: 4, sm: 4 }}>
                <BlockStack gap="300">
                  {feature.product_details.ai_recommendations_comming_soon && (
                    <Card background="bg-surface-magic">
                      <InlineStack gap="100" blockAlign="start" wrap={false}>
                        <Box>
                          <Icon source={WandIcon} />
                        </Box>
                        <BlockStack gap="200">
                          <Box>
                            <BlockStack gap="100">
                              <Text
                                variant="headingMd"
                                as="h6"
                                fontWeight="semibold"
                              >
                                {
                                  content.Rank_collections.product_details
                                    .heading_4
                                }
                              </Text>

                              <Text
                                variant="bodyLg"
                                as="p"
                                fontWeight="subdued"
                              >
                                {
                                  content.Rank_collections.product_details
                                    .content_6
                                }
                              </Text>
                            </BlockStack>
                          </Box>
                          {feature.product_details
                            .ai_recommendations_comming_soon ? (
                            <>
                              {" "}
                              <Card>
                                <BlockStack gap="100">
                                  <Text
                                    variant="headingMd"
                                    as="h6"
                                    fontWeight="semibold"
                                  >
                                    {
                                      content.Rank_collections.product_details
                                        .heading_5
                                    }
                                  </Text>
                                  <Text variant="bodyLg" as="p">
                                    {
                                      content.Rank_collections.product_details
                                        .content_7
                                    }
                                  </Text>
                                  <Text variant="bodyMd" as="p">
                                    {
                                      content.Rank_collections.product_details
                                        .content_8
                                    }
                                  </Text>
                                </BlockStack>
                              </Card>
                              <Card>
                                <BlockStack gap="100">
                                  <Text
                                    variant="headingMd"
                                    as="h6"
                                    fontWeight="semibold"
                                  >
                                    {
                                      content.Rank_collections.product_details
                                        .heading_6
                                    }
                                  </Text>
                                  <Text variant="bodyMd" as="p">
                                    {
                                      content.Rank_collections.product_details
                                        .content_9
                                    }
                                  </Text>
                                </BlockStack>
                              </Card>
                              <Card>
                                <BlockStack gap="100">
                                  <Text
                                    variant="headingMd"
                                    as="h6"
                                    fontWeight="semibold"
                                  >
                                    {
                                      content.Rank_collections.product_details
                                        .heading_7
                                    }
                                  </Text>
                                  <Text variant="bodyMd" as="p">
                                    {
                                      content.Rank_collections.product_details
                                        .content_10
                                    }
                                  </Text>
                                </BlockStack>
                              </Card>{" "}
                            </>
                          ) : (
                            <Box style={{ height: "300px" }}>
                              <InlineStack align="center" blockAlign="center">
                                {!feature.product_details
                                  .ai_recommendations_comming_soon && (
                                  <Text
                                    variant="headingMd"
                                    as="h6"
                                    fontWeight="sembold"
                                    tone="critical"
                                  >
                                    {
                                      content.Rank_collections.product_details
                                        .heading_9
                                    }
                                  </Text>
                                )}
                              </InlineStack>
                            </Box>
                          )}
                        </BlockStack>
                      </InlineStack>
                    </Card>
                  )}

                  {feature.product_details.ai_recommendations_comming_soon && (
                    <Card>
                      <BlockStack gap="100" aria-expanded={true}>
                        <InlineStack gap="300" blockAlign="center">
                          <Text
                            variant="headingMd"
                            as="h6"
                            fontWeight="semibold"
                          >
                            {content.Rank_collections.product_details.heading_8}
                          </Text>
                          {!feature.product_details.comming_soon && (
                            <Text
                              variant="headingMd"
                              as="h6"
                              fontWeight="semibold"
                              tone="critical"
                            >
                              {
                                content.Rank_collections.product_details
                                  .heading_9
                              }
                            </Text>
                          )}
                        </InlineStack>
                        {content.Rank_collections.product_details.collection_settings.map(
                          (attribute, index) =>
                            feature.product_details.comming_soon ? (
                              <Checkbox
                                key={index}
                                label={
                                  <Text variant="bodyLg" as="p">
                                    {attribute.label}
                                  </Text>
                                }
                                checked={selectedCollectionSettings.includes(
                                  attribute.label,
                                )}
                                onChange={() =>
                                  handleCollectionSettingsChange(
                                    attribute.label,
                                  )
                                }
                              />
                            ) : (
                              <Box style={{ height: "20px" }}></Box>
                            ),
                        )}
                      </BlockStack>
                    </Card>
                  )}
                </BlockStack>

                {collection?.status === "published" &&
                  feature.collection_drafts.sort_order_show && (
                    <BlockStack gap="300">
                      <Card sectioned>
                        <BlockStack gap="300">
                          <Text
                            variant="headingLg"
                            as="h6"
                            fontWeight="semibold"
                          >
                            {
                              content.Rank_collections.product_details
                                .heading_10
                            }
                          </Text>
                          <Select
                            label={
                              content.Rank_collections.product_details.label_1
                            }
                            placeholder={
                              content.Rank_collections.product_details
                                .placeholder_2
                            }
                            options={sortOrderOptions}
                            value={selectedSortOptions}
                            onChange={handleChangeSortOrderOption}
                          />

                          {positionError &&
                            selectedSortOptions === "MANUAL" && (
                              <Text tone="critical">{positionError}</Text>
                            )}

                          <Button
                            variant="primary"
                            onClick={handleSortUpdate}
                            loading={sortLoading}
                          >
                            {content.Rank_collections.product_details.button_3}
                          </Button>
                        </BlockStack>
                      </Card>
                    </BlockStack>
                  )}
              </Grid.Cell>
            </Grid>
          </BlockStack>

          <Modal
            open={productDeleteModalOpen}
            onHide={handleProductDeleteModalClose}
          >
            <TitleBar
              title={content.Rank_collections.product_details.modal_title_1}
            >
              <button
                variant="primary"
                tone="critical"
                onClick={() => handleDeleteSingleProduct(selectedIndex)}
              >
                {content.Rank_collections.product_details.modal_button_1}
              </button>
              <button onClick={handleProductDeleteModalClose}>
                {content.Rank_collections.product_details.modal_button_2}
              </button>
            </TitleBar>
            <Box style={{ padding: "10px" }}>
              <Text variant="bodyLg" as="p">
                {selectedIndex !== null &&
                collection?.products?.find(
                  (product) => product.id === selectedIndex,
                )
                  ? `${content.Rank_collections.product_details.modal_msg_1} "${
                      collection.products.find(
                        (product) => product.id === selectedIndex,
                      ).title
                    }"`
                  : `"${content.Rank_collections.product_details.modal_msg_2}"`}
              </Text>
            </Box>
          </Modal>

          <Modal open={productModalOpen} onHide={handleProductModalClose}>
            <TitleBar
              title={content.Rank_collections.product_details.modal_title_2}
            ></TitleBar>
            <Box
              ref={containerRef}
              style={{
                maxHeight: "400px",
                padding: "1rem",
                overflowY: "scroll",
                scrollbarWidth: "thin",
              }}
              onScroll={handleScroll}
            >
              <BlockStack gap="300">
                {updatedProducts.length > 0 ? (
                  updatedProducts.map((items, index) => (
                    <Card sectioned key={items.id}>
                      <InlineStack gap="300" blockAlign="center" wrap={false}>
                        <Text variant="headingMd" as="h6" fontWeight="semibold">
                          {index + 1}.{" "}
                        </Text>
                        <Checkbox
                          label={
                            <InlineStack gap="300" blockAlign="center">
                              {" "}
                              <Thumbnail
                                source={items?.image ? items.image : FileIcon}
                                size="small"
                              />{" "}
                              <Text variant="bodyMd" as="p" fontWeight="bold">
                                {items.title}
                              </Text>
                            </InlineStack>
                          }
                          checked={selectedProduct.includes(items.id)}
                          onChange={() => handleSelectProducts(items.id)}
                        />
                      </InlineStack>
                    </Card>
                  ))
                ) : (
                  <Text variant="bodyMd" as="p" alignment="center">
                    No products available
                  </Text>
                )}

                {productLoading && (
                  <Box padding="300" alignment="center">
                    <Spinner
                      accessibilityLabel="Loading more products"
                      size="small"
                    />
                    <Text variant="bodySm" as="p" alignment="center">
                      Loading more products...
                    </Text>
                  </Box>
                )}
              </BlockStack>
            </Box>
            <Box
              style={{
                height: "3rem",
                backgroundColor: "rgba(128, 128, 128, 0.1)",
                padding: "10px",
              }}
            >
              <InlineStack gap="300" align="end" blockAlign="center">
                <Button onClick={handleProductModalClose}>
                  {content.Rank_collections.product_details.modal_button_3}
                </Button>
                <Button
                  variant="primary"
                  onClick={handleAddProducts}
                  loading={addProductLoading}
                >
                  {content.Rank_collections.product_details.modal_button_4}
                </Button>
              </InlineStack>
            </Box>
          </Modal>

          <Modal open={upgradeModalOpen} onHide={handleUpgradeModalClose}>
            <TitleBar title={content.Rank_collections.home.modal_heading_4}>
              <button onClick={() => navigate("/app/plans")} variant="primary">
                {content.Rank_collections.home.modal_button_1}
              </button>
            </TitleBar>
            <Box style={{ padding: "10px" }}>
              <Text variant="bodyMd" as="p">
                {content.Rank_collections.home.modal_content_3}
              </Text>
            </Box>
          </Modal>
        </Page>
      </Box>
    </Modal>
  );
}
