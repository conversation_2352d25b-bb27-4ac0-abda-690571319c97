import React from "react";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  BlockStack,
  InlineStack,
  Box,
  Button,
  Text,
  Card,
} from "@shopify/polaris";

export default function CollectionScheduleModal({
  open,
  hide,
  onUpdate,
  loading,
  configs,
  from,
  domain,
  manualCreatePayload,
  manualLoading,
  syncUrl,
  syncLoading,
  isUrlSynced,
}) {
  const [currentStep, setCurrentStep] = React.useState(1);

  const handleNextStep = () => setCurrentStep((prev) => prev + 1);

  return (
    <Modal
      open={open}
      onClose={hide}
      title="Generate Collections"
      primaryAction={{
        content: "Continue",
        onAction: handleNextStep,
      }}
      secondaryActions={[
        {
          content: "Cancel",
          onAction: hide,
        },
      ]}
    >
      <BlockStack gap="400">
        <Text variant="headingLg">Step {currentStep} of 3</Text>
        <Card>
          <BlockStack gap="400">
            <Text variant="headingMd">Choose Generation Method</Text>
          </BlockStack>
        </Card>
      </BlockStack>
    </Modal>
  );
}
