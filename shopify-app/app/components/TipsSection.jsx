import React from 'react';
import {
    Card,
    BlockStack,
    InlineStack,
    Text,
    Link
} from '@shopify/polaris';
import content from '../locales/en.json';
import {
    bestPracticesLink,
    seoLink,
    collectionMetricsLink,
} from "../configs/config"; // Assuming config is accessible here

export default function TipsSection({ featureFlags }) {

    const TipCard = ({ title, linkUrl, linkText }) => (
        <Card sectioned>
            <BlockStack gap="200">
                <Text variant="headingLg" as="h6" fontWeight="semibold">
                    {title}
                </Text>
                <Link url={linkUrl} target="_blank" removeUnderline>
                    {linkText}
                </Link>
            </BlockStack>
        </Card>
    );

    return (
        <Card sectioned>
            <BlockStack gap="400">
                <InlineStack gap="200" blockAlign="center">
                    <Text variant="bodyLg" as="p" fontWeight="semibold">
                        &#x1F4A1; {/* Lightbulb Emoji */}
                    </Text>
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                        {content.Rank_collections.home.heading_7}
                    </Text>
                </InlineStack>

                <TipCard
                    title={content.Rank_collections.home.heading_8}
                    linkUrl={bestPracticesLink}
                    linkText={content.Rank_collections.home.link_content_1}
                />

                <TipCard
                    title={content.Rank_collections.home.heading_9}
                    linkUrl={seoLink}
                    linkText={content.Rank_collections.home.link_content_2}
                />

                {featureFlags?.home?.collection_metrics_link && (
                    <TipCard
                        title={content.Rank_collections.home.heading_10}
                        linkUrl={collectionMetricsLink}
                        linkText={content.Rank_collections.home.link_content_3}
                    />
                )}
            </BlockStack>
        </Card>
    );
} 