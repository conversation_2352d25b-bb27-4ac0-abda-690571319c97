import React from "react";
import {
  Card,
  BlockStack,
  InlineStack,
  Box,
  Text,
  SkeletonDisplayText,
  Icon,
  Tooltip,
} from "@shopify/polaris";
import { HomeIcon, InfoIcon } from "@shopify/polaris-icons";
import content from "../locales/en.json"; // Assuming content is accessible here

const MetricItem = ({
  title,
  value,
  isLoading,
  valuePrefix = "",
  valueSuffix = "",
  icon = null,
}) => (
  <BlockStack gap="100" inlineAlign="start">
    <InlineStack gap="100" blockAlign="center">
      <Box style={{ cursor: "pointer" }}>{icon && icon}</Box>
      <Text variant="bodySm" as="p" tone="subdued">
        {title}
      </Text>
    </InlineStack>
    {isLoading ? (
      <Box style={{ width: "80px", marginTop: "4px" }}>
        <SkeletonDisplayText size="small" />
      </Box>
    ) : (
      <Text variant="headingXl" as="p" fontWeight="semibold">
        {" "}
        {/* Larger text for value */}
        {valuePrefix}
        {value ?? "N/A"}
        {valueSuffix}
      </Text>
    )}
  </BlockStack>
);

export default function KeyMetricsDisplay({
  productCount,
  opportunity,
  conversionRate,
  isLoading,
  metricsLoading,
}) {
  return (
    <Card sectioned>
      {" "}
      {/* Wrap all metrics in a single card */}
      <InlineStack
        gap="600"
        align="space-between"
        blockAlign="start"
        wrap={false}
      >
        {" "}
        {/* Distribute metrics horizontally */}
        <MetricItem
          title={content.Rank_collections.home.heading_2} // "Products Synced"
          value={productCount}
          isLoading={isLoading}
          icon={
            <Box style={{ cursor: "default" }}>
              <Icon source={HomeIcon} />
            </Box>
          }
        />
        <MetricItem
          title={content.Rank_collections.home.heading_3} // "Opportunity Score"
          value={opportunity}
          isLoading={isLoading || metricsLoading}
          icon={
            <Tooltip
              content={content.Rank_collections.home.info_content}
              width="wide"
              padding="500"
            >
              <Box style={{ cursor: "pointer" }}>
                <Icon source={InfoIcon} />
              </Box>
            </Tooltip>
          }
        />
        <MetricItem
          title={content.Rank_collections.home.heading_4} // "Est. Conversion Boost"
          value={conversionRate}
          valuePrefix={conversionRate ? "+" : ""}
          valueSuffix={conversionRate ? "%" : ""}
          isLoading={isLoading || metricsLoading}
          icon={
            <Tooltip
              content={content.Rank_collections.home.info_content_1}
              width="wide"
              padding="500"
            >
              <Box style={{ cursor: "pointer" }}>
                <Icon source={InfoIcon} />
              </Box>
            </Tooltip>
          }
        />
      </InlineStack>
    </Card>
  );
}
