import { formatTime, formatDate } from "../../../helper/formatDateAndTime";
import {
  Icon,
  Box,
  Button,
  Text,
  InlineStack,
  Divider,
  BlockStack,
} from "@shopify/polaris";
import { ExternalIcon } from "@shopify/polaris-icons";
import content from "../../../locales/en.json";

export default function PublishedCollectionsInfo({ data, store }) {
  const handleCollectionNavigate = (url) => {
    window.open(`https://${store}/collections${url}`, "_blank");
  };

  const renderListItem = (collection, index, totalItems) => (
    <Box key={index}>
      <BlockStack gap="200">
        <InlineStack align="space-between" blockAlign="start">
          <BlockStack gap="100">
            <Text variant="bodyMd" as="p" fontWeight="regular">
              {collection.collection_name}
            </Text>
            <Text variant="bodySm" as="p" color="subdued">
              {content.Rank_collections.dashboard.heading_10}
              {collection.collection_url}
            </Text>
          </BlockStack>

          <InlineStack gap="400" align="end" blockAlign="center" wrap={false}>
            <Text variant="bodySm" as="p" color="subdued" alignment="end">
              {formatDate(collection.published_time)}
            </Text>
            <Button
              plain
              icon={ExternalIcon}
              onClick={() =>
                handleCollectionNavigate(collection.collection_url)
              }
              accessibilityLabel={`View ${collection.collection_name}`}
            />
          </InlineStack>
        </InlineStack>
        <Divider />
      </BlockStack>
      <Box style={{ marginBottom: "1rem" }}></Box>
    </Box>
  );

  const renderEmptyState = () => (
    <Box paddingBlockStart="800" paddingBlockEnd="800" paddingInline="400">
      <Text alignment="center" color="subdued">
        {content.Rank_collections.dashboard.empty_content_1}
      </Text>
    </Box>
  );

  const publishedCollections =
    data?.filter((status) => status.status === "published") ?? [];

  return (
    <BlockStack gap="0">
      {publishedCollections.length === 0
        ? renderEmptyState()
        : publishedCollections.map((collection, index) =>
            renderListItem(collection, index, publishedCollections.length),
          )}
    </BlockStack>
  );
}
