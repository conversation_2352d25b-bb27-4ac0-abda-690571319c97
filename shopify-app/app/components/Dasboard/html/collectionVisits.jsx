import { Text, Box, InlineStack, BlockStack } from "@shopify/polaris";
import content from "../../../locales/en.json";

export default function CollectionsVisits({ data }) {
  // Styling for individual list items
  const renderListItem = (collection, index, totalItems) => (
    <Box key={index}>
      <InlineStack align="space-between" blockAlign="start" wrap={false}>
        <BlockStack gap="050">
          <Text variant="bodyMd" as="p" fontWeight="regular">
            {collection.collection_name}
          </Text>{" "}
          {/* Regular weight */}
          <Text variant="bodySm" as="p" color="subdued">
            {collection.collection_url}
          </Text>
        </BlockStack>
        <Text variant="bodyMd" as="p" numeric fontWeight="medium">
          {collection.view_count}
        </Text>{" "}
        {/* Right align count */}
      </InlineStack>
      <Box style={{ marginBottom: "16px" }}></Box>
    </Box>
  );

  const renderEmptyState = () => (
    <Box paddingBlockStart="800" paddingBlockEnd="800" paddingInline="400">
      {" "}
      {/* Add padding */}
      <Text alignment="center" color="subdued">
        {content.Rank_collections.dashboard.empty_content_2}
      </Text>
    </Box>
  );

  const visitedCollections =
    data?.filter(
      (status) => status.status === "published" && status.view_count > 0,
    ) ?? [];

  return (
    <BlockStack gap="0">
      {" "}
      {/* Remove gap if using dividers in items */}
      {/* Header is removed as it's handled in parent */}
      {visitedCollections.length === 0
        ? renderEmptyState()
        : visitedCollections.map((collection, index) =>
            renderListItem(collection, index, visitedCollections.length),
          )}
    </BlockStack>
  );
}
