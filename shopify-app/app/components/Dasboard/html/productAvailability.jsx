import { Text, Box, InlineStack } from "@shopify/polaris";

import contents from '../../../locales/en.json'

export default function ProductsAvailability({data, dataTwo, content}) {
  return (
    <Box className="dashboard-four__main-container">
      <Text variant="headingLg" as="h5" fontWeight="semibold" tone='magic-subdued'>{contents.dashboard.heading_eight}</Text>
      <InlineStack align="space-between" blockAlign="stretch" gap='100' wrap={true}>
        <Box style={{backgroundColor:'rgba(128, 128, 128, 0.1)', padding:'1rem', borderRadius:'0.5rem', width:'49%', height:'100%'}}>
          <InlineStack align="start" gap='200'>
            <Box>
              <img
                src="https://ik.imagekit.io/1tudtg11f/eybxfg1qzz9m165kzkf.png?updatedAt=1726622730082"
                alt="section one image"
                className="section-one__image"
              />
            </Box>
            <Text variant="bodyMd" as="p" fontWeight="medium">
              {contents.Rank_collections.dashboard.content_3}
            </Text>
          </InlineStack>
          <Text variant="headingLg" as="h5" fontWeight="semibold" alignment="center">{data}</Text>
        </Box>
        <Box style={{backgroundColor:'rgba(128, 128, 128, 0.1)', padding:'1rem', borderRadius:'0.5rem', width:'48%', height:'100%'}}>
          <InlineStack align="start" gap='200'>
            <Box>
              <img
                src="https://ik.imagekit.io/1tudtg11f/ndewuvdjh3sm15zto7j.png?updatedAt=1726622730068"
                alt="section two image"
                className="section-two__image"
              />
            </Box>
            <Text variant="bodyMd" as="p" fontWeight="medium">
              {contents.Rank_collections.dashboard.content_4}
            </Text>
          </InlineStack>
          <Text variant="headingLg" as="h5" fontWeight="semibold" alignment="center">{dataTwo}</Text>
        </Box>
      </InlineStack>
    </Box>
  );
}
