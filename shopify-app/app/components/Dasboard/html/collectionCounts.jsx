import {useEffect, useState} from 'react'
import { Text, Card, Grid, Badge, InlineStack, SkeletonDisplayText, Box } from '@shopify/polaris';

import content from '../../../locales/en.json'

export default function CollectionCounts({data, loading}) {
    
    const [generatedCollection, setGeneratedCollection] = useState()
    const [activeCollections, setActiveCollections] = useState()
    const [scheduledCollection, setScheduledCollection] = useState()
    const [conversionRate, setConversionRate] = useState();
    useEffect(() => {
      if(!loading){

        setGeneratedCollection(data.length)
        setActiveCollections(data.filter(status => status.status === 'published').length || 0)
        setScheduledCollection(data.filter(status => status.status === 'scheduled').length ||0)
        setConversionRate(0)
      }
    },[data, loading])
    return (
        <>
            <Grid>
      <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
        <Card sectioned>
          <Box style={{height:'80px'}}>
          <img src='https://ik.imagekit.io/1tudtg11f/Vector.jpg?updatedAt=1721895824380' alt='image' />
          
          <Text variant="bodyLg" as="p" fontWeight="bold">{content.Rank_collections.dashboard.heading_2}</Text>
          {!loading ? <Text variant="heading2xl" as="h3">{generatedCollection}</Text> : <SkeletonDisplayText />}
          </Box>
        </Card>
      </Grid.Cell>
      <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
        <Card sectioned>
          <Box style={{height:'80px'}}>
          <img src='https://ik.imagekit.io/1tudtg11f/Primary%20fill.jpg?updatedAt=1721895780054' alt='image' />
          <Text variant="bodyLg" as="p" fontWeight="bold">{content.Rank_collections.dashboard.heading_3}</Text>
          {!loading ? <Text variant="heading2xl" as="h3">{activeCollections}</Text> : <SkeletonDisplayText />}
          </Box>
        </Card>
      </Grid.Cell>
      <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
        <Card sectioned>
          <Box style={{height:'80px'}}>
          <img src='https://ik.imagekit.io/1tudtg11f/clock_minor.jpg?updatedAt=1721895824109' alt="image" />
          <Text variant="bodyLg" as="p" fontWeight="bold">{content.Rank_collections.dashboard.heading_4}</Text>
          {!loading ? <Text variant="heading2xl" as="h3">{scheduledCollection}</Text> : <SkeletonDisplayText />}
          </Box>
        </Card>
      </Grid.Cell>
      <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3, xl: 3 }}>
        <Card sectioned>
          <Box style={{height:'80px'}}>
        <InlineStack align='space-between' blockAlign='center'>

          <img src='https://ik.imagekit.io/1tudtg11f/behavior_minor.jpg?updatedAt=1721895824075' alt='image' />
          <Badge tone='info'>Launching soon</Badge>
          </InlineStack>
          <Text variant="bodyLg" as="p" fontWeight="bold">{content.Rank_collections.dashboard.heading_5}</Text>
          {!loading ? <Text variant="heading2xl" as="h3">{conversionRate}</Text> : <SkeletonDisplayText />}
          </Box>
        </Card>
      </Grid.Cell>
    </Grid>
        </>
    )
}