import { formatTime, formatDate } from "../../../helper/formatDateAndTime";
import {
  Text,
  Box,
  InlineStack,
  Divider,
  BlockStack,
  Icon,
} from "@shopify/polaris";
import { NotificationIcon, CheckCircleIcon } from "@shopify/polaris-icons";
import content from "../../../locales/en.json";

export default function NewerCollections({ data }) {
  const renderHeader = () => (
    <BlockStack gap="300">
      <Text variant="headingMd" as="h2" fontWeight="semibold">
        <InlineStack gap="200" blockAlign="center">
          <Box>
            <Icon source={NotificationIcon} color="highlight" />
          </Box>

          {content.Rank_collections.dashboard.heading_14}
        </InlineStack>
      </Text>

      <Divider />
    </BlockStack>
  );

  const renderListItem = (collection, index, totalItems) => (
    <Box style={{ padding: "10px 0" }} key={index}>
      <InlineStack align="space-between" gap="300" wrap={false}>
        <InlineStack gap="200" blockAlign="start" wrap={false}>
          <Box>
            <Icon source={CheckCircleIcon} color="success" />
          </Box>
          <BlockStack gap="100">
            <Text variant="bodyMd" as="p">
              {content.Rank_collections.dashboard.content_1}{" "}
              <strong>{collection.collection_name}</strong>{" "}
              {content.Rank_collections.dashboard.content_2}
            </Text>
          </BlockStack>
        </InlineStack>

        <Text variant="bodySm" as="p" color="subdued" alignment="end">
          {formatDate(collection.published_time)},{" "}
          {formatTime(collection.published_time)}
        </Text>
      </InlineStack>
    </Box>
  );

  const renderEmptyState = () => (
    <Box paddingBlockStart="800" paddingBlockEnd="800" paddingInline="400">
      <Text alignment="center" color="subdued">
        {content.Rank_collections.dashboard.empty_content_3}
      </Text>
    </Box>
  );

  const recentCollections =
    data
      ?.filter((collection) => collection.status === "published")
      .sort(
        (a, b) =>
          new Date(b.published_time).getTime() -
          new Date(a.published_time).getTime(),
      ) ?? [];

  return (
    <BlockStack gap="0">
      <Box
        style={{
          position: "sticky",
          top: "0",
          backgroundColor: "#FFF",
          zIndex: "100",
        }}
      >
        {renderHeader()}
      </Box>
      {recentCollections.length === 0
        ? renderEmptyState()
        : recentCollections.map((collection, index) =>
            renderListItem(collection, index, recentCollections.length),
          )}
    </BlockStack>
  );
}
