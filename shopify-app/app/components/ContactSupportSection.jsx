import React from 'react';
import {
    Card,
    Text,
    Link
} from '@shopify/polaris';
import content from '../locales/en.json';
import { ContactLink } from "../configs/config"; // Assuming config is accessible here

export default function ContactSupportSection() {
    return (
        <Card sectioned>
            <Text variant="bodyLg" as="span">
                {content.Rank_collections.home.content_4}{" "}
                <Link url={ContactLink} target="_blank">
                    {content.Rank_collections.home.link_content_4}
                </Link>{" "}
                {content.Rank_collections.home.content_5}
            </Text>
        </Card>
    );
} 