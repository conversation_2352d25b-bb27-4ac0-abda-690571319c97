import React from 'react';
import {
    Card,
    BlockStack,
    InlineStack, // Use InlineStack for label + status pairs
    Text,
    Badge, // Use Badge for status indicators
    Spinner, // Indicate polling/checking
    Divider, // Add Divider for visual separation
    Box, // Add Box to imports
} from '@shopify/polaris';

const StatusItem = ({ label, statusText, badgeTone, showSpinner = false }) => (
    <BlockStack gap="100">
        <Text variant="bodyMd" as="p" fontWeight="medium">{label}</Text>
        <Box>
            {showSpinner ? (
                <Spinner size="small" />
            ) : (
                <Badge tone={badgeTone}>{statusText}</Badge>
            )}
        </Box>
    </BlockStack>
);

export default function StatusOverview({
    manualGenTaskId,
    productSyncTaskId,
    isManualGenPolling,
    isProductSyncPolling,
    // scheduleStatus, // Commented out
    // automaticUpdate, // Commented out
}) {

    // Determine Generation Status
    let genStatusText = "Idle";
    let genBadgeTone = "success";
    let genShowSpinner = false;
    if (manualGenTaskId) {
        if (isManualGenPolling) {
            genStatusText = `Running (ID: ${manualGenTaskId.substring(0, 6)}...)`;
            genBadgeTone = "attention";
            genShowSpinner = true;
        } else {
            // If polling stopped but ID exists, means it likely finished recently (or failed)
            // We might not have the final status here, Polling hook shows the toast
            // Displaying ID might be confusing if it failed/completed.
            genStatusText = "Recently Active"; // Or just "Idle"?
            genBadgeTone = "info";
        }
    } // We could add more states like Failed/Completed if hooks expose them

    // Determine Sync Status
    let syncStatusText = "Idle";
    let syncBadgeTone = "success";
    let syncShowSpinner = false;
    if (productSyncTaskId) {
        if (isProductSyncPolling) {
            syncStatusText = `Running (ID: ${productSyncTaskId.substring(0, 6)}...)`;
            syncBadgeTone = "attention";
            syncShowSpinner = true;
        } else {
            syncStatusText = "Recently Active";
            syncBadgeTone = "info";
        }
    }

    // Determine Schedule Status (Commented out)
    // let scheduleStatusText = "Inactive";
    // let scheduleBadgeTone = "critical";
    // if (!automaticUpdate) {
    //     scheduleStatusText = "Upgrade Required";
    //     scheduleBadgeTone = "warning";
    // } else if (scheduleStatus) {
    //     scheduleStatusText = "Active";
    //     scheduleBadgeTone = "success";
    // }

    return (
        <Card title="System Status" sectioned>
            <InlineStack gap="400" align="space-between" blockAlign="start" wrap={false}>
                <StatusItem
                    label="Manual Collection Generation"
                    statusText={genStatusText}
                    badgeTone={genBadgeTone}
                    showSpinner={genShowSpinner}
                />
                <Divider vertical />
                <StatusItem
                    label="Product Sync"
                    statusText={syncStatusText}
                    badgeTone={syncBadgeTone}
                    showSpinner={syncShowSpinner}
                />
                {/* Commented out Scheduling Status */}
                {/*
                <Divider vertical />
                <StatusItem
                    label="Automatic Scheduling"
                    statusText={scheduleStatusText}
                    badgeTone={scheduleBadgeTone}
                />
                */}
            </InlineStack>
        </Card>
    );
} 