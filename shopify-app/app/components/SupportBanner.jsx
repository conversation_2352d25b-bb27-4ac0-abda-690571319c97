import React from 'react';
import { <PERSON>, <PERSON>, Button, InlineStack, Text } from '@shopify/polaris';
import content from '../locales/en.json'

export default function SupportBanner({ contactLink, onDismiss }) {
    // Define benefits clearly
    const benefits = content.Rank_collections.home.benefits.map((item) => ({
        bold: item.bold,
        content: item.content,
    }))

    return (
        <Banner
            title={content.Rank_collections.home.badge_1}
            status="info"
            onDismiss={onDismiss} // Optional: Allow dismissing the banner
        >
            <p>{content.Rank_collections.home.heading_11}</p>
            <ul style={{ listStyle: 'disc', marginLeft: '20px', marginBottom: '10px' }}>
                {benefits.map((benefit, index) => (
                    
                        <li key={index}>
                            <InlineStack key={index} gap='100' blockAlign='center'>
                                <Text as='span' variant='bodyMd' fontWeight='bold'>
                                    {benefit.bold}
                                </Text>
                                <Text as='span' variant='bodyMd'>
                                    {benefit.content}
                                </Text>
                            </InlineStack>
                        </li>

                    
                ))}
            </ul>
            <Button
                variant="primary"
                url={contactLink}
                target="_blank"
                onClick={() => {
                    // Optional: Track click event if analytics is set up
                    // trackButtonClick('Support Banner CTA Clicked', 'Home Page');
                }}
            >
                Chat with Support
            </Button>
        </Banner>
    );
} 