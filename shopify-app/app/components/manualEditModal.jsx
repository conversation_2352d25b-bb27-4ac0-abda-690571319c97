import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { useEffect, useState, useCallback } from "react";
import {
  TextField,
  Text,
  Box,
  InlineStack,
  BlockStack,
  Card,
  Button,
  Icon,
} from "@shopify/polaris";
import { DeleteIcon, ClipboardIcon } from "@shopify/polaris-icons";

import feature from "../configs/featureFlag.json";
import content from "../locales/en.json";

export default function IndividualEditCollection({
  open,
  hide,
  name,
  url,
  description,
  domain,
  loading,
  collectionFaq,
  status,
  onUpdate,
}) {
  

  const [collectionName, setCollectionName] = useState("");
  const [collectionUrl, setCollectionUrl] = useState("");
  const [collectionDescription, setCollectionDescription] = useState("");
  const [faqCards, setFaqCards] = useState([]);

  const [selectedIndex, setSelectedIndex] = useState(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);

  const handleCollectionNameChange = useCallback(
    (value) => setCollectionName(value),
    [],
  );
  const handleCollectionUrlChange = useCallback(
    (value) => setCollectionUrl(value),
    [],
  );
  const handleCollectionDescriptionChange = useCallback(
    (value) => setCollectionDescription(value),
    [],
  );

  useEffect(() => {
    setCollectionName(name);
    setCollectionUrl(url);
    setCollectionDescription(description);
  }, [name, url, description]);

  useEffect(() => {
    // Debug logs to understand the incoming data
  

    // If collectionFaq is undefined, null, or empty string, set empty array and return
    if (!collectionFaq || collectionFaq === "") {
      setFaqCards([]);
      return;
    }

    try {
      // If it's already an array, use it directly
      if (Array.isArray(collectionFaq)) {
        setFaqCards(collectionFaq);
        return;
      }

      // If it's a string, try to parse it
      let parsedData;
      try {
        parsedData = JSON.parse(collectionFaq);
      } catch (parseError) {
        console.error("Parse error details:", {
          error: parseError,
          receivedValue: collectionFaq,
        });
        setFaqCards([]);
        return;
      }

      // If successfully parsed, check if it's an array
      if (Array.isArray(parsedData)) {
        setFaqCards(parsedData);
      } else {
        console.warn("Parsed data is not an array:", parsedData);
        setFaqCards([]);
      }
    } catch (error) {
      console.error("General error in FAQ processing:", error);
      setFaqCards([]);
    }
  }, [collectionFaq]);

  const addFAQCard = () => {
    setFaqCards([...faqCards, { question: "", answer: "" }]);
  };

  const handleDeleteModalOpen = (index) => {
    setSelectedIndex(index);
    setDeleteModalOpen(true);
  };
  const handleDeleteModalClose = () => {
    setSelectedIndex(null);
    setDeleteModalOpen(false);
  };

  const removeFAQ = (indexToRemove) => {
    setFaqCards(faqCards.filter((_, index) => index !== indexToRemove));
    handleDeleteModalClose();
  };

  const handleQuestionChange = (index, value) => {
    const updatedFaqCards = [...faqCards];
    updatedFaqCards[index].question = value;
    setFaqCards(updatedFaqCards);
  };

  const handleAnswerChange = (index, value) => {
    const updatedFaqCards = [...faqCards];
    updatedFaqCards[index].answer = value;
    setFaqCards(updatedFaqCards);
  };

  const filteredFaqCards = faqCards.filter(
    (faq) => faq?.question?.trim() && faq?.answer?.trim(),
  );

  const handleUpdate = () => {
    onUpdate({
      name: collectionName,
      url: collectionUrl,
      description: collectionDescription,
      faqs:
        filteredFaqCards.length > 0 && status === "published"
          ? filteredFaqCards
          : null,
    });
  };

  const handleCopy = () => {
    const fullUrl = `https://${domain}/collections/${url}`;
    
    // Focus on the document first
    window.focus();
    
    // Try the Clipboard API with a focus safety check
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(fullUrl)
        .then(() => {
          
          shopify.toast.show("URL copied to clipboard")
          // Show success toast or notification here
        })
        .catch(error => {
          console.error('Clipboard API failed:', error);
          // Try the fallback approach
          secureClipboardCopy(fullUrl);
        });
    } else {
      // Fallback for browsers without Clipboard API
      secureClipboardCopy(fullUrl);
    }
  };
  
 

  return (
    <>
      <Modal
        open={open}
        onHide={hide}
        variant={feature.collection_drafts.faq_show ? "max" : "base"}
      >
        <TitleBar title={content.Rank_collections.collection_gallery.modal_heading_2}></TitleBar>
        <Box
          style={
            feature.collection_drafts.faq_show
              ? {
                  display: "flex",
                  flexDirection: "column",
                  height: "100vh",
                  width: "100%",
                  overflowY: "scroll",
                }
              : undefined
          }
        >
          <Box
            style={{
              padding: "10px", // Always applied
              ...(feature.collection_drafts.faq_show && {
                flex: 1,
                overflowY: "scroll",
              }),
            }}
          >
            <BlockStack gap="400">
              <TextField
                label={content.Rank_collections.collection_gallery.modal_heading_3}
                value={collectionName}
                onChange={handleCollectionNameChange}
              />
              <Box>
                <TextField
                  label={content.Rank_collections.collection_gallery.modal_heading_4}
                  value={collectionUrl}
                  onChange={handleCollectionUrlChange}
                />
                
                <InlineStack gap='300' blockAlign='center'>
                <Text variant="bodyMd" as="p" fontWeight="subdued">
                  url: {`https://${domain}/collections${url}`}
                </Text>
                
                <Box style={{cursor:'pointer'}} onClick={handleCopy}>
                  <Icon source={ClipboardIcon} />
                </Box>
                </InlineStack>
                
              </Box>
              <TextField
                label={content.Rank_collections.collection_gallery.modal_heading_5}
                multiline={6}
                value={collectionDescription}
                onChange={handleCollectionDescriptionChange}
              />
              {feature.collection_drafts.faq_show && (
                <Box style={{ marginBottom: "3rem" }}>
                  <Text variant="headingLg" as="h6" fontWeight="semibold">
                    Frequently Asked Questions
                  </Text>
                  {status === "published" ? (
                    faqCards.length > 0 ? (
                      faqCards.map((faq, index) => (
                        <>
                          <Card sectioned key={index}>
                            <Box>
                              <BlockStack gap="300">
                                <InlineStack gap="200" blockAlign="center">
                                  <TextField
                                    placeholder="Question: "
                                    readOnly
                                  />
                                  <Box style={{ flex: 1 }}>
                                    <TextField
                                      value={faq.question}
                                      onChange={(value) =>
                                        handleQuestionChange(index, value)
                                      }
                                    />
                                  </Box>
                                </InlineStack>
                                <InlineStack gap="200" blockAlign="start">
                                  <TextField placeholder="Answer:" readOnly />
                                  <Box style={{ flex: 1 }}>
                                    <TextField
                                      multiline={3}
                                      value={faq.answer}
                                      onChange={(value) =>
                                        handleAnswerChange(index, value)
                                      }
                                    />
                                  </Box>
                                </InlineStack>
                                <InlineStack align="end" blockAlign="center">
                                  <Button
                                    icon={DeleteIcon}
                                    variant="primary"
                                    tone="critical"
                                    onClick={() => handleDeleteModalOpen(index)}
                                  >
                                    Delete
                                  </Button>
                                </InlineStack>
                              </BlockStack>
                            </Box>
                          </Card>
                          <br />
                        </>
                      ))
                    ) : (
                      <Text
                        variant="headingLg"
                        as="h6"
                        fontWeight="semibold"
                        alignment="center"
                      >
                        Now you don’t have any FAQs. Please add some by clicking
                        the "Add FAQ" button below on the right.
                      </Text>
                    )
                  ) : (
                    <Text
                      variant="headingLg"
                      as="h5"
                      fontWeight="semibold"
                      alignment="center"
                    >
                      This Collection is not published
                    </Text>
                  )}
                </Box>
              )}
            </BlockStack>
          </Box>

          <Box
            style={{
              height: "3rem",
              backgroundColor: "rgba(128, 128, 128, 0.1)",
              width: "100%",
              padding: "10px",
              ...(feature.collection_drafts.faq_show && {
                position: "fixed",
                bottom: "0",
                left: "0",
              }),
            }}
          >
            <InlineStack align="end" blockAlign="center" gap="300">
              <Button onClick={hide}>Cancel</Button>
              {status === "published" &&
                feature.collection_drafts.faq_show === true && (
                  <Button tone="success" variant="primary" onClick={addFAQCard}>
                    Add FAQ
                  </Button>
                )}
              <Button
                variant="primary"
                onClick={handleUpdate}
                loading={loading}
              >
                {content.Rank_collections.collection_gallery.modal_button_5}
              </Button>
            </InlineStack>
          </Box>
        </Box>
      </Modal>

      {/* FAQ Delete Modal */}
      <Modal open={deleteModalOpen} onHide={handleDeleteModalClose}>
        <TitleBar title={`Confirm Deletion`}></TitleBar>
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyMd" as="span">
            Are you sure you want to delete the faq for:{" "}
            <Text variant="headingLg" as="span" fontWeight="semibold">
              {faqCards[selectedIndex]?.question}
            </Text>
          </Text>
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            padding: "10px",
          }}
        >
          <InlineStack gap="300" blockAlign="center" align="end">
            <Button onClick={handleDeleteModalClose}>Cancel</Button>
            <Button
              icon={DeleteIcon}
              variant="primary"
              tone="critical"
              onClick={() => removeFAQ(selectedIndex)}
            >
              Delete
            </Button>
          </InlineStack>
        </Box>
      </Modal>
    </>
  );
}
