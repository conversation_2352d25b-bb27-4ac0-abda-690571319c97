import React from 'react';
import {
    BlockStack,
    InlineStack,
    Box,
    Icon,
    Text,
    Button,
} from '@shopify/polaris';
import {
    WandIcon,
} from "@shopify/polaris-icons";
import content from '../locales/en.json';

// Renamed component focusing only on the Generate action
export default function GenerateAction({
    onGenerateClick,
    isGenerating
}) {
    return (
        // This component returns the content for the Generate action card
        <BlockStack gap="300">
            <InlineStack gap="200" blockAlign="center" wrap={false}>
                <Box>
                    <Icon source={WandIcon} />
                </Box>
                <Text variant="headingMd" as="h6" fontWeight="semibold">
                    {content.Rank_collections.home.heading_6} {/* "Generate New Collections" */}
                </Text>
            </InlineStack>
            <Text variant="bodyMd" as="p" tone="subdued">
                {content.Rank_collections.home.content_3}
            </Text>
            <Box paddingBlockStart="200">
                <Button
                    variant='primary'
                    onClick={onGenerateClick}
                    // disabled={isGenerating}
                >
                    {content.Rank_collections.home.button_2} {/* "Start Generating" or similar */}
                </Button>
            </Box>
        </BlockStack>
    );
} 