import React from 'react';
import {
    Card,
    BlockStack,
    InlineStack,
    Box,
    Text,
    SkeletonDisplayText,
} from '@shopify/polaris';
import content from '../locales/en.json'; // Assuming content is accessible here

const MetricCounter = ({ title, value, isLoading }) => (
    <BlockStack gap="050">
        <Text variant="bodyMd" as="p" tone="subdued">
            {title}
        </Text>
        {isLoading ? (
            <Box style={{ width: "80px", marginTop: '2px' }}>
                <SkeletonDisplayText size="small" />
            </Box>
        ) : (
            <Text variant="headingLg" as="p" fontWeight="semibold">
                {value}
            </Text>
        )}
    </BlockStack>
);

export default function DashboardMetricsCards({ productCount, opportunity, conversionRate, isLoading, metricsLoading }) {
    const pageLoading = isLoading; // Combine loading states if needed, or keep separate

    return (
        <InlineStack gap="500" align="space-between" blockAlign="start">
            <MetricCounter
                title={content.Rank_collections.home.heading_2}
                value={productCount}
                isLoading={pageLoading}
            />
            <MetricCounter
                title={content.Rank_collections.home.heading_3}
                value={opportunity ?? 'N/A'}
                isLoading={pageLoading || metricsLoading}
            />
            <MetricCounter
                title={content.Rank_collections.home.heading_4}
                value={conversionRate ? `+${conversionRate}%` : 'N/A'}
                isLoading={pageLoading || metricsLoading}
            />
        </InlineStack>
    );
} 