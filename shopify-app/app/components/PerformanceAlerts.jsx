import React, { useState, useCallback } from 'react';
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  Card,
  BlockStack,
  InlineStack,
  Box,
  Text,
  Button,
  Badge,
  Icon,
  Select,
  TextField,
  Checkbox,
  Divider,
  EmptyState,
} from '@shopify/polaris';
import {
  InfoIcon,
  CheckCircleIcon,
  SettingsIcon,
  HomeIcon
} from '@shopify/polaris-icons';

const AlertItem = ({ alert, onDismiss, onAction }) => {
  const getAlertIcon = (type) => {
    switch (type) {
      case 'critical': return InfoIcon;
      case 'warning': return InfoIcon;
      case 'success': return CheckCircleIcon;
      default: return HomeIcon;
    }
  };

  const getAlertTone = (type) => {
    switch (type) {
      case 'critical': return 'critical';
      case 'warning': return 'warning';
      case 'success': return 'success';
      default: return 'info';
    }
  };

  return (
    <Card>
      <Box padding="400">
        <InlineStack align="space-between" blockAlign="start">
          <InlineStack gap="300" blockAlign="start">
            <Box>
              <Icon source={getAlertIcon(alert.type)} tone={getAlertTone(alert.type)} />
            </Box>
            <BlockStack gap="200">
              <InlineStack gap="200" blockAlign="center">
                <Text variant="bodyMd" fontWeight="semibold">
                  {alert.title}
                </Text>
                <Badge tone={getAlertTone(alert.type)}>
                  {alert.type.charAt(0).toUpperCase() + alert.type.slice(1)}
                </Badge>
              </InlineStack>
              <Text variant="bodySm" color="subdued">
                {alert.description}
              </Text>
              <Text variant="bodySm" color="subdued">
                Collection: {alert.collectionName} • {alert.timestamp}
              </Text>
              {alert.metrics && (
                <InlineStack gap="400">
                  {Object.entries(alert.metrics).map(([key, value]) => (
                    <InlineStack key={key} gap="100" blockAlign="center">
                      <Text variant="bodySm" color="subdued">{key}:</Text>
                      <Text variant="bodySm" fontWeight="medium">{value}</Text>
                    </InlineStack>
                  ))}
                </InlineStack>
              )}
            </BlockStack>
          </InlineStack>

          <InlineStack gap="200">
            {alert.actionLabel && (
              <Button
                size="slim"
                onClick={() => onAction(alert)}
              >
                {alert.actionLabel}
              </Button>
            )}
            <Button
              size="slim"
              variant="plain"
              onClick={() => onDismiss(alert.id)}
            >
              Dismiss
            </Button>
          </InlineStack>
        </InlineStack>
      </Box>
    </Card>
  );
};

// Default settings fallback - moved outside component to avoid dependency issues
const defaultAlertSettings = {
  lowPerformanceThreshold: 30,
  highPerformanceThreshold: 80,
  outOfStockAlerts: true,
  lowInventoryThreshold: 10,
  frequency: 'daily',
  emailNotifications: true
};

const AlertSettings = ({ isOpen, onClose, settings, onSave }) => {
  const [localSettings, setLocalSettings] = useState(settings || defaultAlertSettings);

  // Update local settings when settings prop changes
  React.useEffect(() => {
    if (settings) {
      setLocalSettings(settings);
    } else {
      setLocalSettings(defaultAlertSettings);
    }
  }, [settings]);

  const handleSave = () => {
    onSave(localSettings);
    onClose();
  };

  const updateSetting = (key, value) => {
    setLocalSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Modal open={isOpen} onHide={onClose}>
      <TitleBar title="Alert Settings">
        <button variant="primary" onClick={handleSave}>
          Save Settings
        </button>
      </TitleBar>
      <Box padding="400">
        <BlockStack gap="400">
          <Text variant="headingMd">Performance Thresholds</Text>

          <InlineStack gap="400">
            <Box style={{ flex: 1 }}>
              <TextField
                label="Low Performance Threshold (%)"
                type="number"
                value={localSettings?.lowPerformanceThreshold?.toString() || '30'}
                onChange={(value) => updateSetting('lowPerformanceThreshold', parseInt(value) || 30)}
                helpText="Alert when collection performance drops below this percentage"
              />
            </Box>
            <Box style={{ flex: 1 }}>
              <TextField
                label="High Performance Threshold (%)"
                type="number"
                value={localSettings?.highPerformanceThreshold?.toString() || '80'}
                onChange={(value) => updateSetting('highPerformanceThreshold', parseInt(value) || 80)}
                helpText="Alert when collection performance exceeds this percentage"
              />
            </Box>
          </InlineStack>

          <Divider />

          <Text variant="headingMd">Inventory Alerts</Text>

          <Checkbox
            label="Alert when products go out of stock"
            checked={localSettings?.outOfStockAlerts ?? true}
            onChange={(checked) => updateSetting('outOfStockAlerts', checked)}
          />

          <TextField
            label="Low Inventory Threshold"
            type="number"
            value={localSettings?.lowInventoryThreshold?.toString() || '10'}
            onChange={(value) => updateSetting('lowInventoryThreshold', parseInt(value) || 10)}
            helpText="Alert when product inventory falls below this number"
          />

          <Divider />

          <Text variant="headingMd">Notification Frequency</Text>

          <Select
            label="Alert Frequency"
            options={[
              { label: 'Immediate', value: 'immediate' },
              { label: 'Daily Summary', value: 'daily' },
              { label: 'Weekly Summary', value: 'weekly' },
            ]}
            value={localSettings?.frequency || 'daily'}
            onChange={(value) => updateSetting('frequency', value)}
          />

          <Checkbox
            label="Email notifications"
            checked={localSettings?.emailNotifications ?? true}
            onChange={(checked) => updateSetting('emailNotifications', checked)}
          />

          <InlineStack gap="200">
            <Button onClick={onClose}>
              Cancel
            </Button>
          </InlineStack>
        </BlockStack>
      </Box>
    </Modal>
  );
};

export default function PerformanceAlerts({
  alerts = [],
  onDismissAlert,
  onAlertAction,
  settings,
  onUpdateSettings
}) {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [filter, setFilter] = useState('all');

  const filteredAlerts = alerts.filter(alert => {
    if (filter === 'all') return true;
    return alert.type === filter;
  });

  const criticalAlerts = alerts.filter(alert => alert.type === 'critical').length;
  const warningAlerts = alerts.filter(alert => alert.type === 'warning').length;

  const handleDismissAll = useCallback(() => {
    alerts.forEach(alert => onDismissAlert(alert.id));
  }, [alerts, onDismissAlert]);

  if (alerts.length === 0) {
    return (
      <Card>
        <Box padding="400">
          <EmptyState
            heading="No alerts"
            image=""
          >
            <Text variant="bodyMd" color="subdued">
              All your collections are performing well! We'll notify you if anything needs attention.
            </Text>
            <Box paddingBlockStart="300">
              <Button onClick={() => setIsSettingsOpen(true)}>
                Configure Alert Settings
              </Button>
            </Box>
          </EmptyState>
        </Box>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <InlineStack align="space-between" blockAlign="center">
              <BlockStack gap="100">
                <Text variant="headingMd" fontWeight="semibold">
                  Performance Alerts
                </Text>
                <InlineStack gap="200">
                  {criticalAlerts > 0 && (
                    <Badge tone="critical">{criticalAlerts} Critical</Badge>
                  )}
                  {warningAlerts > 0 && (
                    <Badge tone="warning">{warningAlerts} Warning</Badge>
                  )}
                  <Text variant="bodySm" color="subdued">
                    {alerts.length} total alerts
                  </Text>
                </InlineStack>
              </BlockStack>

              <InlineStack gap="200">
                <Button
                  icon={SettingsIcon}
                  onClick={() => setIsSettingsOpen(true)}
                >
                  Settings
                </Button>
                {alerts.length > 0 && (
                  <Button variant="plain" onClick={handleDismissAll}>
                    Dismiss All
                  </Button>
                )}
              </InlineStack>
            </InlineStack>

            {alerts.length > 3 && (
              <Select
                label="Filter alerts"
                labelHidden
                options={[
                  { label: 'All Alerts', value: 'all' },
                  { label: 'Critical Only', value: 'critical' },
                  { label: 'Warnings Only', value: 'warning' },
                  { label: 'Success Only', value: 'success' },
                ]}
                value={filter}
                onChange={setFilter}
              />
            )}

            <BlockStack gap="300">
              {filteredAlerts.map((alert) => (
                <AlertItem
                  key={alert.id}
                  alert={alert}
                  onDismiss={onDismissAlert}
                  onAction={onAlertAction}
                />
              ))}
            </BlockStack>
          </BlockStack>
        </Box>
      </Card>

      <AlertSettings
        isOpen={isSettingsOpen}
        onClose={() => setIsSettingsOpen(false)}
        settings={settings}
        onSave={onUpdateSettings}
      />
    </>
  );
}
