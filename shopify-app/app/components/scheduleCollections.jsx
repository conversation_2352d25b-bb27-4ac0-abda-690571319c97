import { useState, useEffect, useCallback } from "react";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  Box,
  InlineStack,
  Card,
  Text,
  Button,
  Icon,
  Grid,
  BlockStack,
  Select,
  DatePicker,
  Banner,
} from "@shopify/polaris";
import { WandIcon, PersonIcon, CalendarIcon } from "@shopify/polaris-icons";

import content from "../locales/en.json";

export default function ScheduleCollections({
  open,
  hide,
  time,
  scheduleUpdate,
}) {
  const [isFirstButtonActive, setIsFirstButtonActive] = useState(false);
  const [selectedTime, setSelectedTime] = useState(null);
  const [selectedDateAndTime, setSelectedDateAndTime] = useState(null);
  const [selectedFrequency, setSelectedFrequency] = useState("");
  const [timeSelected, setTimeSelected] = useState("");
  const [combinedTime, setCombinedTime] = useState(null);
  const today = new Date();
  const [{ month, year }, setDate] = useState({
    month: today.getMonth(),
    year: today.getFullYear(),
  });
  const [selectedDates, setSelectedDates] = useState({
    start: today,
    end: today,
  });

  const handleFirstButtonClick = useCallback(() => {
    if (isFirstButtonActive) return;
    setIsFirstButtonActive(true);
  }, [isFirstButtonActive]);

  const handleSecondButtonClick = useCallback(() => {
    if (!isFirstButtonActive) return;
    setIsFirstButtonActive(false);
    setSelectedFrequency("");
    setSelectedDateAndTime(null);
    setSelectedTime(null);
  }, [isFirstButtonActive]);

  const handleFrequencyChange = useCallback(
    (value) => setSelectedFrequency(value),
    [],
  );
  const frequencyOptions =
    content.Rank_collections.schedule_collections.custom_options.map(
      (option) => ({
        label: option.label,
        value: option.value,
      }),
    );
  const handleSelectedAITime = (index) => {
    setSelectedTime(index);
    setCombinedTime(null);
    setSelectedFrequency("weekly");
    setTimeSelected("");
    const selected = time[index];

    const today = new Date();
    const dayOfWeek = [
      "Sunday",
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
    ];

    const selectedDayOffset =
      (dayOfWeek.indexOf(selected.day) - today.getDay() + 7) % 7;

    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + selectedDayOffset);

    const [hours, minutes] = selected.time.split(":").map(Number);
    targetDate.setUTCHours(hours, minutes, 0, 0); // Set time in UTC

    // Get UTC format
    const utcString = targetDate.toISOString().replace("Z", ""); // "YYYY-MM-DDTHH:mm:ss"

    setSelectedDateAndTime(utcString);
  };

  today.setHours(0, 0, 0, 0);

  const handleMonthChange = useCallback(
    (month, year) => setDate({ month, year }),
    [],
  );

  const handleDateChange = (dates) => {
    if (dates?.start && dates.start < today) return;
    setSelectedDates({
      start: dates?.start || today,
      end: dates?.start || today,
    });
  };

  const datePicker = (
    <DatePicker
      month={month}
      year={year}
      onChange={handleDateChange}
      onMonthChange={handleMonthChange}
      selected={selectedDates}
      disableDatesBefore={today}
      allowRange={false}
    />
  );

  const handleTimeChange = useCallback((index) => {
    setSelectedDateAndTime(null);
    setSelectedTime(null);

    setTimeSelected(() => timeOption[index].value);
  }, []);
  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      const period = hour < 12 ? "AM" : "PM";
      const formattedHour = hour % 12 === 0 ? 12 : hour % 12;
      const timeString = `${formattedHour}:00 ${period}`;
      options.push({ label: timeString, value: `${hour}:00` });
    }
    return options;
  };
  const timeOption = generateTimeOptions();

  useEffect(() => {
    if (timeSelected) {
      const [hourString, period] = timeSelected.split(":");
      let hour = parseInt(hourString, 10);

      if (period === "PM" && hour !== 12) {
        hour += 12;
      }
      if (period === "AM" && hour === 12) {
        hour = 0;
      }

      const combined = new Date(selectedDates.start);
      combined.setHours(hour, 0, 0, 0);

      setCombinedTime(combined);
    }
  }, [timeSelected, selectedDates.start]);

  const handleScheduleUpdate = () => {
    scheduleUpdate({
      selectedTime: selectedDateAndTime || combinedTime,
      selectedFrequency: selectedFrequency,
    });
  };

  return (
    <Modal open={open} onHide={hide}>
      <TitleBar title={content.Rank_collections.schedule_collections.heading_0}>
        <button variant="primary" onClick={handleScheduleUpdate}>
          {content.Rank_collections.schedule_collections.button_1}
        </button>
      </TitleBar>
      <Box style={{ padding: "10px" }}>
        <BlockStack gap="500">
          <Grid>
            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 3 }}>
              <Box style={{ flex: 1 }}>
                <Button
                  icon={CalendarIcon}
                  size="large"
                  fullWidth
                  pressed={!isFirstButtonActive}
                  onClick={handleSecondButtonClick}
                  variant={!isFirstButtonActive ? "primary" : "secondary"}
                >
                  {content.Rank_collections.schedule_collections.heading_2}
                </Button>
              </Box>
            </Grid.Cell>
            <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 3 }}>
              <Box style={{ flex: 1 }}>
                <Button
                  icon={WandIcon}
                  size="large"
                  fullWidth
                  pressed={isFirstButtonActive}
                  onClick={handleFirstButtonClick}
                  variant={isFirstButtonActive ? "primary" : "secondary"}
                >
                  {content.Rank_collections.schedule_collections.heading_1}
                </Button>
              </Box>
            </Grid.Cell>
          </Grid>
          {isFirstButtonActive && (
            <>
              <Card>
                <InlineStack gap="200" blockAlign="start" wrap={false}>
                  <Box>
                    <Icon source={WandIcon} />
                  </Box>

                  <Box>
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.schedule_collections.heading_3}
                    </Text>
                    <Text variant="bodyMd" as="p">
                      {content.Rank_collections.schedule_collections.content_1}
                    </Text>
                  </Box>
                </InlineStack>
              </Card>
              <InlineStack align="space-between" blockAlign="center">
                <Text variant="headingMd" as="h6" fontWeight="bold">
                  {content.Rank_collections.schedule_collections.heading_4}
                </Text>
                <Text variant="headingMd" as="h6" fontWeight="bold">
                  {content.Rank_collections.schedule_collections.heading_5}
                </Text>
              </InlineStack>
              {time?.map((option, index) => {
                const [hour, minute] = option.time.split(":").map(Number);
                const period = hour < 12 ? "AM" : "PM";
                const formattedHour = hour % 12 === 0 ? 12 : hour % 12; // Convert to 12-hour format
                const formattedTime = `${formattedHour}:${minute < 10 ? "0" + minute : minute} ${period}`;

                return (
                  <Card key={index}>
                    <InlineStack align="space-between" blockAlign="center">
                      <BlockStack gap="100">
                        <Text variant="headingLg" as="h6" fontWeight="semibold">
                          {option.day}, {formattedTime}
                        </Text>

                        <Box>
                          <InlineStack
                            align="start"
                            gap="100"
                            blockAlign="center"
                          >
                            {/* <Box>
                              <Icon source={PersonIcon} />
                            </Box> */}
                            {/* <Text variant="bodyMd" as="p" fontWeight="subdued">
                              {option.traffic_details} Traffic,{" "}
                              {option.competition_details} Impact
                            </Text> */}
                          </InlineStack>
                        </Box>
                      </BlockStack>
                      <InlineStack gap="300" blockAlign="center">
                        <BlockStack inlineAlign="center">
                          <Text
                            variant="headingLg"
                            as="h5"
                            fontWeight="bold"
                            tone="magic"
                          >
                            {option.score}
                          </Text>
                          <Text variant="bodyMd" as="p" fontWeight="subdued">
                            Score
                          </Text>
                        </BlockStack>
                        <Button
                          onClick={() => handleSelectedAITime(index)}
                          variant={
                            selectedTime === index ? "primary" : "secondary"
                          }
                        >
                          {selectedTime === index ? "Selected" : "Select"}
                        </Button>
                      </InlineStack>
                    </InlineStack>
                  </Card>
                );
              })}
            </>
          )}

          {!isFirstButtonActive && (
            <>
              <Grid>
                <Grid.Cell columnSpan={{ xs: 3, lg: 3, md: 3, sm: 3 }}>
                  <Text alignment="center">
                    {content.Rank_collections.schedule_collections.heading_6}
                  </Text>
                  <Card sectioned>{datePicker}</Card>
                </Grid.Cell>
                <Grid.Cell columnSpan={{ xs: 3, lg: 3, md: 3, sm: 3 }}>
                  <Text alignment="center">
                    {content.Rank_collections.schedule_collections.heading_7}
                  </Text>

                  <Card sectioned>
                    <Box
                      style={{
                        height: "215px",
                        overflowY: "scroll",
                        scrollbarWidth: "thin",
                        flex: 1,
                      }}
                    >
                      <BlockStack gap="200">
                        {timeOption.map((option, index) => (
                          <Button
                            key={index}
                            variant="plain"
                            onClick={() => handleTimeChange(index)}
                          >
                            {option.label}
                          </Button>
                        ))}
                      </BlockStack>
                    </Box>
                  </Card>
                </Grid.Cell>
              </Grid>

              <Select
                label={content.Rank_collections.schedule_collections.heading_8}
                placeholder={
                  content.Rank_collections.schedule_collections.placeholder_1
                }
                options={frequencyOptions}
                onChange={handleFrequencyChange}
                value={selectedFrequency}
                helpText={
                  <Banner tone="info">
                    {" "}
                    {
                      content.Rank_collections.schedule_collections
                        .help_content_1
                    }
                  </Banner>
                }
              />
            </>
          )}
        </BlockStack>
      </Box>
    </Modal>
  );
}
