import React from 'react';
// Restore necessary imports, keep Box, remove Card
import {
    BlockStack,
    Icon,
    Box,
    InlineStack,
    Text,
    Link,
    Button,
} from '@shopify/polaris';
import { HomeIcon, ChatIcon, InfoIcon } from '@shopify/polaris-icons'; // Restore needed icons
import content from '../locales/en.json'; // Restore content import
import {
    ContactLink,
    bestPracticesLink,
    seoLink,
    collectionMetricsLink,
} from "../configs/config"; // Restore config import

export default function HelpResources({ featureFlags }) {

    // Restore articles definition
    const articles = [
        // {
        //     title: content.Rank_collections.home.heading_8,
        //     url: bestPracticesLink,
        //     text: content.Rank_collections.home.link_content_1,
        //     flag: true,
        // },
        {
            title: content.Rank_collections.home.heading_9,
            url: seoLink,
            text: content.Rank_collections.home.link_content_2,
            flag: true,
        },
        {
            title: content.Rank_collections.home.heading_10,
            url: collectionMetricsLink,
            text: content.Rank_collections.home.link_content_3,
            flag: featureFlags?.home?.collection_metrics_link,
        },
    ];

    // Use Box styled as a card, add original content back
    return (
        <Box
            borderWidth="025"
            borderColor="border"
            borderRadius="200"
            background="bg-surface"
        // Remove padding from Box, add sections with padding instead?
        // Or keep padding and structure content inside
        >
            {/* Add Title using Text within Box padding */}
            {/* <Box padding="400">
                <Text variant="headingMd" as="h3">Help & Resources</Text>
            </Box> */}

            {/* Use Box with padding instead of Card.Section */}
            <Box paddingBlockStart="0" paddingBlockEnd="400" paddingInlineStart="400" paddingInlineEnd="400">
                <BlockStack gap="300">
                    {/* <Text variant="headingSm" as="h4">Need Assistance?</Text> Sub-heading */}
                    {/* <Text variant="bodyMd" as="p"> */}
                    {/* {content.Rank_collections.home.content_4} Our team is ready to help. */}
                    {/* </Text> */}
                    {/* <Button
                        icon={ChatIcon}
                        variant="primary"
                        url={ContactLink}
                        target="_blank"
                    >
                        {content.Rank_collections.home.link_content_4} {/* "Chat with Support" */}
                    {/* </Button> */}
                </BlockStack>
            </Box>

            {/* Use Box with padding instead of Card.Section */}
            <Box paddingBlockStart="0" paddingBlockEnd="400" paddingInlineStart="400" paddingInlineEnd="400">
                <BlockStack gap="300">
                    <Text variant="headingSm" as="h4">Learning Resources</Text> {/* Sub-heading */}
                    <BlockStack gap="200">
                        {articles.map((article, index) => (
                            article.flag && (
                                <Link key={index} url={article.url} target="_blank" removeUnderline>
                                    <InlineStack gap="100" blockAlign="center">
                                        {/* <Icon source={InfoIcon} tone="subdued"/> */}
                                        <Text variant="bodyMd" as="span">{article.text}</Text>
                                    </InlineStack>
                                </Link>
                            )
                        ))}
                    </BlockStack>
                </BlockStack>
            </Box>
        </Box >
    );
} 