import { Spinner, Box, BlockStack, Text, InlineStack } from "@shopify/polaris";
import content from "../locales/en.json";

export default function OwnSpinner() {
  return (
    <Box
      style={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "90vh",
      }}
    >
      <BlockStack gap='300'>
        <Box style={{display:'flex', flexDirection:'column', alignItems:'center'}}>
      <Spinner></Spinner>
      <Text variant="headingLg" as='h5' fontWeight="semibold">{content.Rank_collections.components.heading_1}</Text>
      </Box>
      </BlockStack>
    </Box>
  );
}
