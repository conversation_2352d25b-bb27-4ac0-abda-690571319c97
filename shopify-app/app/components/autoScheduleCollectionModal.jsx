import { useEffect, useCallback, useState } from "react";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  Badge,
  Card,
  BlockStack,
  InlineStack,
  Box,
  Button,
  Checkbox,
  Text,
  Select,
  Icon,
  TextField,
  Pagination,
  Spinner,
  Banner
} from "@shopify/polaris";
import { WandIcon, FileIcon } from "@shopify/polaris-icons";

import content from "../locales/en.json";
import flag from "../configs/featureFlag.json";

export default function AutoScheduleCollectionsModal({
  open,
  hide,
  onUpdate,
  loading,
  configs,
  from,
  domain,
  keywordsPayload,
  keywordLoading,
  keywordOption,
  manualCreatePayload,
  manualLoading,
  pageLoad,
  scheduleTime,
  syncUrl,
  syncLoading,
  isUrlSynced
}) {
  const [productAttributes, setProductAttributes] = useState(
    content.Rank_collections.collection_generation.product_attributes.reduce(
      (acc, attribute) => {
        if (attribute.label) {
          acc[attribute.label.toLowerCase().replace(/\s+/g, "_")] = false;
        }
        return acc;
      },
      {},
    ),
  );
  const handleProductAttributesChange = useCallback((attribute) => {
    setProductAttributes((prev) => ({
      ...prev,
      [attribute.label.toLowerCase().replace(/\s+/g, "_")]:
        !prev[attribute.label.toLowerCase().replace(/\s+/g, "_")],
    }));
  }, []);

  const [trendsSelected, setTrendsSelected] = useState(
    content.Rank_collections.collection_generation.trends.reduce((acc, attribute) => {
      if (attribute.label) {
        acc[attribute.label.toLowerCase().replace(/\s+/g, "_")] = false;
      }
      return acc;
    }, {}),
  );
  const handleTrendsAttributesChange = useCallback((attribute) => {
    setTrendsSelected((prev) => ({
      ...prev,
      [attribute.label.toLowerCase().replace(/\s+/g, "_")]:
        !prev[attribute.label.toLowerCase().replace(/\s+/g, "_")],
    }));
  }, []);

  const [selectedBehavior, setSelectedBehavior] = useState(
    content.Rank_collections.collection_generation.behaviors.reduce((acc, attribute) => {
      if (attribute.label) {
        acc[attribute.label.toLowerCase().replace(/\s+/g, "_")] = false;
      }
      return acc;
    }, {}),
  );
  const handleBehaviourAttributeChange = useCallback((attribute) => {
    setSelectedBehavior((prev) => ({
      ...prev,
      [attribute.label.toLowerCase().replace(/\s+/g, "_")]:
        !prev[attribute.label.toLowerCase().replace(/\s+/g, "_")],
    }));
  }, []);

  const [selectedLocation, setSelectedLocation] = useState(
    content.Rank_collections.collection_generation.location.reduce((acc, attribute) => {
      if (attribute.label) {
        acc[attribute.label.toLowerCase().replace(/\s+/g, "_")] = false;
      }
      return acc;
    }, {}),
  );
  const handleLocationAttributeChange = useCallback((attribute) => {
    setSelectedLocation((prev) => ({
      ...prev,
      [attribute.label.toLowerCase().replace(/\s+/g, "_")]:
        !prev[attribute.label.toLowerCase().replace(/\s+/g, "_")],
    }));
  }, []);

  const [selectedAnalysis, setSelectedAnalysis] = useState(
    content.Rank_collections.collection_generation.analysis.reduce((acc, attribute) => {
      if (attribute.label) {
        acc[attribute.label.toLowerCase().replace(/\s+/g, "_")] = false;
      }
      return acc;
    }, {}),
  );
  const handleAnalysisAttributeChange = useCallback((attribute) => {
    setSelectedAnalysis((prev) => ({
      ...prev,
      [attribute.label.toLowerCase().replace(/\s+/g, "_")]:
        !prev[attribute.label.toLowerCase().replace(/\s+/g, "_")],
    }));
  }, []);

  const [selectedFrequency, setSelectedFrequency] = useState("");
  const handleFrequencyChange = useCallback(
    (value) => setSelectedFrequency(value),
    [],
  );
  const frequencyOptions =
    content.Rank_collections.collection_generation.frequency_options.map((frequency) => ({
      label: frequency.label,
      value: frequency.value,
    }));

  const [selectedImpactScore, setSelectedImpactScore] = useState("");
  const handleImpactScoreChange = useCallback(
    (value) => setSelectedImpactScore(value),
    [],
  );
  const impactScoreOptions =
    content.Rank_collections.collection_generation.impact_score_options.map((score) => ({
      label: score.label,
      value: score.value,
    }));

  const [collectionsPerMonth, setCollectionPerMonth] = useState(1);
  const handleCollectionsPerMonthChange = useCallback((value) => {
    if (value > 10) {
      shopify.toast.show(content.Rank_collections.collection_generation.toast_1, {
        isError: true,
      });
      return;
    } else {
      setCollectionPerMonth(value);
    }
  }, []);

  const [competitorUrl, setCompetitorUrl] = useState("");
  const handleChangeCompetitorUrl = useCallback(
    (value) => setCompetitorUrl(value),
    [],
  );

  const [enteredCompetitorUrl, setEnteredCompetitorUrl] = useState([]);

  const formatURL = (url) => {
    let formattedURL = url.startsWith('http://') || url.startsWith('https://') ? url : `https://${url}`;
    // Remove trailing '/' if present
    return formattedURL.endsWith('/') ? formattedURL.slice(0, -1) : formattedURL;
  };


  const handleAddCompetitorUrl = () => {
    if (competitorUrl.trim() !== '') {
      setEnteredCompetitorUrl(prevUrls => [...prevUrls, formatURL(competitorUrl)]);
      setCompetitorUrl('');
    }
  };

  const handleRemoveUrl = (index) => {
    setEnteredCompetitorUrl(prevUrls => prevUrls.filter((_, i) => i !== index));
  };

  const handleEnterKey = (event) => {
    if (event.key === 'Enter') {
      handleAddCompetitorUrl();
    }
  };

  // const [selectedKeyword, setSelectedKeyword] = useState([]);
  // const [currentPage, setCurrentPage] = useState(1);
  // const itemsPerPage = 5;
  // const totalPages = Math.ceil(keywordOption?.length / itemsPerPage);
  // const startIndex = (currentPage - 1) * itemsPerPage;
  // const paginatedKeywords = keywordOption?.slice(startIndex, startIndex + itemsPerPage);

  // const handleKeywordChange = useCallback((key) => {
  //   setSelectedKeyword((prev) => {
  //     if (prev.includes(key)) {
  //       // Remove the key if it's already selected
  //       return prev.filter((item) => item !== key);
  //     } else {
  //       // Add the key if it's not already selected
  //       return [...prev, key];
  //     }
  //   });
  // }, []);
  // const keyOption = keywordOption?.map((key) => ({
  //   label: key,
  //   value: key
  // }))
  // const handlePreviousPage = () => {
  //   if (currentPage > 1) setCurrentPage((prevPage) => prevPage - 1);
  // };

  // const handleNextPage = () => {
  //   if (currentPage < totalPages) setCurrentPage((prevPage) => prevPage + 1);
  // };

  const [urlSynced, setUrlSyned] = useState(false)


  useEffect(() => {
    if (configs && Object.keys(configs).length > 0) {
      setProductAttributes(configs.product || {});
      setTrendsSelected(configs.seasonal || {});
      setSelectedBehavior(configs.customer || {});
      setSelectedAnalysis(configs.market || {});
      setSelectedLocation(configs.location || {})
      setSelectedFrequency(configs.frequency || "");
      setSelectedImpactScore(configs.impact || "");
      setCollectionPerMonth(configs.count || 1);
      setUrlSyned(configs.urlSync || isUrlSynced)
    }
  }, [configs, isUrlSynced]);

  const handleConfigSchedule = (fromInner) => {
    if (fromInner === 'competitor') {
      if (!urlSynced) {
        shopify.toast.show("Please add your competitor url", { isError: true });
        return;
      }
    }

    if ((selectedFrequency === '' || selectedImpactScore === '') && from !== 'autoSchedule') {
      shopify.toast.show("Please choose frequency and impact score", { isError: true })
      return;
    }

    const currentDate = new Date();
    let startDate;

    // Define frequency mapping for date calculations (without "daily")
    const frequencyMapping = {
      "every_three_days": { generated: 1, scheduled: 2, published: 3 },
      "weekly": { generated: 5, scheduled: 6, published: 7 },
      "every_two_weeks": { generated: 12, scheduled: 13, published: 14 },
      "monthly": { generated: 28, scheduled: 29, published: 30 }
    };

    const selectedFreq = from === "autoSchedule" ? scheduleTime.selectedFrequency : selectedFrequency;
    let { generated, scheduled, published } = frequencyMapping[selectedFreq] || {};

    if (from !== "autoSchedule") {
      startDate = new Date(currentDate);
    } else {
      startDate = new Date(scheduleTime.selectedTime);
    }

    onUpdate({
      product: productAttributes,
      seasonal: trendsSelected,
      customer: selectedBehavior,
      market: selectedAnalysis,
      location: selectedLocation,
      competitorUrl: urlSynced ? "competitor" : "self",
      schedule_settings: {
        frequency: selectedFreq,
        count: collectionsPerMonth,
        impact_type: selectedImpactScore,
        start_date: startDate,
        next_generated_date: new Date(startDate.getTime() + generated * 24 * 60 * 60 * 1000),
        next_scheduled_date: new Date(startDate.getTime() + scheduled * 24 * 60 * 60 * 1000),
        next_published_date: new Date(startDate.getTime() + published * 24 * 60 * 60 * 1000),
      },
    });
  };



  const handleKeyWordGenerate = (from) => {

    if (from === 'ai-engine') {
      keywordsPayload({
        store: domain,
        competitorUrl: null,
        focusKeywords: null
      })
    } else {
      keywordsPayload({
        store: null,
        competitorUrl: enteredCompetitorUrl,
        focusKeywords: null
      })
    }
  }

  const handleSycnCompetitorUrl = () => {
    syncUrl({
      url: enteredCompetitorUrl
    })
  }

  const handleManualGenerate = (from) => {
    if (from === 'competitor') {
      if (!urlSynced) {
        shopify.toast.show("Please add your competitor url", { isError: true })
        return;
      }
    }
    manualCreatePayload({
      product: productAttributes,
      seasonal: trendsSelected,
      customer: selectedBehavior,
      market: selectedAnalysis,
      location: selectedLocation,
      competitorUrl: urlSynced ? "competitor" : "self",
    });
  };


  return (
    <Modal open={open} onHide={hide} variant="large">
      <TitleBar title={content.Rank_collections.collection_generation.heading_1}></TitleBar>
      {!pageLoad ? <Box style={{ position: "relative" }}>
        <Box style={{ padding: "10px" }}>
          <BlockStack gap="300">
            <Card sectioned>
              <BlockStack gap="200">
                <InlineStack gap="200" blockAlign="center">
                  <Box>
                    <Icon source={WandIcon} />
                  </Box>
                  <Text variant="headingMd" as="h6" fontWeight="semibold">
                    {from !== 'manual' ? content.Rank_collections.collection_generation.heading_2 : content.Rank_collections.collection_generation.heading_3}
                  </Text>
                </InlineStack>
                <Text variant="bodyMd" as="p">
                  {content.Rank_collections.collection_generation.content_3}
                </Text>
              </BlockStack>
            </Card>


            {!urlSynced && <Card sectioned>
              <BlockStack gap="300">
                <InlineStack gap="100" blockAlign="end">
                  <Box style={{ flex: 1 }} onKeyDown={handleEnterKey}>
                    <TextField
                      label={content.Rank_collections.collection_generation.heading_4}
                      onChange={handleChangeCompetitorUrl}
                      value={competitorUrl}
                    />

                  </Box>

                  <Button variant="primary" onClick={handleAddCompetitorUrl}>
                    {content.Rank_collections.collection_generation.button_2}
                  </Button>
                </InlineStack>
                <InlineStack align="start" gap='300' blockAlign="center">
                  <Text variant="bodyMd" as="p" fontWeight="subdued">{content.Rank_collections.collection_generation.content_2}</Text>
                  <Button variant="plain" onClick={(from === 'auto' || from === 'autoSchedule') ? () => handleConfigSchedule('self') : () => handleManualGenerate('self')} loading={manualLoading || loading}>{content.Rank_collections.collection_generation.button_1}</Button>
                </InlineStack>
                {enteredCompetitorUrl.length > 0 &&
                  enteredCompetitorUrl.map((url, index) => (
                    <InlineStack align="start" key={index} gap="300">
                      <Badge>
                        <InlineStack gap="200" blockAlign="center">
                          <Text variant="bodyMd" as="p">
                            {url}
                          </Text>
                          <Button
                            variant="plain"
                            tone="critical"
                            onClick={() => handleRemoveUrl(index)}
                          >
                            {content.Rank_collections.collection_generation.button_3}
                          </Button>
                        </InlineStack>
                      </Badge>
                    </InlineStack>
                  ))}
                {/* <InlineStack align="center" blockAlign="center">
                    <Button
                      disabled={enteredCompetitorUrl.length === 0}
                      variant="primary"
                      loading={keywordLoading}
                      onClick={()=>handleKeyWordGenerate('manual')}
                    >
                      Analyse
                    </Button>
                  </InlineStack> */}
                <Button variant="primary" disabled={enteredCompetitorUrl.length === 0} onClick={handleSycnCompetitorUrl} loading={syncLoading}>{content.Rank_collections.collection_generation.button_6}</Button>
              </BlockStack>
            </Card>}

            {/* {(from === 'manual' && keywordOption?.length > 0) && (
        <Card sectioned>
          <InlineStack align="space-between">
          <BlockStack gap='300'>
            
            <Text variant="headingMd" as="h6" fontWeight="semibold">Suggested Keywords</Text>
            
              
          {paginatedKeywords.map((item,index) => (
          <Checkbox
          key={index}
          label={item}
            option={keyOption}
            checked={selectedKeyword.includes(item)}
            onChange={()=> handleKeywordChange(item)}
          />
          
          ))} */}



            {/* </BlockStack> */}
            {/* {selectedKeyword.length > 0 && (
            <BlockStack gap='300'>
            <Text variant="headingMd" as="h6" fontWeight="semibold">Selected Keywords</Text>
            <Box style={{maxHeight:'250px', overflowY:'scroll', scrollbarWidth:'thin'}}>
              {selectedKeyword.map((key, index) => (
                <InlineStack key={index} align="start" gap='200' blockAlign="center">
                  <Text variant="bodyMd" as='p' fontWeight="semibold">
                    {index + 1}
                  </Text>
                  <Text variant="bodyMd" as="p" fontWeight="semibold">{key}</Text>
                </InlineStack>
              ))}
            </Box>
            </BlockStack>
          )}
          </InlineStack>
          <InlineStack align="center" blockAlign="center" gap='300'>
            <Button onClick={() => setSelectedKeyword([])} variant="primary" disabled={selectedKeyword.length === 0} size='large'>Reset</Button>
          <Pagination
            onPrevious={handlePreviousPage}
            onNext={handleNextPage}
            hasPrevious={currentPage > 1}
            hasNext={currentPage < totalPages}
            label={`${startIndex + 1}-${Math.min(startIndex + itemsPerPage, keywordOption.length)} of ${keywordOption.length}`}
          />
          </InlineStack>
        </Card>
      )} */}
            {/* <Card sectioned>
        <BlockStack gap='200'>
        <Text variant="headingMd" as="h6" fontWeight="semibold">
          {content.Rank_collections.collection_generation.heading_5}
        </Text>
        <Text variant="bodyMd" as="p">
          {content.Rank_collections.collection_generation.content_3}
        </Text>
        </BlockStack>
      </Card> */}
            {flag.attribute_selection.product_attributes && <Card sectioned>
              <Box

              >
                <BlockStack gap="300">
                  <InlineStack align='space-between' blockAlign='center'>

                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_6}
                    </Text>
                    <Text variant="headingMd" as='h6' fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_14}
                    </Text>
                  </InlineStack>
                  {content.Rank_collections.collection_generation.product_attributes.map(
                    (attribute, index) => (
                      <InlineStack
                        key={index}
                        align="space-between"
                        blockAlign="start"
                      >
                        <Checkbox
                          label={
                            <Text variant="bodyLg" as="p" fontWeight="semibold">
                              {attribute.label}
                            </Text>
                          }
                          helpText={attribute.help_text}
                          checked={
                            productAttributes[
                            attribute.label.toLowerCase().replace(/\s+/g, "_")
                            ] || false
                          } // Check if selected
                          onChange={() =>
                            handleProductAttributesChange(attribute)
                          }
                        />
                        <Badge
                          tone={
                            attribute.badge !== "High"
                              ? "attention"
                              : "success"
                          }
                        >
                          {attribute.badge}
                        </Badge>
                      </InlineStack>
                    ),
                  )}
                </BlockStack>
              </Box>
            </Card>}

            <Card sectioned>
              <Box

              >
                <BlockStack gap="300">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_7}
                    </Text>
                    <Text variant="headingMd" as='h6' fontWeight="semibold">
                      {flag.attribute_selection.seasonal_collections ? content.Rank_collections.collection_generation.heading_14 : <Badge tone='info'>{content.Rank_collections.collection_generation.heading_15}</Badge>}
                    </Text>
                  </InlineStack>
                  {content.Rank_collections.collection_generation.trends.map(
                    (trend, index) => (
                      flag.attribute_selection.seasonal_collections && <InlineStack
                        key={index}
                        align="space-between"
                        blockAlign="start"
                      >
                        <Checkbox
                          label={
                            <Text variant="bodyLg" as="p" fontWeight="semibold">
                              {trend.label}
                            </Text>
                          }
                          helpText={trend.help_text}
                          checked={
                            trendsSelected[
                            trend.label.toLowerCase().replace(/\s+/g, "_")
                            ] || false
                          } // Check if selected
                          onChange={() => handleTrendsAttributesChange(trend)}
                        />
                        <Badge
                          tone={
                            trend.badge !== "High"
                              ? "attention"
                              : "success"
                          }
                        >
                          {trend.badge}
                        </Badge>
                      </InlineStack>
                    ),
                  )}
                </BlockStack>
              </Box>
            </Card>

            {flag.attribute_selection.customer_behaviour && <Card sectioned>
              <Box

              >
                <BlockStack gap="300">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_8}
                    </Text>
                    <Text variant="headingMd" as='h6' fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_14}
                    </Text>
                  </InlineStack>
                  {content.Rank_collections.collection_generation.behaviors.map(
                    (attribute, index) => (
                      <InlineStack
                        key={index}
                        align="space-between"
                        blockAlign="start"
                      >
                        <Checkbox
                          label={
                            <Text variant="bodyLg" as="p" fontWeight="semibold">
                              {attribute.label}
                            </Text>
                          }
                          helpText={attribute.help_text}
                          checked={
                            selectedBehavior[
                            attribute.label.toLowerCase().replace(/\s+/g, "_")
                            ] || false
                          }
                          onChange={() =>
                            handleBehaviourAttributeChange(attribute)
                          }
                        />
                        <Badge
                          tone={
                            attribute.badge !== "High"
                              ? "attention"
                              : "success"
                          }
                        >
                          {attribute.badge}
                        </Badge>
                      </InlineStack>
                    ),
                  )}
                </BlockStack>
              </Box>
            </Card>}

            {flag.attribute_selection.location_based && <Card sectioned>
              <Box
              >
                <BlockStack gap="300">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_9}
                    </Text>
                    <Text variant="headingMd" as='h6' fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_14}
                    </Text>
                  </InlineStack>
                  {content.Rank_collections.collection_generation.location.map(
                    (attribute, index) => (
                      <InlineStack
                        key={index}
                        align="space-between"
                        blockAlign="start"
                      >
                        <Checkbox
                          label={
                            <Text variant="bodyLg" as="p" fontWeight="semibold">
                              {attribute.label}
                            </Text>
                          }
                          helpText={attribute.help_text}
                          checked={
                            selectedLocation[
                            attribute.label.toLowerCase().replace(/\s+/g, "_")
                            ] || false
                          }
                          onChange={() =>
                            handleLocationAttributeChange(attribute)
                          }
                        />
                        <Badge
                          tone={
                            attribute.badge !== "High"
                              ? "attention"
                              : "success"
                          }
                        >
                          {attribute.badge}
                        </Badge>
                      </InlineStack>
                    ),
                  )}
                </BlockStack>
              </Box>
            </Card>}

            {flag.attribute_selection.market_analysis && <Card sectioned>
              <Box

              >
                <BlockStack gap="300">
                  <InlineStack align="space-between" blockAlign="center">
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_10}
                    </Text>
                    <Text variant="headingMd" as='h6' fontWeight="semibold">
                      {content.Rank_collections.collection_generation.heading_14}
                    </Text>
                  </InlineStack>
                  {content.Rank_collections.collection_generation.market.map(
                    (attribute, index) => (
                      <InlineStack
                        key={index}
                        align="space-between"
                        blockAlign="start"
                      >
                        <Checkbox
                          label={
                            <Text variant="bodyLg" as="p" fontWeight="semibold">
                              {attribute.label}
                            </Text>
                          }
                          helpText={attribute.help_text}
                          checked={
                            selectedAnalysis[
                            attribute.label.toLowerCase().replace(/\s+/g, "_")
                            ] || false
                          }
                          onChange={() =>
                            handleAnalysisAttributeChange(attribute)
                          }
                        />
                        <Badge
                          tone={
                            attribute.badge !== "High Impact"
                              ? "attention"
                              : "success"
                          }
                        >
                          {attribute.badge}
                        </Badge>
                      </InlineStack>
                    ),
                  )}
                </BlockStack>
              </Box>
            </Card>}

            {(from === "auto" || from === 'autoSchedule') && (
              <Card sectioned>
                <BlockStack gap="300">
                  <Text variant="headingMd" as="h6" fontWeight="semibold">
                    {content.Rank_collections.collection_generation.heading_11}
                  </Text>
                  {from !== 'autoSchedule' && <>
                    <Banner tone='info'>{content.Rank_collections.collection_generation.info}</Banner>
                    <Select
                      label={content.Rank_collections.collection_generation.heading_12}
                      placeholder={
                        content.Rank_collections.collection_generation.placeholder_1
                      }
                      onChange={handleFrequencyChange}
                      value={selectedFrequency}
                      options={frequencyOptions}
                    />

                    <Select
                      label={content.Rank_collections.collection_generation.heading_16}
                      placeholder={
                        content.Rank_collections.collection_generation.placeholder_2
                      }
                      onChange={handleImpactScoreChange}
                      value={selectedImpactScore}
                      options={impactScoreOptions}
                    /></>}

                  <TextField
                    label={content.Rank_collections.collection_generation.heading_17}
                    type="number"
                    value={collectionsPerMonth}
                    onChange={handleCollectionsPerMonthChange}
                  />
                </BlockStack>
              </Card>
            )}
            <Button
              variant="primary"
              icon={FileIcon}
              size="large"
              onClick={(from === 'auto' || from === 'autoSchedule') ? () => handleConfigSchedule('competitor') : () => handleManualGenerate('competitor')}
              loading={loading || manualLoading}

            >
              {(from === 'auto' || from === 'autoSchedule') ? content.Rank_collections.collection_generation.button_4 : content.Rank_collections.collection_generation.button_5}
            </Button>
          </BlockStack>
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            padding: "10px",
            position: "absolute",
            left: 0,
            width: "100%",
          }}
        ></Box>
      </Box> : (
        <Box>
          <InlineStack align="center" blockAlign="center">
            <Spinner size='small' />
          </InlineStack>
        </Box>
      )}
    </Modal>
  );
}
