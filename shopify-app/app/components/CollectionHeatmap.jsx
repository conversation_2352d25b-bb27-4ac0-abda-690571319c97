import React, { useState, useCallback } from 'react';
import {
  Card,
  BlockStack,
  InlineStack,
  Box,
  Text,
  Button,
  Badge,
  Tooltip,
  Icon,
  Grid,
  SkeletonBodyText,
  EmptyState,
  Popover,
} from '@shopify/polaris';
import {
  WandIcon,
  ViewIcon,
  HomeIcon
} from '@shopify/polaris-icons';

const ProductHeatmapItem = ({ product, index, isDragging }) => {
  const getPerformanceColor = (score) => {
    if (score >= 80) return '#00A047'; // Green
    if (score >= 60) return '#FFC453'; // Yellow
    if (score >= 40) return '#FF8A00'; // Orange
    return '#D72C0D'; // Red
  };

  const getPerformanceBadge = (score) => {
    if (score >= 80) return { tone: 'success', text: 'High' };
    if (score >= 60) return { tone: 'warning', text: 'Medium' };
    if (score >= 40) return { tone: 'attention', text: 'Low' };
    return { tone: 'critical', text: 'Poor' };
  };

  const performanceScore = product.performance_score || 0;
  const badge = getPerformanceBadge(performanceScore);

  return (
    <Card
      sectioned
      background={isDragging ? 'bg-surface-selected' : 'bg-surface'}
      borderColor={isDragging ? 'border-brand' : undefined}
    >
      <InlineStack align="space-between" blockAlign="center">
        <InlineStack gap="300" blockAlign="center">
          <Box>
            <Icon source={WandIcon} tone="subdued" />
          </Box>
          <Box
            style={{
              width: '60px',
              height: '60px',
              backgroundColor: '#f6f6f7',
              borderRadius: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            {product.image_url ? (
              <img
                src={product.image_url}
                alt={product.title}
                style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'cover',
                  borderRadius: '8px',
                }}
              />
            ) : (
              <Text variant="bodySm" color="subdued">
                No Image
              </Text>
            )}
          </Box>
          <BlockStack gap="100">
            <Text variant="bodyMd" fontWeight="semibold">
              {product.title}
            </Text>
            <Text variant="bodySm" color="subdued">
              Position: {index + 1}
            </Text>
          </BlockStack>
        </InlineStack>

        <InlineStack gap="200" blockAlign="center">
          <Tooltip content={`Performance Score: ${performanceScore}%`}>
            <Badge tone={badge.tone}>{badge.text}</Badge>
          </Tooltip>

          <InlineStack gap="100" blockAlign="center">
            <Icon source={ViewIcon} tone="subdued" />
            <Text variant="bodySm">{product.view_count || 0}</Text>
          </InlineStack>

          <InlineStack gap="100" blockAlign="center">
            <Icon source={HomeIcon} tone="subdued" />
            <Text variant="bodySm">{product.click_count || 0}</Text>
          </InlineStack>

          <Box
            style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              backgroundColor: getPerformanceColor(performanceScore),
            }}
          />
        </InlineStack>
      </InlineStack>
    </Card>
  );
};

export default function CollectionHeatmap({
  collection,
  loading,
  onReorder,
  onOptimize
}) {
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [products, setProducts] = useState(collection?.products || []);

  const handleOptimizeOrder = useCallback(() => {
    // Sort products by performance score (highest first)
    const optimizedProducts = [...products].sort((a, b) =>
      (b.performance_score || 0) - (a.performance_score || 0)
    );
    setProducts(optimizedProducts);
    if (onReorder) {
      onReorder(optimizedProducts);
    }
  }, [products, onReorder]);

  if (loading) {
    return (
      <Card>
        <Box padding="400">
          <SkeletonBodyText lines={6} />
        </Box>
      </Card>
    );
  }

  if (!collection || !products.length) {
    return (
      <Card>
        <Box padding="400">
          <EmptyState
            heading="No products found"
            image=""
          >
            <Text variant="bodyMd" color="subdued">
              This collection doesn't have any products to analyze.
            </Text>
          </EmptyState>
        </Box>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <Box padding="400">
          <BlockStack gap="400">
            <InlineStack align="space-between" blockAlign="center">
              <BlockStack gap="100">
                <Text variant="headingMd" fontWeight="semibold">
                  Collection Performance Heatmap
                </Text>
                <Text variant="bodySm" color="subdued">
                  Colors indicate performance levels. Green = High, Yellow = Medium, Orange = Low, Red = Poor
                </Text>
              </BlockStack>

              <InlineStack gap="200">
                <Button onClick={handleOptimizeOrder}>
                  Auto-Optimize Order
                </Button>
                <Popover
                  active={isDetailsOpen}
                  activator={
                    <Button
                      variant="primary"
                      onClick={() => setIsDetailsOpen(!isDetailsOpen)}
                    >
                      View Details
                    </Button>
                  }
                  onClose={() => setIsDetailsOpen(false)}
                  sectioned
                >
                  <Box style={{ width: '400px', maxHeight: '500px' }}>
                    <BlockStack gap="300">
                      <Text variant="headingMd">Collection Performance Details</Text>

                      <InlineStack gap="300">
                        <Box>
                          <Text variant="bodySm" tone="subdued">Total Products</Text>
                          <Text variant="headingMd">{products.length}</Text>
                        </Box>
                        <Box>
                          <Text variant="bodySm" tone="subdued">Avg Performance</Text>
                          <Text variant="headingMd">
                            {Math.round(
                              products.reduce((sum, p) => sum + (p.performance_score || 0), 0) / products.length
                            )}%
                          </Text>
                        </Box>
                      </InlineStack>

                      <InlineStack gap="300">
                        <Box>
                          <Text variant="bodySm" tone="subdued">Total Views</Text>
                          <Text variant="headingMd">
                            {products.reduce((sum, p) => sum + (p.view_count || 0), 0)}
                          </Text>
                        </Box>
                        <Box>
                          <Text variant="bodySm" tone="subdued">Total Clicks</Text>
                          <Text variant="headingMd">
                            {products.reduce((sum, p) => sum + (p.click_count || 0), 0)}
                          </Text>
                        </Box>
                      </InlineStack>

                      <Box style={{ maxHeight: '300px', overflowY: 'auto' }}>
                        <BlockStack gap="200">
                          {products.slice(0, 10).map((product, index) => (
                            <Box key={product.id} padding="200" background="bg-surface-secondary">
                              <InlineStack gap="200" blockAlign="center">
                                <Text variant="bodyMd" fontWeight="medium">
                                  {product.title}
                                </Text>
                                <Badge tone={product.performance_score >= 80 ? 'success' : 'warning'}>
                                  {product.performance_score}%
                                </Badge>
                              </InlineStack>
                            </Box>
                          ))}
                        </BlockStack>
                      </Box>
                    </BlockStack>
                  </Box>
                </Popover>
              </InlineStack>
            </InlineStack>

            <BlockStack gap="200">
              {products.slice(0, 5).map((product, index) => (
                <ProductHeatmapItem
                  key={product.id}
                  product={product}
                  index={index}
                  isDragging={false}
                />
              ))}
            </BlockStack>

            {products.length > 5 && (
              <Box paddingBlockStart="200">
                <Text variant="bodySm" color="subdued" alignment="center">
                  Showing top 5 products. View all {products.length} products in detailed view.
                </Text>
              </Box>
            )}
          </BlockStack>
        </Box>
      </Card>
    </>
  );
}
