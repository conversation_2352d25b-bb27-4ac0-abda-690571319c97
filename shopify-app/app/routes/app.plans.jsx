import { authenticate } from "../shopify.server";
import { json } from "@remix-run/node";
import { useEffect, useCallback, useState } from "react";
import {
  useSubmit,
  useActionData,
  useLoaderData,
  useNavigate,
} from "@remix-run/react";
import {
  Page,
  Box,
  Card,
  Text,
  InlineStack,
  BlockStack,
  SkeletonDisplayText,
  Button,
  Modal,
  LegacyCard
} from "@shopify/polaris";

// import { getProductDetails } from "../models/getProductDetails.server";
import { planProductCount } from "../configs/config";
import content from "../locales/en.json";
import { checkPlanDetails } from "../models/checkPlanDetails.server";
import { pricingPlan } from "../models/planDetails.server";
import { trackButtonClick, usePageViewTracking } from "../helper/analytics";
export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const shopName = session.shop;

  return json({
    shopName: shopName,
  });
};

export const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const type = formData.get("type");
  try {
    if (type === "initial-fetch") {
      // const productData = await getProductDetails(admin);
      const pricingPlan = await checkPlanDetails(admin);
      return json({
        status: pricingPlan.status,
        message: pricingPlan.message,
        // data: productData.data.length,
        type: type,
        plan: pricingPlan,
      });
    } else if (type === "plan-update") {
      const planKey = formData.get("planKey");
      const actionData = await pricingPlan(admin, planKey);
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
        plan: planKey,
      });
    } 
  } catch (error) {
    return json({
      status: 400,
      message: "Something went wrong",
      data: null,
    });
  }
};

export default function Plans() {
  const submit = useSubmit();
  const actionData = useActionData();
  const navigate = useNavigate();
  const { shopName } = useLoaderData();

  const [productCount, setProductCount] = useState(0);
  const [planDetails, setPlanDetails] = useState(null);
  const [selectedPlan, setSelectedPlan] = useState(null);

  const [initialLoading, setInitialLoading] = useState(true);
  const [planUpdateLoading, setPlanUpdateLoading] = useState(false);

  const [downgradeModalOpen, setDowngradeModalOpen] = useState(false);
  
  const handleDowngradeModalClose = () => {
    setDowngradeModalOpen(false);
    setSelectedPlan(null);
  };

  useEffect(() => {
    const formData = new FormData();
    formData.append("type", "initial-fetch");
    submit(formData, { method: "post" });
    trackButtonClick("Initial Data Fetch", "Pricing", {
      shop_name: shopName,
    });
  }, []);

  useEffect(() => {
    if (actionData?.type === "initial-fetch" && actionData?.status === 200) {
      // setProductCount(actionData?.data);
      setPlanDetails(actionData?.plan?.data);
      setInitialLoading(false);
    } else if (
      actionData?.type === "initial-fetch" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setInitialLoading(false);
    }
  }, [actionData]);

  const handlePlanUpdate = (planKey, index) => {
    if (planKey === "freePlan") {
      setSelectedPlan({ key: planKey, index });
      setDowngradeModalOpen(true);
      return;
    }

    setPlanUpdateLoading((prev) => ({
      ...prev,
      [index]: true,
    }));
    submitPlanUpdate(planKey);
  };

  const submitPlanUpdate = (planKey) => {
    const formData = new FormData();
    formData.append("type", "plan-update");
    formData.append("planKey", planKey);
    submit(formData, { method: "post" });
    trackButtonClick("Plan Update", "Pricing", {
      shop_name: shopName,
    });
  };

  const handleDowngradeConfirm = () => {
    if (selectedPlan) {
      setPlanUpdateLoading((prev) => ({
        ...prev,
        [selectedPlan.index]: true,
      }));
      submitPlanUpdate(selectedPlan.key);
      handleDowngradeModalClose();
    }
  };

  useEffect(() => {
    if (actionData?.type === "plan-update" && actionData?.status === 200) {
      if (actionData?.plan === "freePlan") {
        setPlanUpdateLoading(false);
        navigate("/app/home");
        return;
      } else if (actionData?.plan !== "freePlan") {
        window.parent.location.href = actionData?.data;
        setPlanUpdateLoading(false);
      }
    } else if (
      actionData?.type === "plan-update" &&
      actionData?.status !== 200
    ) {
      setPlanUpdateLoading(false);
    }
  }, [actionData]);

  usePageViewTracking('Pricing Page', {shop_name: shopName});

  return (
    <Page fullWidth>
      <BlockStack gap="300">
        <Card sectioned>
          <Text variant="headingLg" as="h5" fontWeight="semibold">
            {content.Rank_collections.plans_page.heading}
          </Text>
        </Card>

        {!initialLoading ? (
          <Box
            style={{
              display: "flex",
              height: "100%",
              gap: "1rem",
              justifyContent: "center",
              flexWrap: "wrap",
              alignItems: "stretch",
            }}
          >
            {content.Rank_collections.plans_page.planDetails
              .filter(
                (plan) =>
                  plan.planKey === "freePlan" || plan.planKey === "automatic",
              )
              .map((plan, index) => (
                <Box
                  key={index}
                  style={{
                    width: "350px",
                    backgroundColor: "#ffffff",
                    padding: "16px",
                    borderRadius: "8px",
                    boxShadow:
                      "0px 1px 3px rgba(23, 24, 24, 0.1), 0px 1px 2px rgba(23, 24, 24, 0.06)",
                    border: "1px solid rgba(223, 227, 232, 1)",
                  }}
                >
                  <Box
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      height: "100%",
                      gap: "10px",
                    }}
                  >
                    <Text variant="bodyMd" as="p" fontWeight="semibold">
                      {plan.planName}
                    </Text>
                    <InlineStack gap="200" blockAlign="start">
                      <Text variant="headingLg" as="h5" fontWeight="bold">
                        {plan.planKey !== "freePlan" && "$ "} {plan.amount}
                      </Text>
                      <Text variant="bodyMd" as="span" fontWeight="subdued">
                        {plan.planKey !== "freePlan" && "/month"}
                      </Text>
                    </InlineStack>
                    <Text variant="bodyMd" as="p" fontWeight="subdued">
                      {plan.planDescription}
                    </Text>
                    {plan.features.map((items, index) => (
                      <InlineStack key={index} gap="200" blockAlign="center">
                        <Text variant="headingLg" as="h6" fontWeight="regular">
                          &#10004;
                        </Text>
                        <Text variant="bodyLg" as="p" fontWeight="regular">
                          {items}
                        </Text>
                      </InlineStack>
                    ))}
                    <Box style={{ marginTop: "auto" }}>
                      <Button
                        fullWidth
                        variant="primary"
                        size="large"
                        disabled={
                          (planDetails?.isFreePlan &&
                            plan.planKey === "freePlan") ||
                          (planDetails?.isAutomaticPlan &&
                            plan.planKey === "automatic")
                        }
                        onClick={() => handlePlanUpdate(plan.planKey, index)}
                        loading={planUpdateLoading[index]}
                      >
                        {planDetails?.isFreePlan && plan.planKey === "freePlan"
                          ? "Subscribed"
                          : planDetails?.isAutomaticPlan &&
                            plan.planKey === "automatic"
                          ? content.Rank_collections.plans_page.button_2
                          : content.Rank_collections.plans_page.button_1}
                      </Button>
                    </Box>
                  </Box>
                </Box>
              ))}
          </Box>
        ) : (
          // Loading skeleton remains the same
          <Box
            style={{
              display: "flex",
              height: "100%",
              gap: "1rem",
              justifyContent: "center",
              alignItems: "stretch",
              flexWrap: "wrap",
            }}
          >
            {content.Rank_collections.plans_page.planDetails
              .filter(
                (plan) =>
                  plan.planKey === "freePlan" || plan.planKey === "automatic",
              )
              .map((plan, index) => (
                <Box
                  key={index}
                  style={{
                    width:'350px',
                    backgroundColor: "#ffffff",
                    padding: "16px",
                    borderRadius: "8px",
                    boxShadow:
                      "0px 1px 3px rgba(23, 24, 24, 0.1), 0px 1px 2px rgba(23, 24, 24, 0.06)",
                    border: "1px solid rgba(223, 227, 232, 1)",
                    minWidth: "300px",
                  }}
                >
                  <Box
                    style={{
                      display: "flex",
                      flexDirection: "column",
                      height: "100%",
                      gap: "10px",
                    }}
                  >
                    <SkeletonDisplayText size="small" maxWidth="100%" />
                    <SkeletonDisplayText size="small" maxWidth="50%" />
                    <SkeletonDisplayText size="small" maxWidth="100%" />
                    {plan.features.map((items, index) => (
                      <BlockStack gap="300" key={index}>
                        <SkeletonDisplayText size="small" maxWidth="100%" />
                      </BlockStack>
                    ))}
                    <Box style={{ marginTop: "auto" }}>
                      <SkeletonDisplayText maxWidth="100%" size="medium" />
                    </Box>
                  </Box>
                </Box>
              ))}
          </Box>
        )}
      </BlockStack>
      
      <Modal
        open={downgradeModalOpen}
        onClose={handleDowngradeModalClose}
        title="Confirm Downgrade"
        primaryAction={{
          content: "Downgrade to Free Plan",
          onAction: handleDowngradeConfirm,
          destructive: true,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: handleDowngradeModalClose,
          },
        ]}
      >
        <Modal.Section>
          <BlockStack gap="400">
            <Text as="p">
              {content.Rank_collections.plans_page.content_1}
            </Text>
            <ul style={{ paddingLeft: "20px" }}>
              <li>
                <Text as="p"> {content.Rank_collections.plans_page.content_2}</Text>
              </li>
              <li>
                <Text as="p"> {content.Rank_collections.plans_page.content_3}</Text>
              </li>
              <li>
                <Text as="p"> {content.Rank_collections.plans_page.content_4}</Text>
              </li>
            </ul>
          </BlockStack>
        </Modal.Section>
      </Modal>
    </Page>
  );
}
