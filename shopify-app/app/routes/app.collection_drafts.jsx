import { useEffect, useState, useCallback, useMemo, useRef } from "react";
import { json } from "@remix-run/node";
import {
  useActionData,
  useSubmit,
  useNavigate,
  useLoaderData,
} from "@remix-run/react";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import {
  Text,
  Button,
  IndexTable,
  TextField,
  Select,
  Pagination,
  Badge,
  Checkbox,
  Page,
  Card,
  Box,
  Spinner,
  InlineStack,
  Icon,
  Popover,
  BlockStack,
  Tooltip,
  DropZone,
  Divider,
  EmptyState,
  Tabs,
  LegacyFilters,
  ActionList,
  ButtonGroup,
  Layout,
  Banner,
  SkeletonBodyText,
  SkeletonDisplayText,
  Frame,
  Toast,
} from "@shopify/polaris";
import {
  DeleteIcon,
  EditIcon,
  EyeCheckMarkIcon,
  SettingsIcon,
  HideIcon,
  SearchIcon,
  FileIcon,
  PlusIcon,
  SortIcon,
  ImportIcon,
  ExportIcon,
  FilterIcon,
} from "@shopify/polaris-icons";
import content from "../locales/en.json";

// Import helpers
import { authenticate } from "../shopify.server";
import { createCapitalizer } from "../helper/capitalizer";
import { formatDate } from "../helper/formatDateAndTime";
import { trackButtonClick, usePageViewTracking } from "../helper/analytics";

// Import components
import IndividualEditCollection from "../components/manualEditModal";
import AutoScheduleCollectionsModal from "../components/autoScheduleCollectionModal";
import ScheduleCollections from "../components/scheduleCollections";
import ProductsViewModalWithSEO from "../components/collectionDetails";

// Import server-side functions
import { collectionDataFetchForCollectionDrafts } from "../models/fetchCollectionDataForCollectionDrafts.server";
import { synctSchedulingInputsToAgent } from "../models/syncSchedulingInputsToAgent.server";
import { individualCollectionDelete } from "../models/individualCollectionDelete.server";
import { individualCollectionPublish } from "../models/individualCollectionPublish.server";
import { individualCollectionUnpublish } from "../models/individualCollectionUnpublish.server";
import { individualCollectionUpdate } from "../models/individualCollectionUpdate.server";
import { collectionsBulkDelete } from "../models/collectionsBulkDelete.server";
import { importCSVFile } from "../models/importCSVFile.server";
import { exportCollection } from "../models/exportCollection.server";
import { fetchScheduleConfig } from "../models/fetchScheduleConfig.server";
import { fetchBestScheduleTime } from "../models/fetchBestScheduleTime.server";
import { fetchSingleCollectionDetails } from "../models/fetchSingleCollectionDetails.server";
import { getProductDetailsForCollectionDetails } from "../models/collectionDetailsPageProducts.server";
import { productDeleteFromCollection } from "../models/productsDeleteFromCollection.server";
import { addProductToSingleCollection } from "../models/addProductToSingleCollection.server";
import { updateCollectionSortingValue } from "../models/updateTheCollectionSortingValue.server";
import { syncCompetitorURL } from "../models/syncCompetitorURLToAgent.server";
import { manualGenerate } from "../models/manualGenerate.server";
import { fetchTaskStatus } from "../models/fetchTaskStatusById.server";
import { checkCollectionLimit } from "../models/collectionLimitCheck.server";
import { fetchProductSyncStatusFromAgent } from "../models/fetchProductSyncStatus.server";
import { fetchSingleProductDetails } from "../models/fetchSingleProductDetails.server";

// Server-side loader function
export const loader = async ({ request }) => {
  const { admin, session } = await authenticate.admin(request);
  const shopName = session.shop;

  // Run all functions in parallel
  const [collectionLimit, config, schedule] = await Promise.all([
    checkCollectionLimit(admin),
    fetchScheduleConfig(admin),
    fetchBestScheduleTime(admin),
  ]);

  return json({
    shopName,
    storeDomain: session.shop,
    collectionLimit: collectionLimit?.data || 0,
    configs: config,
    schedules: schedule,
  });
};

// Server-side action function
export const action = async ({ request }) => {
  const { admin, session } = await authenticate.admin(request);
  const formData = await request.formData();
  const type = formData.get("type");

  switch (type) {
    case "initialFetch": {
      const page = formData.get("page") || 1;
      const option = formData.get("option") || "all";
      const searchValue = formData.get("searchValue") || "";
      const id = formData.get("id") || null;
      let productSyncStatus;

      const actionData = await collectionDataFetchForCollectionDrafts(
        admin,
        page,
        10,
        option,
        searchValue,
      );

      if (id !== null) {
        productSyncStatus = await fetchProductSyncStatusFromAgent(admin, id);
      }

      return json({
        status: actionData.status,
        message: actionData.message,
        data: {
          collection: actionData.data,
          productSyncStatus: productSyncStatus?.data,
        },
        type: type,
      });
    }

    case "saveCron": {
      const attribute = formData.get("attributes");
      const scheduleSettings = formData.get("schedule-settings");
      const actionData = await synctSchedulingInputsToAgent(
        admin,
        attribute,
        scheduleSettings,
      );
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
      });
    }

    case "delete": {
      const id = formData.get("id");
      const response = await individualCollectionDelete(admin, id);
      return json({ type, ...response });
    }

    case "bulkDelete": {
      const id = formData.get("id");
      const response = await collectionsBulkDelete(admin, id);
      return json({ type, ...response });
    }

    case "publish": {
      const id = formData.get("id");
      const response = await individualCollectionPublish(admin, id);
      return json({ type, ...response });
    }

    case "unpublish": {
      const id = formData.get("id");
      const response = await individualCollectionUnpublish(admin, id);
      return json({ type, ...response });
    }

    case "manualUpdate": {
      const id = formData.get("id");
      const name = formData.get("name");
      const url = formData.get("url");
      const description = formData.get("description");
      const faqs = formData.get("faqs");

      const response = await individualCollectionUpdate(
        admin,
        id,
        name,
        url,
        description,
        faqs,
      );

      return json({ type, ...response });
    }

    case "file-import": {
      const file = formData.get("file");
      if (!file || !(file instanceof File)) {
        throw new Error("No Valid File provided");
      }
      const response = await importCSVFile(admin, file, session.shop);
      return json({ type, ...response });
    }

    case "exportCollections": {
      const response = await exportCollection(admin, session.shop);
      return json({ type, ...response });
    }

    case "fetch-collection-details": {
      const collectionId = formData.get("id");
      const cursor = formData.get("cursor");

      const actionData = await fetchSingleCollectionDetails(
        admin,
        collectionId,
      );
      const product = await getProductDetailsForCollectionDetails(
        admin,
        cursor,
      );

      return json({
        status: actionData.status || product.status,
        message: actionData.message || product.message,
        data: {
          collectionDetail: actionData.data,
          product: product.data?.products || [],
          page: product.data?.hasNextPage || false,
          cursor: product.data?.endCursor || null,
        },
        type: type,
      });
    }

    case "sync-url": {
      const url = formData.get("url");
      const actionData = await syncCompetitorURL(admin, url);
      return json({
        status: actionData?.status,
        message: actionData?.message,
        data: actionData?.data,
        type: type,
      });
    }

    case "delete-product": {
      const productId = formData.get("productId");
      const collectionId = formData.get("collectionId");

      const actionData = await productDeleteFromCollection(
        admin,
        collectionId,
        productId,
      );
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
      });
    }

    case "manual-generate": {
      const competitorUrl = formData.get("competitorUrl");
      const attributes = formData.get("attributes");
      const actionData = await manualGenerate(admin, competitorUrl, attributes);
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type,
      });
    }

    case "add-products": {
      const collectionId = formData.get("collectionId");
      const productId = formData.get("productId");

      const actionData = await addProductToSingleCollection(
        admin,
        collectionId,
        productId,
      );
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
      });
    }

    case "task-status": {
      const id = formData.get("id");
      const jobStatus = await fetchTaskStatus(admin, id);
      return json({
        status: jobStatus.status,
        message: jobStatus.message,
        data: jobStatus.data,
        type,
      });
    }

    case "sort-update": {
      const option = formData.get("option");
      const id = formData.get("id");
      const actionData = await updateCollectionSortingValue(admin, id, option);
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
      });
    }

    case "fetch-product-sync": {
      const id = formData.get("id");
      const actionData = await fetchProductSyncStatusFromAgent(admin, id);
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
      });
    }

    case "add-products-fetch": {
      const cursor = formData.get("cursor");
      const actionData = await getProductDetailsForCollectionDetails(
        admin,
        cursor,
      );
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
      });
    }

    case "fetch-db-collection-product": {
      const id = formData.get("id");
      const actionData = await fetchSingleProductDetails(admin, id);
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type: type,
      });
    }

    default:
      return json({
        status: 400,
        message: "Something went wrong",
        data: null,
      });
  }
};

// Client component
export default function CollectionDrafts() {
  const submit = useSubmit();
  const actionData = useActionData();
  const navigate = useNavigate();
  const { shopName, storeDomain, collectionLimit, configs, schedules } =
    useLoaderData();

  // Track page view
  usePageViewTracking("Collection Drafts", { shop_name: shopName });

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [spinnerLoading, setSpinnerLoading] = useState(false);
  const [cronLoading, setCronLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false);
  const [collectionUpdateLoading, setCollectionUpdateLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState({});
  const [publishLoading, setPublishLoading] = useState({});
  const [unpublishLoading, setUnpublishLoading] = useState({});
  const [importLoading, setImportLoading] = useState(false);
  const [manualGenerateLoading, setManualGenerateLoading] = useState(false);
  const [competitorSyncLoading, setCompetitorSyncLoading] = useState(false);

  // Main data states
  const [collections, setCollections] = useState([]);
  const [collectionDataFromDB, setCollectionDataFromDB] = useState([]);
  const [selectedCollections, setSelectedCollections] = useState([]);
  const [selectedCollectionIndex, setSelectedCollectionIndex] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [searchValue, setSearchValue] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedOptions, setSelectedOptions] = useState("all");
  const [selectedTab, setSelectedTab] = useState(0);

  // UI states
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [appliedFilters, setAppliedFilters] = useState([]);
  const [actionMenuOpen, setActionMenuOpen] = useState(false);
  const [toastMessage, setToastMessage] = useState(null);
  const [toastError, setToastError] = useState(false);

  // Modal states from old code
  const [autoGenerateModalOpen, setAutoGenerateModalOpen] = useState(false);
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [bulkDeleteModalOpen, setBulkDeleteModalOpen] = useState(false);
  const [individualEditModalOpen, setIndividualEditModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [unpublishModalOpen, setUnpublishModalOpen] = useState(false);
  const [collectionDetailsModalOpen, setCollectionDetailsModalOpen] =
    useState(false);
  const [scheduleOpen, setScheduleOpen] = useState(false);
  const [upgradeModalOpen, setUpgradeModalOpen] = useState(false);
  const [infoModalOpen, setInfoModalOpen] = useState(false);
  const [limitReachedModal, setLimitReachedModal] = useState(false);
  const [productSyncModalOpen, setProductSyncModalOpen] = useState(false);

  // Popover states
  const [importPopoverActive, setImportPopoverActive] = useState(false);

  // Collection details states
  const [collectionLoading, setCollectionLoading] = useState(false);
  const [collectionDetails, setCollectionDetails] = useState([]);
  const [filteredProducts, setFilterProducts] = useState([]);
  const [productDeleteLoading, setProductDeleteLoading] = useState({});
  const [addProductLoading, setAddProductLoading] = useState(false);
  const [addProductsLoading, setAddProductsLoading] = useState(false);
  const [hideAddProduct, setHideAddProduct] = useState(false);
  const [endCursor, setEndCursor] = useState(null);
  const [hasNextPage, setHasNextPage] = useState(false);

  // Task and sync states
  const [taskId, setTaskId] = useState(null);
  const [productSyncId, setProductSyncId] = useState(null);
  const [isUrlSynced, setIsUrlSynced] = useState(false);

  // Schedule and settings states
  const [scheduleConfigs, setScheduleConfigs] = useState({});
  const [from, setFrom] = useState("");
  const [schedule, setSchedule] = useState({});
  const [sortUpdateLoading, setSortUpdateLoading] = useState(false);
  const [sortValue, setSortValue] = useState(null);
  const [collectionIndex, setCollectionIndex] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState(null);

  // File upload states
  const [isDropZoneOpen, setDropZoneOpen] = useState(false);
  const [file, setFile] = useState(null);

  // Refs for task status checking
  const initialFetchCalled = useRef(false);
  const singleTimeCallRef = useRef(false);

  // Modal states - keeping new structure
  const [modals, setModals] = useState({
    delete: false,
    bulkDelete: false,
    unpublish: false,
    individualEdit: false,
    collectionDetails: false,
    schedule: false,
    import: false,
    limitReached: false,
    upgradeModal: false,
  });

  // Loading states for actions - keeping new structure
  const [actionLoading, setActionLoading] = useState({
    delete: {},
    publish: {},
    unpublish: {},
    update: false,
    bulkDelete: false,
    export: false,
  });

  // Open and close modals
  const openModal = (modalName, index = null) => {
    if (index !== null) {
      setSelectedCollectionIndex(index);
    }
    setModals({ ...modals, [modalName]: true });
  };

  const closeModal = (modalName) => {
    setModals({ ...modals, [modalName]: false });
    if (modalName === "individualEdit" || modalName === "collectionDetails") {
      setSelectedCollectionIndex(null);
    }
  };

  // Status options from content
  const statusOptions =
    content.Rank_collections.collection_gallery.status_options.map(
      (option) => ({
        label: option.label,
        value: option.value,
      }),
    );

  // Handler functions from old codebase
  const handleStatusOptionsChange = useCallback(
    (options) => setSelectedOptions(options),
    [],
  );

  const handleSearchValueChange = useCallback(
    (searchValue) => {
      setSearchValue(searchValue);

      // Trigger search with debouncing effect
      if (searchValue.trim() !== "") {
        // Set a small delay for better UX
        setTimeout(() => {
          setSpinnerLoading(true);
          const formData = new FormData();
          formData.append("type", "initialFetch");
          formData.append("page", 1); // Reset to page 1 for search
          formData.append("option", selectedOptions);
          formData.append("searchValue", searchValue);
          if (productSyncId) {
            formData.append("id", productSyncId);
          }
          submit(formData, { method: "post" });
          setCurrentPage(1); // Reset current page
        }, 300);
      }
    },
    [selectedOptions, productSyncId, submit],
  );

  // Handle search clear button
  const handleSearchClear = useCallback(() => {
    setSearchValue("");
    setSpinnerLoading(true);

    // Fetch initial collections based on current tab selection
    const formData = new FormData();
    formData.append("type", "initialFetch");
    formData.append("page", 1);
    formData.append("option", selectedOptions);
    formData.append("searchValue", "");
    if (productSyncId) {
      formData.append("id", productSyncId);
    }
    submit(formData, { method: "post" });
    setCurrentPage(1);
  }, [selectedOptions, productSyncId, submit]);

  const handleInitialFetch = useCallback(() => {
    const id = localStorage.getItem("product_sync_task_id");
    setProductSyncId(id);
    trackButtonClick("Initial Data Fetch", "Collection Drafts", {
      shop_name: shopName,
    });
    setSpinnerLoading(true);
    const formData = new FormData();
    formData.append("type", "initialFetch");
    formData.append("id", id);
    formData.append("page", currentPage);
    formData.append("option", selectedOptions);
    formData.append("searchValue", searchValue);
    submit(formData, { method: "post" });
  }, [currentPage, selectedOptions, searchValue, shopName, submit]);

  const handleSearchKeyDown = (event) => {
    if (event.key === "Enter") {
      trackButtonClick(
        "Search value based Collection filter",
        "Collection Drafts",
        { shop_name: shopName },
      );
      setSpinnerLoading(true);
      const formData = new FormData();
      formData.append("type", "initialFetch");
      formData.append("page", currentPage);
      formData.append("option", selectedOptions);
      formData.append("searchValue", searchValue);
      submit(formData, { method: "post" });
    }
  };

  const handleFetchProductSyncStatus = () => {
    const formData = new FormData();
    formData.append("type", "fetch-product-sync");
    formData.append("id", productSyncId);
    submit(formData, { method: "post" });
  };

  // Modal handlers from old code
  const handleScheduleModalOpen = () => setScheduleOpen(true);
  const handleScheduleModalClose = () => setScheduleOpen(false);
  const handleLimitModalOpen = () => setLimitReachedModal(true);
  const handleLimitModalClose = () => setLimitReachedModal(false);
  const handleProductSyncModalOpen = () => setProductSyncModalOpen(true);
  const handleProductSyncModalClose = () => setProductSyncModalOpen(false);
  const handleUpgradeModalOpen = () => setUpgradeModalOpen(true);
  const handleUpgradeModalClose = () => setUpgradeModalOpen(false);
  const handleInfoModalOpen = () => setInfoModalOpen(true);
  const handleInfoModalClose = () => setInfoModalOpen(false);

  const handleAutoGenerateModalOpen = (from) => {
    setAutoGenerateModalOpen(true);
    setFrom(from);
  };

  const handleAutoGenerateModalClose = () => {
    setAutoGenerateModalOpen(false);
    setFrom("");
  };

  const handleScheduleModal = (schedule) => {
    setSchedule(schedule);
    handleScheduleModalClose();
    handleAutoGenerateModalOpen("autoSchedule");
  };

  // Import/Export handlers
  const handleImportPopoverToggle = () => {
    setImportPopoverActive((prev) => !prev);
  };

  const handleImportModalOpen = () => {
    setImportModalOpen(true);
  };

  const handleImportModalClose = () => {
    setImportModalOpen(false);
    setDropZoneOpen(false);
  };

  const downloadExampleFile = () => {
    const url =
      "https://ik.imagekit.io/1tudtg11f/collections.csv?updatedAt=1728402602163";
    const anchor = document.createElement("a");
    anchor.href = url;
    anchor.download = "collections.csv";
    document.body.appendChild(anchor);
    anchor.click();
    document.body.removeChild(anchor);
  };

  const handleImportCollection = () => {
    trackButtonClick("Import Collection", "Collection Drafts", {
      shop_name: shopName,
    });
    setImportLoading(true);
    if (file === null) {
      shopify.toast.show("Please upload any one of CSV file.", {
        isError: true,
      });
      setImportLoading(false);
    } else {
      const formData = new FormData();
      formData.append("type", "file-import");
      formData.append("file", file);
      submit(formData, { method: "post", encType: "multipart/form-data" });
    }
  };

  const handleExportCollections = () => {
    trackButtonClick("Export Collections", "Collection Draft", {
      shop_name: shopName,
    });
    setExportLoading(true);
    const formData = new FormData();
    formData.append("type", "exportCollections");
    submit(formData, { method: "post" });
  };

  // Bulk operations
  const handleBulkSelection = useCallback((collectionId) => {
    setSelectedCollections((prevCollections) => {
      if (prevCollections.includes(collectionId)) {
        return prevCollections.filter((item) => item !== collectionId);
      } else {
        return [...prevCollections, collectionId];
      }
    });
  }, []);

  const handleBulkDeleteModalOpen = () => {
    setBulkDeleteModalOpen(true);
  };
  const handleBulkDeleteModalClose = () => {
    setBulkDeleteModalOpen(false);
  };

  const handleBulkDelete = () => {
    if (selectedCollections.length > 0) {
      trackButtonClick("Delete Multiple Collections", "Collection Draft", {
        shop_name: shopName,
      });
      setBulkDeleteLoading(true);
      const formData = new FormData();
      formData.append("type", "bulkDelete");
      formData.append("id", selectedCollections);
      submit(formData, { method: "post" });
      handleBulkDeleteModalClose();
    } else {
      shopify.toast.show("Please select Some collections to delete", {
        isError: true,
      });
    }
  };

  // Individual collection operations
  const handleIndividualEditModalOpen = (index) => {
    setSelectedIndex(index);
    setIndividualEditModalOpen(true);
  };

  const handleIndividualEditModalClose = () => {
    setSelectedIndex(null);
    setIndividualEditModalOpen(false);
  };

  const handleUpdateCollection = (updatedData) => {
    trackButtonClick("Manually Update the Collection", "Collection Draft", {
      shop_name: shopName,
    });
    setCollectionUpdateLoading(true);
    const formData = new FormData();
    formData.append("type", "manualUpdate");
    formData.append("name", updatedData.name);
    formData.append("url", updatedData.url);
    formData.append("description", updatedData.description);
    formData.append("faqs", JSON.stringify(updatedData.faqs));
    formData.append("id", collectionDataFromDB[selectedIndex]?.id);
    submit(formData, { method: "post" });
  };

  const handleDeleteModalOpen = (index) => {
    setDeleteModalOpen(true);
    setSelectedIndex(index);
  };

  const handleDeleteModalClose = () => {
    setSelectedIndex(null);
    setDeleteModalOpen(false);
  };

  const handleDelete = (index) => {
    trackButtonClick("Delete Single Collection", "Collection Draft", {
      shop_name: shopName,
    });
    setDeleteLoading((prev) => ({
      ...prev,
      [index]: true,
    }));
    const formData = new FormData();
    formData.append("type", "delete");
    formData.append("id", collectionDataFromDB[index].id);
    submit(formData, { method: "post" });
    handleDeleteModalClose();
  };

  const handlePublish = (index) => {
    trackButtonClick("Manually Publish the Collection", "Collection Draft", {
      shop_name: shopName,
    });
    setPublishLoading((prev) => ({
      ...prev,
      [index]: true,
    }));
    const formData = new FormData();
    formData.append("type", "publish");
    formData.append("id", collectionDataFromDB[index].id);
    submit(formData, { method: "post" });
  };

  const handleUnpublishModalOpen = (index) => {
    setSelectedIndex(index);
    setUnpublishModalOpen(true);
  };

  const handleUnpublishModalClose = () => {
    setSelectedIndex(null);
    setUnpublishModalOpen(false);
  };

  const handleUnpublish = (index) => {
    trackButtonClick("Manually Unpublish the Collection", "Collection Draft", {
      shop_name: shopName,
    });
    setUnpublishLoading((prev) => ({
      ...prev,
      [index]: true,
    }));
    const formData = new FormData();
    formData.append("type", "unpublish");
    formData.append("id", collectionDataFromDB[index].id);
    submit(formData, { method: "post" });
    handleUnpublishModalClose();
  };

  // Collection details handlers
  const handleCollectionDetailsModalOpen = (index, from) => {
    setCollectionDetailsModalOpen(true);
    handleFetchCollectionDetails(index, from);
  };

  const handleCollectionDetailsModalClose = () => {
    setCollectionDetailsModalOpen(false);
    setCollectionDetails([]);
    setFilterProducts([]);
    setCollectionIndex(null);
    setCollectionLoading(false);
    setSortValue(null);
  };

  // Task status and sync handlers
  const handleCheckTaskStatus = () => {
    const formData = new FormData();
    formData.append("type", "task-status");
    formData.append("id", taskId);
    submit(formData, { method: "post" });
    trackButtonClick("Fetching Task Status", "Collection Drafts", {
      shop_name: shopName,
    });
  };

  const handleManualGenerate = (manualCreatePayload) => {
    setManualGenerateLoading(true);
    const { competitorUrl, ...restofAttributes } = manualCreatePayload;
    const formData = new FormData();
    formData.append("type", "manual-generate");
    formData.append("competitorUrl", competitorUrl);
    formData.append("attributes", JSON.stringify(restofAttributes));
    submit(formData, { method: "post" });
    trackButtonClick("Manually Generate the Collection", "Collection Drafts", {
      shop_name: shopName,
    });
  };

  const handleSycnCompetitorUrl = (url) => {
    setCompetitorSyncLoading(true);
    const formData = new FormData();
    formData.append("type", "sync-url");
    formData.append("url", JSON.stringify(url.url));
    submit(formData, { method: "post" });
    trackButtonClick("Competitor URL Sync", "Collection Drafts", {
      shop_name: shopName,
    });
  };

  const handleCronUpdate = (updatedData) => {
    trackButtonClick("Update the cron Values", "Collection Drafts", {
      shop_name: shopName,
    });
    setCronLoading(true);
    const { schedule_settings, ...restOfOnUpdate } = updatedData;

    const formData = new FormData();
    formData.append("type", "saveCron");
    formData.append("attributes", JSON.stringify(restOfOnUpdate));
    formData.append("schedule-settings", JSON.stringify(schedule_settings));
    submit(formData, { method: "post" });
  };

  const handleFetchCollectionDetails = (index, from) => {
    if (from === "shopifySide") {
      setHideAddProduct(false);
      setCollectionLoading(true);
      const formData = new FormData();
      const collectionId = collectionDataFromDB[index]?.id;
      formData.append("type", "fetch-collection-details");
      formData.append("id", collectionId);
      formData.append("cursor", null);
      submit(formData, { method: "post" });
      trackButtonClick("Fetch Single Collection Details", "Collection Drafts", {
        shop_name: shopName,
      });
    } else {
      setCollectionLoading(true);
      setHideAddProduct(true);
      const selectedCollection = collectionDataFromDB[index];
      const collectionId = selectedCollection.id;
      const formData = new FormData();
      formData.append("type", "fetch-db-collection-product");
      formData.append("id", collectionId);
      submit(formData, { method: "post" });
      trackButtonClick("Fetch Single Collection Details", "Collection Drafts", {
        shop_name: shopName,
      });
    }
  };

  useEffect(() => {
    if (actionData?.type === "sort-update" && actionData?.status === 200) {
      shopify.toast.show(actionData?.message);
      setSortUpdateLoading(false);
      setSortValue(actionData?.data);
    } else if (
      actionData?.type === "sort-update" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setSortUpdateLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.type === "add-products-fetch") {
      // Always set loading to false when we receive any response
      setAddProductsLoading(false);

      if (actionData?.status === 200) {
        const newFetchedProducts = actionData?.data?.products || [];

        setFilterProducts((prevProducts) => {
          const existingIds = new Set(
            prevProducts.map((product) => product.id),
          );
          const uniqueProducts = newFetchedProducts.filter(
            (product) => !existingIds.has(product.id),
          );
          return [...prevProducts, ...uniqueProducts];
        });

        // Update pagination state
        const newHasNextPage = actionData?.data?.hasNextPage === true;
        setEndCursor(actionData?.data?.endCursor);
        setHasNextPage(newHasNextPage);
      } else if (actionData?.status === 400) {
        shopify.toast.show(actionData?.message, { isError: true });
      }
    }
  }, [actionData]);

  

  // Fetch collections on initial load and when page, search, or status changes
  useEffect(() => {
    handleInitialFetch();
  }, [currentPage, selectedOptions]);

  // Handle action data response from old codebase
  useEffect(() => {
    if (actionData?.type === "initialFetch" && actionData?.status === 200) {
      setCollectionDataFromDB(actionData?.data?.collection?.collectionData);
      setTotalCount(actionData?.data?.collection?.totalCount);
      setSpinnerLoading(false);
      if (
        actionData?.data?.productSyncStatus?.syncStatus === "COMPLETED" ||
        actionData?.data?.productSyncStatus?.syncStatus === "FAILED"
      ) {
        localStorage.removeItem("product_sync_task_id");
        setProductSyncId(null);
      } else if (
        actionData?.data?.productSyncStatus?.syncStatus === "PROCESSING" ||
        actionData?.data?.productSyncStatus?.syncStatus === "QUEUED"
      ) {
        handleFetchProductSyncStatus();
      }
    } else if (
      actionData?.type === "initialFetch" &&
      actionData?.status === 400
    ) {
      setSpinnerLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData]);

  // Product sync status handling
  useEffect(() => {
    if (
      actionData?.type === "fetch-product-sync" &&
      actionData?.status === 200
    ) {
      if (
        actionData?.data?.syncStatus === "COMPLETED" ||
        actionData?.data?.syncStatus === "FAILED"
      ) {
        localStorage.removeItem("product_sync_task_id");
        setProductSyncId(null);
      } else if (
        actionData?.data?.syncStatus === "PROCESSING" ||
        actionData?.data?.syncStatus === "QUEUED"
      ) {
        const timeoutId = setTimeout(
          () => handleFetchProductSyncStatus(),
          10000,
        );
        return () => clearTimeout(timeoutId);
      }
    } else if (
      actionData?.type === "fetch-product-sync" &&
      actionData?.status !== 200
    ) {
      localStorage.removeItem("product_sync_task_id");
      setProductSyncId(null);
    }
  }, [actionData]);

  // Cron update handling
  useEffect(() => {
    if (actionData?.type === "saveCron" && actionData?.status === 200) {
      const storedData = localStorage.getItem("initial-data");
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        parsedData.data.scheduleStatus = true;
        parsedData.data.frequency =
          actionData?.data?.schedule_settings?.frequency;
        parsedData.data.count = actionData?.data?.schedule_settings?.count;
        parsedData.data.impact =
          actionData?.data?.schedule_settings?.impact_type;
        parsedData.data.customer = actionData?.data?.attributes?.customer;
        parsedData.data.location = actionData?.data?.attributes?.location;
        parsedData.data.market = actionData?.data?.attributes?.market;
        parsedData.data.product = actionData?.data?.attributes?.product;
        parsedData.data.seasonal = actionData?.data?.attributes?.seasonal;
        localStorage.setItem("initial-data", JSON.stringify(parsedData));
      }
      setCronLoading(false);
      shopify.toast.show(actionData?.message);
    } else if (actionData?.type === "saveCron" && actionData?.status === 400) {
      setCronLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData]);

  // File import handling
  useEffect(() => {
    if (actionData?.type === "file-import" && actionData?.status === 200) {
      setImportLoading(false);
      shopify.toast.show(actionData?.message);
      setFile(null);
      setImportModalOpen(false);
      handleInitialFetch();
    } else if (
      actionData?.type === "file-import" &&
      actionData?.status === 400
    ) {
      setImportLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData]);

  // Export collections handling
  useEffect(() => {
    if (
      actionData?.type === "exportCollections" &&
      actionData?.status === 200
    ) {
      const fileUrl = actionData.data?.url;
      const fileName = actionData.data?.fileName;

      if (fileUrl && fileName) {
        const link = document.createElement("a");
        link.href = fileUrl;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
      shopify.toast.show(actionData?.message);
      setExportLoading(false);
    } else if (
      actionData?.type === "exportCollections" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setExportLoading(false);
    }
  }, [actionData]);

  // Bulk delete handling
  useEffect(() => {
    if (actionData?.type === "bulkDelete") {
      if (actionData?.status === 200) {
        const deletedIdsSet = new Set(actionData.data);
        setCollectionDataFromDB((prevData) => {
          return prevData.filter((product) => !deletedIdsSet.has(product.id));
        });
        setTotalCount((prevCount) => prevCount - actionData.data.length);
        shopify.toast.show(actionData.message);
        setSelectedCollections([]);
      } else if (actionData?.status === 400) {
        shopify.toast.show(actionData.message, { isError: true });
      }
      setBulkDeleteLoading(false);
    }
  }, [actionData]);

  // Manual update handling
  useEffect(() => {
    if (actionData?.type === "manualUpdate") {
      if (actionData?.status === 200) {
        shopify.toast.show(actionData?.message);
        const id = actionData?.data?.id;
        setCollectionDataFromDB((prevData) => {
          return prevData.map((collection) => {
            if (collection.id === id) {
              return {
                ...collection,
                collection_name: actionData?.data?.name,
                collection_url: actionData?.data?.url,
                description: actionData?.data?.description,
              };
            }
            return collection;
          });
        });
        setCollectionUpdateLoading(false);
      } else if (actionData?.status === 400) {
        setCollectionUpdateLoading(false);
        shopify.toast.show(actionData?.message, { isError: true });
      }
    }
  }, [actionData]);

  // Publish handling
  useEffect(() => {
    if (actionData?.type === "publish" && actionData?.status === 200) {
      setCollectionDataFromDB((prevData) =>
        prevData.map((collection) =>
          collection.id === actionData?.data?.id
            ? {
                ...collection,
                status: actionData?.data?.status,
                published_at: actionData?.data?.publishedAt,
              }
            : collection,
        ),
      );
      setPublishLoading(false);
      shopify.toast.show(actionData?.message);
    } else if (actionData?.type === "publish" && actionData?.status === 400) {
      setPublishLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData]);

  // Unpublish handling
  useEffect(() => {
    if (actionData?.type === "unpublish" && actionData?.status === 200) {
      setCollectionDataFromDB((prevData) =>
        prevData.map((collection) =>
          collection.id === actionData?.data?.id
            ? {
                ...collection,
                status: actionData?.data?.status,
                published_at: actionData?.data?.publishedAt,
              }
            : collection,
        ),
      );
      setUnpublishLoading(false);
      shopify.toast.show(actionData?.message);
    } else if (actionData?.type === "unpublish" && actionData?.status === 400) {
      setUnpublishLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData]);

  // Delete handling
  useEffect(() => {
    if (actionData?.type === "delete" && actionData?.status === 200) {
      const id = parseInt(actionData?.data, 10);
      setDeleteLoading(false);
      setCollectionDataFromDB((prevData) => {
        const newData = prevData.filter((product) => product.id !== id);
        return [...newData];
      });
      setTotalCount((prveCount) => prveCount - 1);
      shopify.toast.show(actionData.message);
    } else if (actionData?.type === "delete" && actionData?.status === 400) {
      setDeleteLoading(false);
      shopify.toast.show(actionData.message, { isError: true });
    }
  }, [actionData]);

  // Competitor URL sync handling
  useEffect(() => {
    if (actionData?.type === "sync-url" && actionData?.status === 200) {
      shopify.toast.show(actionData?.message);
      const storedData = localStorage.getItem("initial-data");
      const parsedData = JSON.parse(storedData);
      parsedData.data.urlSync = actionData?.data;
      localStorage.setItem("initial-data", JSON.stringify(parsedData));
      setIsUrlSynced(actionData?.data);
      setCompetitorSyncLoading(false);
    } else if (actionData?.type === "sync-url" && actionData?.status !== 200) {
      shopify.toast.show(actionData?.message, { isError: true });
      setCompetitorSyncLoading(false);
    }
  }, [actionData]);

  // Manual generate handling
  useEffect(() => {
    if (actionData?.type === "manual-generate" && actionData?.status === 200) {
      shopify.toast.show(content.Rank_collections.collection_gallery.toast_1);
      setManualGenerateLoading(false);
      setTaskId(actionData?.data?.task_id);
      localStorage.setItem(
        "manual-generate-task-id",
        actionData?.data?.task_id,
      );
      handleAutoGenerateModalClose();
    } else if (
      actionData?.type === "manual-generate" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.data, { isError: true });
      setManualGenerateLoading(false);
    }
  }, [actionData]);

  // Task status handling
  useEffect(() => {
    if (actionData?.type === "task-status" && actionData?.status === 200) {
      if (actionData?.data === "STARTED" && !initialFetchCalled.current) {
        handleInitialFetch();
        initialFetchCalled.current = true;
      }
      if (
        actionData?.data === "COMPLETED" ||
        actionData?.data === "FAILED" ||
        actionData?.data === "NO_COLLECTIONS"
      ) {
        localStorage.removeItem("manual-generate-task-id");
        setTaskId(null);
        handleInitialFetch();
        if (
          actionData?.data === "NO_COLLECTIONS" ||
          actionData?.data === "FAILED"
        ) {
          shopify.toast.show(
            "No valid products available for the the given keyword",
            { isError: true },
          );
        }
      } else {
        setTimeout(handleCheckTaskStatus, 10000);
      }
    } else if (
      actionData?.type === "task-status" &&
      actionData?.status !== 200
    ) {
      if (!singleTimeCallRef.current) {
        handleInitialFetch();
        singleTimeCallRef.current = true;
        initialFetchCalled.current = true;
      }
    }
  }, [actionData]);

  // Collection details handling
  useEffect(() => {
    if (
      actionData?.type === "fetch-collection-details" &&
      actionData?.status === 200
    ) {
      setCollectionDetails(actionData?.data?.collectionDetail);
      const existingProducts =
        actionData?.data?.collectionDetail?.products?.map((items) => items.id);
      const filterdProducts = actionData?.data?.product?.filter(
        (product) => !existingProducts.includes(product.id),
      );
      setFilterProducts(filterdProducts);
      setEndCursor(actionData?.data?.cursor);
      setHasNextPage(actionData?.data?.page);
      setCollectionLoading(false);
    } else if (
      actionData?.type === "fetch-collection-details" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setCollectionLoading(false);
    }
  }, [actionData]);

  // Database collection product handling
  useEffect(() => {
    if (
      actionData?.type === "fetch-db-collection-product" &&
      actionData?.status === 200
    ) {
      const id = Number(actionData?.data?.collectionId);
      const selectedCollection = collectionDataFromDB.find(
        (collection) => collection.id === id,
      );

      if (selectedCollection) {
        const collectionDetailsObj = {
          collectionId: selectedCollection.id,
          title: selectedCollection.collection_name,
          handle: selectedCollection.collection_url.replace("/", ""),
          products: actionData?.data?.products,
          image: actionData?.data?.products[0]?.image || null,
          views: selectedCollection.view_count || 0,
          status: selectedCollection.status,
          sortValue: selectedCollection.sorting_value,
          updatedAt: selectedCollection.updated_at,
        };
        setCollectionDetails(collectionDetailsObj);
        setCollectionLoading(false);
      }
    } else if (
      actionData?.type === "fetch-db-collection-product" &&
      actionData?.status !== 200
    ) {
      setCollectionLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData, collectionDataFromDB]);

  // Add products fetch handling
  useEffect(() => {
    if (actionData?.type === "add-products-fetch") {
      setAddProductsLoading(false);
      if (actionData?.status === 200) {
        const newFetchedProducts = actionData?.data?.products || [];
        setFilterProducts((prevProducts) => {
          const existingIds = new Set(
            prevProducts.map((product) => product.id),
          );
          const uniqueProducts = newFetchedProducts.filter(
            (product) => !existingIds.has(product.id),
          );
          return [...prevProducts, ...uniqueProducts];
        });
        const newHasNextPage = actionData?.data?.hasNextPage === true;
        setEndCursor(actionData?.data?.endCursor);
        setHasNextPage(newHasNextPage);
      } else if (actionData?.status === 400) {
        shopify.toast.show(actionData?.message, { isError: true });
      }
    }
  }, [actionData]);

  // Delete product handling
  useEffect(() => {
    if (actionData?.type === "delete-product" && actionData?.status === 200) {
      const productId = actionData?.data?.productId;
      const collectionId = actionData?.data?.collectionId;
      const pId = actionData?.data?.id;

      setCollectionDataFromDB((prevCollections) => {
        const updatedCollections = prevCollections.map((collection) => {
          if (collection.id === collectionId) {
            return {
              ...collection,
              product_details: collection.product_details
                ? [
                    ...collection.product_details.filter(
                      (product) => product.id !== pId,
                    ),
                  ]
                : [],
            };
          }
          return { ...collection };
        });
        return [...updatedCollections];
      });

      setCollectionDetails((prev) => ({
        ...prev,
        products: prev.products.filter((product) => product.id !== productId),
      }));

      setProductDeleteLoading(false);
      shopify.toast.show(actionData?.message);
    } else if (
      actionData?.type === "delete-product" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setProductDeleteLoading(false);
    }
  }, [actionData]);

  // Add products handling
  useEffect(() => {
    if (actionData?.type === "add-products" && actionData?.status === 200) {
      setCollectionDataFromDB((prevCollections) =>
        prevCollections.map((collection) =>
          collection.id === actionData?.data?.collectionId
            ? {
                ...collection,
                product_details: [
                  ...(Array.isArray(collection.product_details)
                    ? collection.product_details
                    : []),
                  ...actionData?.data?.dbProduct,
                ],
              }
            : collection,
        ),
      );

      setCollectionDetails((prevcollection) => ({
        ...prevcollection,
        products: [
          ...prevcollection.products,
          ...actionData?.data?.allProducts,
        ],
      }));

      const addedProductIds = actionData?.data?.allProducts.map(
        (product) => product.id,
      );
      setFilterProducts((prevProducts) =>
        prevProducts.filter((product) => !addedProductIds.includes(product.id)),
      );

      shopify.toast.show(actionData?.message);
      setAddProductLoading(false);
    } else if (
      actionData?.type === "add-products" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setAddProductLoading(false);
    }
  }, [actionData]);

  // Sort update handling
  useEffect(() => {
    if (actionData?.type === "sort-update" && actionData?.status === 200) {
      shopify.toast.show(actionData?.message);
      setSortUpdateLoading(false);
      setSortValue(actionData?.data);
    } else if (
      actionData?.type === "sort-update" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setSortUpdateLoading(false);
    }
  }, [actionData]);

  // Task ID initialization
  useEffect(() => {
    const storedTaskId = localStorage.getItem("manual-generate-task-id");
    if (storedTaskId !== null) {
      setTaskId(storedTaskId);
    }
  }, []);

  // Task status checking when taskId is set
  useEffect(() => {
    if (taskId !== null) {
      handleCheckTaskStatus();
    }
  }, [taskId]);

  // Fetch collections from the server
  const fetchCollections = useCallback(() => {
    setIsLoading(true);
    const formData = new FormData();
    formData.append("type", "initialFetch");
    formData.append("page", currentPage);
    formData.append("option", selectedStatus);
    formData.append("searchValue", searchValue);

    // If we have a product sync ID, include it
    if (productSyncId) {
      formData.append("id", productSyncId);
    }

    submit(formData, { method: "post" });
  }, [currentPage, selectedStatus, searchValue, productSyncId, submit]);

  // Handle action response
  const handleActionResponse = useCallback(
    (data) => {
      setIsLoading(false);

      if (data.type === "initialFetch") {
        setCollections(data.data || []);
        setTotalCount(data.data.totalCount);
      } else if (data.type === "fetchCollectionDetails") {
        setCollectionLoading(false);
        if (data.status === 200) {
          setCollectionDetails(data.data.details);
          setFilteredProducts(data.data.products);
        } else {
          setToastMessage(data.message || "Failed to load collection details");
          setToastError(true);
          closeModal("collectionDetails");
        }
      } else if (
        data.type === "delete" ||
        data.type === "bulkDelete" ||
        data.type === "publish" ||
        data.type === "unpublish" ||
        data.type === "manualUpdate"
      ) {
        // Show success toast
        setToastMessage(data.message || "Action completed successfully");
        setToastError(data.status !== 200);

        // Reset action loading states
        setActionLoading({
          delete: {},
          publish: {},
          unpublish: {},
          update: false,
          bulkDelete: false,
          export: false,
        });

        // Close any open modals
        setModals({
          delete: false,
          bulkDelete: false,
          unpublish: false,
          individualEdit: false,
          collectionDetails: false,
          schedule: false,
          import: false,
          limitReached: false,
          upgradeModal: false,
        });

        // Refresh collections
        fetchCollections();

        // Clear selected collections after bulk delete
        if (data.type === "bulkDelete") {
          setSelectedCollections([]);
        }
      } else if (data.type === "file-import") {
        setToastMessage(data.message || "Import completed");
        setToastError(data.status !== 200);
        setFile(null);
        closeModal("import");
        fetchCollections();
      } else if (data.type === "exportCollections") {
        setActionLoading((prev) => ({ ...prev, export: false }));
        if (data.status === 200 && data.data) {
          window.location.href = data.data;
        } else {
          setToastMessage(data.message || "Export failed");
          setToastError(true);
        }
      }
    },
    [fetchCollections],
  );

  // Handle collection selection
  const handleSelectCollection = useCallback((id) => {
    setSelectedCollections((prev) => {
      if (prev.includes(id)) {
        return prev.filter((collectionId) => collectionId !== id);
      } else {
        return [...prev, id];
      }
    });
  }, []);

  // Handle collection details view
  const handleCollectionDetailsOpen = useCallback(
    async (index) => {
      setSelectedCollectionIndex(index);
      setCollectionLoading(true);
      openModal("collectionDetails");

      // Submit to action to fetch collection details
      const formData = new FormData();
      formData.append("type", "fetch-collection-details");
      formData.append("id", collectionDataFromDB[index].id);
      submit(formData, { method: "post" });
    },
    [collectionDataFromDB, submit],
  );

  // Handle select all collections
  const handleSelectAllCollections = useCallback(() => {
    if (selectedCollections.length === collectionDataFromDB.length) {
      setSelectedCollections([]);
    } else {
      setSelectedCollections(collectionDataFromDB.map((c) => c.id));
    }
  }, [collectionDataFromDB, selectedCollections.length]);

  // Search and filter handlers
  const handleSearchChange = useCallback((value) => {
    setSearchValue(value);
  }, []);

  const handleStatusChange = useCallback((value) => {
    setSelectedStatus(value);
  }, []);

  const handleTabChange = useCallback(
    (tabIndex) => {
      setSelectedTab(tabIndex);
      setSpinnerLoading(true);

      // Map tab index to status
      const statusMap = {
        0: "all",
        1: "published",
        2: "unpublished",
        3: "scheduled",
        4: "generated",
      };

      const newStatus = statusMap[tabIndex] || "all";
      setSelectedStatus(newStatus);
      setSelectedOptions(newStatus);

      // Fetch data for the selected tab
      const formData = new FormData();
      formData.append("type", "initialFetch");
      formData.append("page", 1); // Reset to page 1 when changing tabs
      formData.append("option", newStatus);
      formData.append("searchValue", searchValue);
      if (productSyncId) {
        formData.append("id", productSyncId);
      }
      submit(formData, { method: "post" });
      setCurrentPage(1); // Reset current page
    },
    [searchValue, productSyncId, submit],
  );

  // Filter options
  const filters = [
    {
      key: "status",
      label: "Status",
      filter: (
        <Select
          label="Status"
          labelHidden
          options={[
            { label: "All", value: "all" },
            { label: "Published", value: "published" },
            { label: "Unpublished", value: "unpublished" },
            { label: "Scheduled", value: "scheduled" },
            { label: "Generated", value: "generated" },
          ]}
          value={selectedStatus}
          onChange={handleStatusChange}
        />
      ),
    },
  ];

  // Tab options
  const tabs = [
    {
      id: "all",
      content: "All",
    },
    {
      id: "published",
      content: "Published",
      badge: Array.isArray(collectionDataFromDB)
        ? collectionDataFromDB.filter((c) => c.status === "published").length ||
          null
        : null,
    },
    {
      id: "unpublished",
      content: "Unpublished",
      badge: Array.isArray(collectionDataFromDB)
        ? collectionDataFromDB.filter((c) => c.status === "unpublished")
            .length || null
        : null,
    },
    {
      id: "scheduled",
      content: "Scheduled",
      badge: Array.isArray(collectionDataFromDB)
        ? collectionDataFromDB.filter((c) => c.status === "scheduled").length ||
          null
        : null,
    },
    {
      id: "generated",
      content: "Generated",
      badge: Array.isArray(collectionDataFromDB)
        ? collectionDataFromDB.filter((c) => c.status === "generated").length ||
          null
        : null,
    },
  ];

  // Action menu items
  const actionMenuItems = [
    {
      content: "Import collections",
      icon: ImportIcon,
      onAction: () => handleImportModalOpen(),
    },
    {
      content: "Export collections",
      icon: ExportIcon,
      onAction: handleExportCollections,
    },
    {
      content: "Schedule settings",
      icon: SettingsIcon,
      onAction: () => {
        if (configs?.data?.automaticUpdate) {
          handleScheduleModalOpen();
        } else {
          handleUpgradeModalOpen();
        }
      },
    },
  ];

  // Render collection status badge
  const renderStatusBadge = (status) => {
    const toneMap = {
      published: "success",
      unpublished: "critical",
      scheduled: "info",
      generated: "attention",
    };

    return (
      <Badge tone={toneMap[status] || "new"}>{createCapitalizer(status)}</Badge>
    );
  };

  // Empty state component
  const emptyStateMarkup = (
    <EmptyState
      heading="Create your first collection"
      image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
      action={{
        content: "Generate collections",
        onAction: () => navigate("/app/generate-collections"),
      }}
    >
      <p>
        Generate smart collections based on your products or import existing
        collections.
      </p>
    </EmptyState>
  );

  // Loading state component
  const loadingStateMarkup = (
    <Card>
      <Box padding="400">
        <BlockStack gap="400">
          <SkeletonDisplayText size="small" />
          <SkeletonBodyText lines={6} />
        </BlockStack>
      </Box>
    </Card>
  );

  // Missing handlers from old codebase
  const handleDeleteSingleProduct = (product) => {
    const productId = product?.id;
    const collectionId = product?.collectionId;

    setCollectionIndex(product.index);
    setProductDeleteLoading((prev) => ({
      ...prev,
      [product.index]: true,
    }));

    const formData = new FormData();
    formData.append("type", "delete-product");
    formData.append("collectionId", collectionId);
    formData.append("productId", productId);

    submit(formData, { method: "post" });
    trackButtonClick(
      "Delete Product from the Collection",
      "Collection Drafts",
      {
        shop_name: shopName,
      },
    );
  };

  const handleAddProducts = (productId) => {
    setAddProductLoading(true);
    const productIds = productId.productIds;
    const formData = new FormData();

    formData.append("type", "add-products");
    formData.append("productId", JSON.stringify(productIds));
    formData.append("collectionId", productId.collectionId);

    submit(formData, { method: "post" });
    trackButtonClick("Add Product to the Collection", "Collection Drafts", {
      shop_name: shopName,
    });
  };

  const handleFetchProductForAddProducts = () => {
    if (!hasNextPage) {
      setAddProductsLoading(false);
      return;
    }

    setAddProductsLoading(true);
    const formData = new FormData();
    formData.append("type", "add-products-fetch");
    formData.append("cursor", endCursor);
    formData.append("nextPage", true);
    submit(formData, { method: "post" });
  };

  const handleSortUpdate = (sort) => {
    setSortUpdateLoading(true);
  
    const formData = new FormData();
    formData.append("type", "sort-update");
    formData.append("id", sort.id);
    formData.append("option", sort.sort);
  
    if (sort.sort === "MANUAL" && sort.repositionedProducts) {
      formData.append("products", JSON.stringify(sort.repositionedProducts));
    }
  
    submit(formData, { method: "post" });
  
    trackButtonClick("Sorting the Collection", "Collection Drafts", {
      shop_name: shopName,
    });
  };
  

  const handleScheduleUpdate = (update) => {
    handleScheduleModalClose();
    handleAutoGenerateModalOpen("autoSchedule");
  };

  // File upload for import
  const handleDropZoneDrop = useCallback(
    (_dropFiles, acceptedFiles, _rejectedFiles) => {
      setFile(acceptedFiles[0]);
    },
    [],
  );

  // File upload components from old code
  const validFileType = "text/csv";
  const fileUpload = !file && <DropZone.FileUpload actionTitle="Upload File" />;
  const uploadedFile = file && (
    <Card sectioned>
      <div style={{ padding: "0" }}>
        <InlineStack direction="column" align="center" blockAlign="center">
          <Card>
            <Icon source={FileIcon} />
          </Card>
          <div>
            {file.name}{" "}
            <Text variant="bodySm" as="p">
              {file.size} bytes
            </Text>
          </div>
        </InlineStack>
      </div>
    </Card>
  );

  // Main component layout - Modern UI Structure
  return (
    <Frame>
      {toastMessage && (
        <Toast
          content={toastMessage}
          error={toastError}
          onDismiss={() => setToastMessage(null)}
          duration={4500}
        />
      )}

      <Page
        title="Collections"
        primaryAction={{
          content: "Generate collections",
          icon: PlusIcon,
          onAction: () => {
            if (collectionLimit <= 0) {
              handleLimitModalOpen();
            } else if (productSyncId !== null) {
              handleProductSyncModalOpen();
            } else {
              navigate("/app/generate-collections");
            }
          },
          disabled: spinnerLoading || taskId,
        }}
        secondaryActions={[
          {
            content: "Actions",
            icon: FilterIcon,
            onAction: () => setActionMenuOpen(!actionMenuOpen),
          },
        ]}
      >
        <Popover
          active={actionMenuOpen}
          activator={<div />}
          onClose={() => setActionMenuOpen(false)}
          preferredAlignment="right"
        >
          <ActionList
            actionRole="menuitem"
            items={[
              {
                content: "Import collections",
                icon: ImportIcon,
                onAction: () => handleImportModalOpen(),
              },
              {
                content: "Export collections",
                icon: ExportIcon,
                onAction: handleExportCollections,
              },
              {
                content: "Schedule settings",
                icon: SettingsIcon,
                onAction: () => {
                  if (configs?.data?.automaticUpdate) {
                    handleScheduleModalOpen();
                  } else {
                    handleUpgradeModalOpen();
                  }
                },
              },
            ]}
          />
        </Popover>

        {/* Notification banner for collection limit */}
        {collectionLimit === 0 && (
          <Layout.Section>
            <Banner
              title="Collection limit reached"
              tone="warning"
              action={{
                content: "Upgrade plan",
                onAction: () => handleUpgradeModalOpen(),
              }}
            >
              <p>
                You've reached your collection limit. Upgrade your plan to
                create more collections.
              </p>
            </Banner>
          </Layout.Section>
        )}

        <Layout>
          <Layout.Section>
            <Card padding="0">
              <Tabs
                tabs={[
                  {
                    id: "all",
                    content: "All",
                  },
                  {
                    id: "published",
                    content: "Published",
                    badge: Array.isArray(collectionDataFromDB)
                      ? collectionDataFromDB.filter(
                          (c) => c.status === "published",
                        ).length || null
                      : null,
                  },
                  {
                    id: "unpublished",
                    content: "Unpublished",
                    badge: Array.isArray(collectionDataFromDB)
                      ? collectionDataFromDB.filter(
                          (c) => c.status === "unpublished",
                        ).length || null
                      : null,
                  },
                  {
                    id: "scheduled",
                    content: "Scheduled",
                    badge: Array.isArray(collectionDataFromDB)
                      ? collectionDataFromDB.filter(
                          (c) => c.status === "scheduled",
                        ).length || null
                      : null,
                  },
                  {
                    id: "generated",
                    content: "Generated",
                    badge: Array.isArray(collectionDataFromDB)
                      ? collectionDataFromDB.filter(
                          (c) => c.status === "generated",
                        ).length || null
                      : null,
                  },
                ]}
                selected={selectedTab}
                onSelect={handleTabChange}
                fitted
              />
              <Divider />

              <Box padding="400">
                <InlineStack
                  align="space-between"
                  blockAlign="center"
                  wrap={false}
                  gap="400"
                >
                  <div style={{ flexGrow: 1 }}>
                    <TextField
                      placeholder="Search collections"
                      value={searchValue}
                      onChange={handleSearchValueChange}
                      onKeyDown={handleSearchKeyDown}
                      prefix={<Icon source={SearchIcon} />}
                      clearButton
                      onClearButtonClick={handleSearchClear}
                    />
                  </div>

                  <InlineStack gap="300">
                    <ButtonGroup>
                      <Button
                        icon={FilterIcon}
                        onClick={() => setIsFilterVisible(!isFilterVisible)}
                        pressed={isFilterVisible}
                      >
                        Filter
                      </Button>

                      <Button
                        icon={ImportIcon}
                        onClick={() => handleImportModalOpen()}
                      >
                        Import
                      </Button>

                      <Button
                        icon={ExportIcon}
                        onClick={handleExportCollections}
                        loading={exportLoading}
                      >
                        Export
                      </Button>
                    </ButtonGroup>

                    {selectedCollections.length > 0 && (
                      <Button
                        tone="critical"
                        onClick={() => handleBulkDeleteModalOpen()}
                        loading={bulkDeleteLoading}
                      >
                        Delete selected ({selectedCollections.length})
                      </Button>
                    )}
                  </InlineStack>
                </InlineStack>

                {isFilterVisible && (
                  <Box paddingBlockStart="400">
                    <LegacyFilters
                      queryValue={searchValue}
                      filters={[
                        {
                          key: "status",
                          label: "Status",
                          filter: (
                            <Select
                              label="Status"
                              labelHidden
                              options={statusOptions}
                              value={selectedOptions}
                              onChange={handleStatusOptionsChange}
                            />
                          ),
                        },
                      ]}
                      appliedFilters={appliedFilters}
                      onQueryChange={handleSearchValueChange}
                      onQueryClear={handleSearchClear}
                      onClearAll={() => {
                        setSearchValue("");
                        setAppliedFilters([]);
                        setSelectedOptions("all");
                        setSelectedTab(0);

                        // Fetch fresh data
                        setSpinnerLoading(true);
                        const formData = new FormData();
                        formData.append("type", "initialFetch");
                        formData.append("page", 1);
                        formData.append("option", "all");
                        formData.append("searchValue", "");
                        if (productSyncId) {
                          formData.append("id", productSyncId);
                        }
                        submit(formData, { method: "post" });
                        setCurrentPage(1);
                      }}
                    />
                  </Box>
                )}
              </Box>

              <Divider />

              {spinnerLoading ? (
                <Box padding="400">
                  <BlockStack gap="400">
                    <SkeletonDisplayText size="small" />
                    <SkeletonBodyText lines={5} />
                  </BlockStack>
                </Box>
              ) : !Array.isArray(collectionDataFromDB) ||
                collectionDataFromDB?.length === 0 ? (
                <EmptyState
                  heading="Create your first collection"
                  image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
                  action={{
                    content: "Generate collections",
                    onAction: () => navigate("/app/generate-collections"),
                  }}
                >
                  <p>
                    Generate smart collections based on your products or import
                    existing collections.
                  </p>
                </EmptyState>
              ) : (
                <>
                  <IndexTable
                    resourceName={{
                      singular: "collection",
                      plural: "collections",
                    }}
                    itemCount={collectionDataFromDB?.length}
                    selectable={false}
                    headings={[
                      {
                        title: (
                          <Checkbox
                            checked={
                              selectedCollections.length ===
                                collectionDataFromDB?.length &&
                              collectionDataFromDB?.length > 0
                            }
                            indeterminate={
                              selectedCollections.length > 0 &&
                              selectedCollections.length <
                                collectionDataFromDB?.length
                            }
                            onChange={handleSelectAllCollections}
                          />
                        ),
                      }, // Select all checkbox column
                      { title: "Name" },
                      { title: "Status" },
                      { title: "Products" },
                      { title: "Created" },
                      { title: "Actions" },
                    ]}
                  >
                    {Array.isArray(collectionDataFromDB) &&
                      collectionDataFromDB?.map((collection, index) => (
                        <IndexTable.Row
                          id={collection?.id}
                          key={collection?.id}
                          position={index}
                        >
                          <IndexTable.Cell>
                            <Checkbox
                              checked={selectedCollections.includes(
                                collection?.id,
                              )}
                              onChange={() =>
                                handleBulkSelection(collection?.id)
                              }
                            />
                          </IndexTable.Cell>

                          <IndexTable.Cell>
                            <InlineStack gap="300" blockAlign="center">
                              <Text
                                variant="bodyMd"
                                fontWeight="semibold"
                                as="span"
                              >
                                {collection?.collection_name}
                              </Text>
                              {collection.collection_url &&
                                collection.status === "published" && (
                                  <Tooltip content="View in store">
                                    <Button
                                      plain
                                      icon={EyeCheckMarkIcon}
                                      accessibilityLabel="View in store"
                                      onClick={() =>
                                        window.open(
                                          `https://${storeDomain}/collections${collection?.collection_url}`,
                                          "_blank",
                                        )
                                      }
                                    />
                                  </Tooltip>
                                )}
                            </InlineStack>
                          </IndexTable.Cell>

                          <IndexTable.Cell>
                            <Badge
                              tone={
                                collection.status === "published"
                                  ? "success"
                                  : collection.status === "unpublished"
                                    ? "critical"
                                    : collection.status === "scheduled"
                                      ? "info"
                                      : collection.status === "generated"
                                        ? "attention"
                                        : null
                              }
                            >
                              {createCapitalizer(collection.status)}
                            </Badge>
                          </IndexTable.Cell>

                          <IndexTable.Cell>
                            <Button
                              plain
                              onClick={() =>
                                handleCollectionDetailsModalOpen(
                                  index,
                                  collection.status !== "published"
                                    ? "dbSide"
                                    : "shopifySide",
                                )
                              }
                            >
                              {collection?.product_details?.length || 0}{" "}
                              products
                            </Button>
                          </IndexTable.Cell>

                          <IndexTable.Cell>
                            {formatDate(collection.created_at)}
                          </IndexTable.Cell>

                          <IndexTable.Cell>
                            <InlineStack gap="200">
                              {collection?.status !== "published" ? (
                                <Tooltip content="Publish">
                                  <Button
                                    icon={EyeCheckMarkIcon}
                                    onClick={() => handlePublish(index)}
                                    loading={publishLoading[index]}
                                    accessibilityLabel="Publish"
                                    size="slim"
                                  />
                                </Tooltip>
                              ) : (
                                <Tooltip content="Unpublish">
                                  <Button
                                    icon={HideIcon}
                                    onClick={() =>
                                      handleUnpublishModalOpen(index)
                                    }
                                    loading={unpublishLoading[index]}
                                    accessibilityLabel="Unpublish"
                                    size="slim"
                                  />
                                </Tooltip>
                              )}

                              <Tooltip content="Edit">
                                <Button
                                  icon={EditIcon}
                                  onClick={() =>
                                    handleIndividualEditModalOpen(index)
                                  }
                                  accessibilityLabel="Edit"
                                  size="slim"
                                />
                              </Tooltip>

                              <Tooltip content="Delete">
                                <Button
                                  icon={DeleteIcon}
                                  onClick={() => handleDeleteModalOpen(index)}
                                  loading={deleteLoading[index]}
                                  accessibilityLabel="Delete"
                                  size="slim"
                                />
                              </Tooltip>
                            </InlineStack>
                          </IndexTable.Cell>
                        </IndexTable.Row>
                      ))}
                  </IndexTable>

                  {totalCount > 10 && (
                    <Box padding="400">
                      <InlineStack align="center" blockAlign="center" gap="500">
                        <Pagination
                          onPrevious={() => {
                            const newPage = Math.max(currentPage - 1, 1);
                            setCurrentPage(newPage);
                            setSpinnerLoading(true);

                            const formData = new FormData();
                            formData.append("type", "initialFetch");
                            formData.append("page", newPage);
                            formData.append("option", selectedOptions);
                            formData.append("searchValue", searchValue);
                            if (productSyncId) {
                              formData.append("id", productSyncId);
                            }
                            submit(formData, { method: "post" });
                          }}
                          onNext={() => {
                            const newPage = currentPage + 1;
                            setCurrentPage(newPage);
                            setSpinnerLoading(true);

                            const formData = new FormData();
                            formData.append("type", "initialFetch");
                            formData.append("page", newPage);
                            formData.append("option", selectedOptions);
                            formData.append("searchValue", searchValue);
                            if (productSyncId) {
                              formData.append("id", productSyncId);
                            }
                            submit(formData, { method: "post" });
                          }}
                          type="table"
                          hasPrevious={currentPage > 1}
                          hasNext={currentPage * 10 < totalCount}
                          label={`${content.Rank_collections.collection_gallery.content_2} ${currentPage * 10 - 9}-${Math.min(currentPage * 10, totalCount)} of ${totalCount} `}
                        />
                      </InlineStack>
                    </Box>
                  )}
                </>
              )}
            </Card>
          </Layout.Section>
        </Layout>
      </Page>

      {/* Modals from old codebase with TitleBar structure */}

      {/* Schedule Modal */}
      {scheduleOpen && (
        <ScheduleCollections
          open={scheduleOpen}
          hide={handleScheduleModalClose}
          time={schedules.data}
          scheduleUpdate={handleScheduleModal}
        />
      )}

      {/* Auto Generate Modal */}
      {autoGenerateModalOpen && (
        <AutoScheduleCollectionsModal
          open={autoGenerateModalOpen}
          hide={handleAutoGenerateModalClose}
          from={from}
          configs={configs.data}
          onUpdate={handleCronUpdate}
          loading={cronLoading}
          syncLoading={competitorSyncLoading}
          syncUrl={handleSycnCompetitorUrl}
          isUrlSynced={isUrlSynced}
          manualCreatePayload={handleManualGenerate}
          manualLoading={manualGenerateLoading}
          scheduleTime={schedule}
        />
      )}

      {/* Import Modal */}
      <Modal open={importModalOpen} onHide={handleImportModalClose}>
        <TitleBar
          title={content.Rank_collections.collection_gallery.modal_heading_1}
        />
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyLg" as="p" fontWeight="semibold">
            {content.Rank_collections.collection_gallery.modal_content_1}
          </Text>
          <br />
          <BlockStack>
            <InlineStack
              direction="column"
              align="center"
              blockAlign="center"
              gap="400"
            >
              <Button onClick={downloadExampleFile}>
                {content.Rank_collections.collection_gallery.modal_button_1}
              </Button>
              <Button onClick={() => setDropZoneOpen(true)}>
                {content.Rank_collections.collection_gallery.modal_button_2}
              </Button>
            </InlineStack>
          </BlockStack>
          <br />
          {isDropZoneOpen && (
            <>
              <DropZone
                onDrop={handleDropZoneDrop}
                accept=".csv"
                style={{ marginTop: "20px" }}
                actionTitle={
                  content.Rank_collections.collection_gallery.modal_button_3
                }
              >
                {uploadedFile}
                {fileUpload}
              </DropZone>
              <br />
              <InlineStack align="center" blockAlign="center" gap="500">
                <Button
                  loading={importLoading}
                  onClick={handleImportCollection}
                >
                  {content.Rank_collections.collection_gallery.modal_button_4}
                </Button>
              </InlineStack>
            </>
          )}
        </Box>
      </Modal>

      {/* Bulk Delete Modal */}
      <Modal open={bulkDeleteModalOpen} onHide={handleBulkDeleteModalClose}>
        <TitleBar
          title={content.Rank_collections.collection_gallery.heading_9}
        />
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyLg" as="p" fontWeight="bold">
            {content.Rank_collections.collection_gallery.content_3}
          </Text>
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            padding: "10px",
          }}
        >
          <InlineStack gap="300" align="end" blockAlign="center">
            <Button onClick={handleBulkDeleteModalClose}>
              {content.Rank_collections.collection_gallery.button_9}
            </Button>
            <Button
              icon={DeleteIcon}
              variant="primary"
              tone="critical"
              onClick={handleBulkDelete}
            >
              {content.Rank_collections.collection_gallery.button_10}
            </Button>
          </InlineStack>
        </Box>
      </Modal>

      {/* Individual Edit Modal */}
      {individualEditModalOpen && (
        <IndividualEditCollection
          open={individualEditModalOpen}
          hide={handleIndividualEditModalClose}
          name={collectionDataFromDB[selectedIndex]?.collection_name}
          url={collectionDataFromDB[selectedIndex]?.collection_url}
          description={collectionDataFromDB[selectedIndex]?.description}
          domain={storeDomain}
          loading={collectionUpdateLoading}
          collectionFaq={collectionDataFromDB[selectedIndex]?.metafields}
          status={collectionDataFromDB[selectedIndex]?.status}
          onUpdate={handleUpdateCollection}
        />
      )}

      {/* Delete Modal */}
      <Modal open={deleteModalOpen} onHide={handleDeleteModalClose}>
        <TitleBar
          title={content.Rank_collections.collection_gallery.heading_10}
        />
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyMd" as="span">
            {content.Rank_collections.collection_gallery.content_4}{" "}
            <Text variant="bodyMd" as="span" fontWeight="bold">
              {collectionDataFromDB[selectedIndex]?.collection_name}?
            </Text>
          </Text>
        </Box>
        <Box
          style={{
            padding: "10px",
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            display: "flex",
            justifyContent: "end",
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Button onClick={handleDeleteModalClose}>
            {content.Rank_collections.collection_gallery.button_11}
          </Button>
          <Button
            onClick={() => handleDelete(selectedIndex)}
            variant="primary"
            tone="critical"
            icon={DeleteIcon}
          >
            {content.Rank_collections.collection_gallery.button_12}
          </Button>
        </Box>
      </Modal>

      {/* Unpublish Modal */}
      <Modal open={unpublishModalOpen} onHide={handleUnpublishModalClose}>
        <TitleBar
          title={content.Rank_collections.collection_gallery.modal_heading_6}
        />
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyMd" as="span">
            {content.Rank_collections.collection_gallery.content_5}{" "}
            <Text variant="bodyMd" as="span" fontWeight="bold">
              {collectionDataFromDB[selectedIndex]?.collection_name}?
            </Text>
          </Text>
        </Box>
        <Box
          style={{
            padding: "10px",
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            display: "flex",
            justifyContent: "end",
            alignItems: "center",
            gap: "10px",
          }}
        >
          <Button onClick={handleUnpublishModalClose}>
            {content.Rank_collections.collection_gallery.button_13}
          </Button>
          <Button
            onClick={() => handleUnpublish(selectedIndex)}
            variant="primary"
            tone="critical"
          >
            {content.Rank_collections.collection_gallery.modal_button_6}
          </Button>
        </Box>
      </Modal>

      {/* Collection Details Modal */}
      {collectionDetailsModalOpen && (
        <ProductsViewModalWithSEO
          open={collectionDetailsModalOpen}
          hide={handleCollectionDetailsModalClose}
          collection={collectionDetails}
          loading={collectionLoading}
          products={filteredProducts}
          scheduleUpdate={handleScheduleModalOpen}
          deleteProduct={handleDeleteSingleProduct}
          deleteLoading={productDeleteLoading[collectionIndex] || false}
          addProducts={handleAddProducts}
          addProductLoading={addProductLoading}
          addProductHide={hideAddProduct}
          automatic={configs?.data?.automaticUpdate}
          navigate={navigate}
          sortUpdate={handleSortUpdate}
          sortLoading={sortUpdateLoading}
          sortValue={sortValue}
          productLoading={addProductsLoading}
          addProductFetch={handleFetchProductForAddProducts}
        />
      )}

      {/* Upgrade Modal */}
      <Modal open={upgradeModalOpen} onHide={handleUpgradeModalClose}>
        <TitleBar title={content.Rank_collections.home.modal_heading_4}>
          <button onClick={() => navigate("/app/plans")} variant="primary">
            {content.Rank_collections.home.modal_button_1}
          </button>
        </TitleBar>
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyMd" as="p">
            {content.Rank_collections.home.modal_content_3}
          </Text>
        </Box>
      </Modal>

      {/* Info Modal */}
      <Modal open={infoModalOpen} onHide={handleInfoModalClose}>
        <TitleBar title={content.Rank_collections.home.modal_heading_5} />
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyLg" as="p" fontWeight="semibold">
            {content.Rank_collections.home.modal_content_5}
          </Text>
        </Box>
      </Modal>

      {/* Limit Reached Modal */}
      <Modal open={limitReachedModal} onHide={handleLimitModalClose}>
        <TitleBar />
        <Box style={{ padding: "10px" }}>
          <Text
            variant="headingMd"
            as="h6"
            fontWeight="semibold"
            alignment="center"
          >
            {content.Rank_collections.home.modal_content_6}
          </Text>
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            padding: "10px",
          }}
        />
      </Modal>

      {/* Product Sync Modal */}
      <Modal open={productSyncModalOpen} onHide={handleProductSyncModalClose}>
        <TitleBar
          title={content.Rank_collections.collection_gallery.modal_heading_7}
        />
        <Box style={{ padding: "10px" }}>
          <Text
            variant="headingMd"
            as="h6"
            fontWeight="semibold"
            alignment="center"
          >
            {content.Rank_collections.collection_gallery.modal_content_2}
          </Text>
        </Box>
      </Modal>
    </Frame>
  );
}
