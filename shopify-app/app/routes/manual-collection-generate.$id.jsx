import { json } from '@remix-run/node';
import { saveFetchedCollection } from '../models/saveFetchedCollectionFromAgent.server';
import { supabase } from '../db/supabase_insert_helper';

export const action = async ({ request }) => {
  try {
    const storeUUID = request.url.match(/\/manual-collection-generate\/([^/]+)/)[1]
    const requestBody = await request.json(); // Parse the JSON body of the request

    const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('uuid', storeUUID);
    if(storeDataError){
        loggerError('Store Data not found or somthing went wrong', storeUUID, storeDataError.message);
        return {
            status: 400,
            message: "Store Data Not found",
            data: null
        }
    }
    const {data: collectionJobData, error: collectionJobDataError} = await supabase.from('collection_jobs').update({task_status: requestBody.status}).eq('task_id', requestBody.task_id).eq('store_id', storeData[0].id) 
    if(collectionJobDataError){
        loggerError('Something went wrong to update the collection job', storeUUID, collectionJobDataError.message);
        return {
            status: 400,
            message: "Something went wrong",
            data: null
        }
    }

    await saveFetchedCollection(storeUUID, requestBody)
    return json({
      status: 200,
      message: "success",
      data: requestBody, // Optionally return the parsed data
    });
  } catch (error) {
    console.error("Error parsing request body: ", error);

    return json({
      status: 400,
      message: "Invalid request body",
      data: null,
    });
  }
};
