import { json } from "@remix-run/node";
import { useEffect } from 'react'
import {
  Link,
  Outlet,
  useLoaderData,
  useRouteError,
} from "@remix-run/react";
import { boundary } from "@shopify/shopify-app-remix/server";
import { AppProvider } from "@shopify/shopify-app-remix/react";
import { NavMenu } from "@shopify/app-bridge-react";
import polarisStyles from "@shopify/polaris/build/esm/styles.css?url";
import { authenticate } from "../shopify.server";
import { checkPlanDetails } from "../models/checkPlanDetails.server";
import { initializeIntercom } from "../helper/intercom";
import { shopDetails } from "../helper/shopDetails";

import content from '../locales/en.json'

export const links = () => [{ rel: "stylesheet", href: polarisStyles }];

export const loader = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const planDetails = await checkPlanDetails(admin);
  const shop = await shopDetails(admin)
  const isPlanValid = planDetails?.data?.isFreePlan || planDetails?.data?.paymentStatus === "ACTIVE";

  return json({
    apiKey: process.env.SHOPIFY_API_KEY || "",
    status: isPlanValid,
    shop: shop,
    automaticUpdate: planDetails?.data?.automaticUpdate || false
  });
};

export default function App() {
  const { apiKey, status, shop, automaticUpdate } = useLoaderData();
  // const name = shop.data.shop.name;
  const email = shop.data.shop.email;
  const regexEmail = shop.data.shop.email.replace(/@.*/, '');

  useEffect(() => {
    initializeIntercom({
      name: regexEmail,
      email: email,
    });

    // Update localStorage with the automaticUpdate value
    const storedData = localStorage.getItem('initial-data');
    if (storedData) {
      const parsedData = JSON.parse(storedData);
      parsedData.data.automaticUpdate = automaticUpdate; // Update automaticUpdate
      localStorage.setItem('initial-data', JSON.stringify(parsedData));
    }
  }, [email, automaticUpdate]);

  return (
    <AppProvider isEmbeddedApp apiKey={apiKey}>
      <NavMenu>
        <Link to="/app" rel="home">
          Home
        </Link>
        {status && <><Link to='/app/home'>{content.Rank_collections.navbar_contents.nav_1}</Link>
          <Link to="/app/collection_drafts">{content.Rank_collections.navbar_contents.nav_2}</Link>
          <Link to="/app/dashboard">{content.Rank_collections.navbar_contents.nav_3}</Link>
          <Link to="/app/collection-insights">Advanced Insights</Link>
          <Link to="/app/settings">{content.Rank_collections.navbar_contents.nav_4}</Link>
          <Link to='/app/plans'>{content.Rank_collections.navbar_contents.nav_5}</Link></>}
      </NavMenu>
      <Outlet />
    </AppProvider>
  );
}

// Shopify needs Remix to catch some thrown responses, so that their headers are included in the response.
export function ErrorBoundary() {
  return boundary.error(useRouteError());
}

export const headers = (headersArgs) => {
  return boundary.headers(headersArgs);
};
