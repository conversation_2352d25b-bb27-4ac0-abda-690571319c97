import { useState, useEffect, useCallback } from "react";
import { json } from "@remix-run/node";
import { useNavigate, useSubmit, useActionData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  InlineStack,
  Button,
  Icon,
  Box,
  TextField,
  Checkbox,
  Banner,
  ProgressBar,
  Divider,
} from "@shopify/polaris";
import { WandIcon, ArrowLeftIcon } from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import content from "../locales/en.json";
import { manualGenerate } from "../models/manualGenerate.server";
import { syncCompetitorURL } from "../models/syncCompetitorURLToAgent.server";
import { trackButtonClick } from "../helper/analytics";


export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  return json({
    shopName: session.shop,
  });
};

export const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const type = formData.get("type");

  try {
    if (type === "sync-url") {
      const url = formData.get("url");      
      
      const actionData = await syncCompetitorURL(admin, url);
      return json({
        status: actionData?.status,
        message: actionData?.message,
        data: actionData?.data,
        type,
      });
    } else if (type === "manual-generate") {
      const competitorUrl = formData.get("competitorUrl");
      const attributes = formData.get("attributes");
      const actionData = await manualGenerate(admin, competitorUrl, attributes);
      return json({
        status: actionData.status,
        message: actionData.message,
        data: actionData.data,
        type,
      });
    }

    return json({
      status: 400,
      message: "Invalid action type",
      type,
    });
  } catch (error) {
    console.error(`Error handling action type "${type}":`, error);
    return json({
      status: 500,
      message: error.message || "An unexpected error occurred",
      data: null,
      type,
    });
  }
};

export default function GenerateCollections() {
  const navigate = useNavigate();
  const submit = useSubmit();

  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [competitorUrl, setCompetitorUrl] = useState('');
  const [enteredCompetitorUrl, setEnteredCompetitorUrl] = useState([]);
  const [isUrlSynced, setIsUrlSynced] = useState(false);
  const [useCompetitor, setUseCompetitor] = useState(false);
  const actionData = useActionData();

  const formatURL = (url) => {
    let formattedURL =
      url.startsWith("http://") || url.startsWith("https://")
        ? url
        : `https://${url}`;
    // Remove trailing '/' if present
    return formattedURL.endsWith("/")
      ? formattedURL.slice(0, -1)
      : formattedURL;
  };

  const handleCompetitorURLChange = useCallback(
    (value) => {setCompetitorUrl(value); setEnteredCompetitorUrl([formatURL(value)])},
    [],
  );

  // Collection attributes
  const [productAttributes, setProductAttributes] = useState({
    price: false,
    category: true,
    brand: true,
    color: false,
    size: false,
    material: false,
  });

  const [seasonalAttributes, setSeasonalAttributes] = useState({
    summer: false,
    winter: false,
    spring: false,
    fall: false,
    holiday: false,
  });

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1);
    } else {
      navigate("/app/home");
    }
  };

  const handleNext = () => {
    setStep(step + 1);
  };

  const handleSyncUrl = () => {
    if (!competitorUrl) return;

    setIsLoading(true);
    const formData = new FormData();
    formData.append("type", "sync-url");
    formData.append("url", JSON.stringify(enteredCompetitorUrl));

    submit(formData, {
      method: "post",

    });
  };

  const handleGenerate = () => {
    setIsLoading(true);

    const formData = new FormData();
    formData.append("type", "manual-generate");
    formData.append("competitorUrl", useCompetitor ? "competitor" : "self");

    const attributes = {
      product: productAttributes,
      seasonal: seasonalAttributes,
    };

    formData.append("attributes", JSON.stringify(attributes));

    submit(formData, {
      method: "post",
      replace: true,
      onSuccess: () => {
        navigate("/app/home", {
          state: { generationStarted: true },
        });
      },
      onError: () => {
        shopify.toast.show("Something Went wrong", { isError: true });
        navigate("/app/home"),
          {
            state: { generationStarted: false },
          };
      },
    });

    trackButtonClick("Generate Collections", "Generate Collections Page");
  };

  const handleProductAttributeChange = (key) => {
    setProductAttributes({
      ...productAttributes,
      [key]: !productAttributes[key],
    });
  };

  const handleSeasonalAttributeChange = (key) => {
    setSeasonalAttributes({
      ...seasonalAttributes,
      [key]: !seasonalAttributes[key],
    });
  };

  useEffect (() => {
    if(actionData?.type === "sync-url" && actionData?.status === 200) {
      setIsUrlSynced(true);
      setIsLoading(false);
      shopify.toast.show(actionData.message);
    }else if(actionData?.type === "sync-url" && actionData?.status !== 200) {
      setIsLoading(false);
      shopify.toast.show(actionData.message, { isError: true });
    }
  }, [actionData])

  useEffect(() => {
    if (actionData?.status === 200 && actionData?.type === "manual-generate") {
      setIsLoading(false);
      shopify.toast.show(actionData.message);
      localStorage.setItem("manual-generate-task-id", actionData.data.task_id);
      navigate("/app/home");
    } else if (
      actionData?.status !== 200 &&
      actionData?.type === "manual-generate"
    ) {
      setIsLoading(false);
      shopify.toast.show(actionData.message, { isError: true });
    }
  }, [actionData]);

  

  return (
    <Page
      backAction={{
        content: "Back",
        onAction: handleBack,
      }}
      title="Generate Collections"
    >
      <Layout>
        <Layout.Section>
          <Card>
            <Box padding="400">
              <BlockStack gap="400">
                <ProgressBar progress={(step / 3) * 100} size="small" />

                <Text variant="headingMd" as="h2">
                  Step {step} of 3:{" "}
                  {step === 1
                    ? "Choose Source"
                    : step === 2
                      ? "Select Attributes"
                      : "Review & Generate"}
                </Text>

                {step === 1 && (
                  <BlockStack gap="400">
                    <Text variant="bodyLg">
                      Choose how you want to generate your collections
                    </Text>

                    <Card background="bg-surface-secondary">
                      <Box padding="400">
                        <BlockStack gap="300">
                          <InlineStack gap="200" blockAlign="center">
                            <Checkbox
                              label="Use my store data only"
                              checked={!useCompetitor}
                              onChange={() => setUseCompetitor(false)}
                            />
                          </InlineStack>
                          <Text variant="bodyMd" as="p" tone="subdued">
                            Generate collections based on your existing products
                            and store data
                          </Text>
                        </BlockStack>
                      </Box>
                    </Card>

                    <Card background="bg-surface-secondary">
                      <Box padding="400">
                        <BlockStack gap="300">
                          <InlineStack gap="200" blockAlign="center">
                            <Checkbox
                              label="Use competitor insights"
                              checked={useCompetitor}
                              onChange={() => setUseCompetitor(true)}
                            />
                          </InlineStack>

                          {useCompetitor && (
                            <BlockStack gap="300">
                              <TextField
                                label="Competitor store URL"
                                value={competitorUrl}
                                onChange={handleCompetitorURLChange}
                                placeholder="e.g., competitor-store.myshopify.com"
                                disabled={isUrlSynced}
                              />

                              {!isUrlSynced && (
                                <Button
                                  onClick={handleSyncUrl}
                                  disabled={!competitorUrl}
                                  loading={isLoading}
                                >
                                  Analyze Competitor
                                </Button>
                              )}

                              {isUrlSynced && (
                                <Banner tone="success">
                                  Competitor URL analyzed successfully
                                </Banner>
                              )}
                            </BlockStack>
                          )}

                          <Text variant="bodyMd" as="p" tone="subdued">
                            Enhance your collections with insights from
                            competitor stores
                          </Text>
                        </BlockStack>
                      </Box>
                    </Card>
                  </BlockStack>
                )}

                {step === 2 && (
                  <BlockStack gap="400">
                    <Text variant="bodyLg">
                      Select attributes to include in your collections
                    </Text>

                    <Card background="bg-surface-secondary">
                      <Box padding="400">
                        <BlockStack gap="300">
                          <Text variant="headingMd" as="h3">
                            Product Attributes
                          </Text>
                          <Divider />
                          <BlockStack gap="300">
                            {Object.entries(productAttributes).map(
                              ([key, value]) => (
                                <Checkbox
                                  key={key}
                                  label={
                                    key.charAt(0).toUpperCase() + key.slice(1)
                                  }
                                  checked={value}
                                  onChange={() =>
                                    handleProductAttributeChange(key)
                                  }
                                />
                              ),
                            )}
                          </BlockStack>
                        </BlockStack>
                      </Box>
                    </Card>

                    <Card background="bg-surface-secondary">
                      <Box padding="400">
                        <BlockStack gap="300">
                          <Text variant="headingMd" as="h3">
                            Seasonal Collections
                          </Text>
                          <Divider />
                          <BlockStack gap="300">
                            {Object.entries(seasonalAttributes).map(
                              ([key, value]) => (
                                <Checkbox
                                  key={key}
                                  label={
                                    key.charAt(0).toUpperCase() + key.slice(1)
                                  }
                                  checked={value}
                                  onChange={() =>
                                    handleSeasonalAttributeChange(key)
                                  }
                                />
                              ),
                            )}
                          </BlockStack>
                        </BlockStack>
                      </Box>
                    </Card>
                  </BlockStack>
                )}

                {step === 3 && (
                  <BlockStack gap="400">
                    <Text variant="bodyLg">
                      Review your selections and generate collections
                    </Text>

                    <Card background="bg-surface-secondary">
                      <Box padding="400">
                        <BlockStack gap="300">
                          <Text variant="headingMd" as="h3">
                            Collection Source
                          </Text>
                          <Text variant="bodyMd">
                            {useCompetitor
                              ? "Using competitor insights"
                              : "Using your store data only"}
                            {useCompetitor &&
                              competitorUrl &&
                              ` (${competitorUrl})`}
                          </Text>

                          <Text variant="headingMd" as="h3">
                            Product Attributes
                          </Text>
                          <Text variant="bodyMd">
                            {Object.entries(productAttributes)
                              .filter(([_, value]) => value)
                              .map(
                                ([key]) =>
                                  key.charAt(0).toUpperCase() + key.slice(1),
                              )
                              .join(", ") || "None selected"}
                          </Text>

                          <Text variant="headingMd" as="h3">
                            Seasonal Collections
                          </Text>
                          <Text variant="bodyMd">
                            {Object.entries(seasonalAttributes)
                              .filter(([_, value]) => value)
                              .map(
                                ([key]) =>
                                  key.charAt(0).toUpperCase() + key.slice(1),
                              )
                              .join(", ") || "None selected"}
                          </Text>
                        </BlockStack>
                      </Box>
                    </Card>

                    <Banner tone="info">
                      Once generated, you'll be able to review and publish your
                      collections from the Collections page.
                    </Banner>
                  </BlockStack>
                )}

                <InlineStack align="end" gap="300">
                  {step > 1 && <Button onClick={handleBack}>Back</Button>}

                  {step < 3 ? (
                    <Button
                      primary
                      onClick={handleNext}
                      disabled={step === 1 && useCompetitor && !isUrlSynced}
                    >
                      Continue
                    </Button>
                  ) : (
                    <Button
                      primary
                      onClick={handleGenerate}
                      loading={isLoading}
                      icon={WandIcon}
                    >
                      Generate Collections
                    </Button>
                  )}
                </InlineStack>
              </BlockStack>
            </Box>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
