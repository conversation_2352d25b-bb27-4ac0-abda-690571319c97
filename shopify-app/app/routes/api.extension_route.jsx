// import { json } from "@remix-run/node";
// import { supabase } from "../db/supabase_insert_helper";
// import { loggerError, loggerInfo } from "../helper/loggerHelper";
// import { engineBaseUrl, engineAPIVersion, collectionRoute, manualCreateionRoute, manualCreationCallback } from "../configs/config";
// import dotenv from 'dotenv'
// import axios from 'axios'

// dotenv.config();

// const appToken = process.env.BINCHA_APP_TOKEN;
// const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${collectionRoute}${manualCreateionRoute}`;
// const appUrl = process.env.SHOPIFY_APP_URL;



// export const action = async ({request}) => {
//     const requestBody = await request.json();
//     try {
//         const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('store_name', requestBody.shopName);
//         if(storeDataError){
//             loggerError('Store Details not found', requestBody.shopName, storeDataError.message);
//             return {
//                 status: 400,
//                 message: 'something went wrong',
//                 data: null
//             }
//         }
        
//         // Check if collection generation limit is reached
//         const collectionCount = storeData[0].collection_count;
//         const collectionGeneratedCount = storeData[0].collection_generated_count;
        
//         if (collectionGeneratedCount >= collectionCount) {
//             loggerInfo('Collection generation limit reached', requestBody.shopName);
//             return {
//                 status: 400,
//                 message: 'Your collection generated limit is over',
//                 data: null
//             }
//         }
        
//         const engineAPIToken = storeData[0].engine_api_token;
//         const uuid = storeData[0].uuid
//         const callbackUrl = `${appUrl}${manualCreationCallback}/${uuid}`

//         const {data: preferencesData, error: preferencesDataError} = await supabase.from('preferences').select('*').eq('store_id', storeData[0].id);
//         if(preferencesDataError){
//             loggerError('Preferences Data not found', requestBody.shopName, preferencesDataError.message);
//             return {
//                 status: 400,
//                 message: 'something went wrong',
//                 data: null
//             }
//         }
//         const product_attributes = preferencesData[0]?.product_attributes ?? {};
//         const seasonal_attributes = preferencesData[0]?.seasonal_attributes ?? {};
//         const customer_attributes = preferencesData[0]?.customer_attributes ?? {};
//         const location_attributes = preferencesData[0]?.location_attributes ?? {};
//         const market_attributes = preferencesData[0]?.market_attributes ?? {};


//        const payLoad = {
//             callback_url: callbackUrl,
//             is_manual_creation: true,
//             keywords_origin_type: 'competitor',
//             attributes: {
//                 "product": product_attributes,
//                 "seasonal": seasonal_attributes,
//                 "customer": customer_attributes,
//                 "market": market_attributes,
//                 "location": location_attributes,
//             }
//         }


//         const manualCreation = await axios.post(endPoint, payLoad, {
//             headers: {
//             'X-PROACTIVE-TOKEN': engineAPIToken,
//             'X-BINCHA-APP-TOKEN': appToken
//             }
//         })
//         if(manualCreation.data.status_code !== 200){
//             loggerError('Something went wrong to create the collections by Manual', requestBody.shopName);
//             return {
//                 status: 400,
//                 message: "Something went wrong",
//                 data: null
//             }
//         }

//         if(manualCreation.data.status_code === 200){
//             const {data: collectionJobData, error: collectionJobDataError} = await supabase.from('collection_jobs').insert({
//                 store_id: storeData[0].id,
//                 task_id: manualCreation.data.data.task_id,
//                 task_status: 'started',
//                 attributes: payLoad.attributes,
//                 origin: 'admin',
//                 updated_at: new Date().toISOString()
//             })
//             if(collectionJobDataError){
//                 loggerError('Something went wrong to create the collections by Manual', requestBody.shopName, collectionJobDataError.message);
//                 return {
//                     status: 400,
//                     message: "Something went wrong",
//                     data: null
//                 }
//             }
//         }

//         return {
//             status: manualCreation.data.status_code,
//             message: manualCreation.data.message,
//             data: null
//         }
       
//     } catch(error) {
//         loggerError('Something went wrong to create the collections by Manual', requestBody.shopName, error.message);
//         return {
//             status: 400,
//             message: 'something went wrong',
//             data: null
//         }   
//     }
// }

import { json } from "@remix-run/node";
import { supabase } from "../db/supabase_insert_helper";
import { loggerError, loggerInfo } from "../helper/loggerHelper";
import {
  engineBaseUrl,
  engineAPIVersion,
  collectionRoute,
  manualCreateionRoute,
  manualCreationCallback
} from "../configs/config";
import dotenv from "dotenv";
import axios from "axios";

dotenv.config();

const appToken = process.env.BINCHA_APP_TOKEN;
const appUrl = process.env.SHOPIFY_APP_URL;
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${collectionRoute}${manualCreateionRoute}`;

// 🧩 Helper to wrap responses with CORS headers
function withCors(data, status = 200) {
  return json(data, {
    status,
    headers: {
      "Access-Control-Allow-Origin": "*", // Change to your frontend domain for production
      "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-PROACTIVE-TOKEN, X-BINCHA-APP-TOKEN",
    },
  });
}

// 🧩 Handle preflight (OPTIONS) requests
export const loader = async () => {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
      "Access-Control-Allow-Headers":
        "Content-Type, Authorization, X-PROACTIVE-TOKEN, X-BINCHA-APP-TOKEN",
    },
  });
};

// 🧩 Handle the POST request
export const action = async ({ request }) => {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
        "Access-Control-Allow-Headers":
          "Content-Type, Authorization, X-PROACTIVE-TOKEN, X-BINCHA-APP-TOKEN",
      },
    });
  }

  const requestBody = await request.json();

  try {
    const { data: storeData, error: storeDataError } = await supabase
      .from("stores")
      .select("*")
      .eq("store_name", requestBody.shopName);

    if (storeDataError || !storeData || storeData.length === 0) {
      loggerError("Store Details not found", requestBody.shopName, storeDataError?.message);
      return withCors({
        message: "Store not found",
        data: null,
      }, 400);
    }

    const store = storeData[0];
    if (store.collection_generated_count >= store.collection_count) {
      loggerInfo("Collection generation limit reached", requestBody.shopName);
      return withCors({
        message: "Your collection generated limit is over",
        data: null,
      }, 400);
    }

    const callbackUrl = `${appUrl}${manualCreationCallback}/${store.uuid}`;

    const { data: preferencesData, error: preferencesDataError } = await supabase
      .from("preferences")
      .select("*")
      .eq("store_id", store.id);

    if (preferencesDataError || !preferencesData || preferencesData.length === 0) {
      loggerError("Preferences Data not found", requestBody.shopName, preferencesDataError?.message);
      return withCors({
        message: "Preferences not found",
        data: null,
      }, 400);
    }

    const prefs = preferencesData[0];
    const payLoad = {
      callback_url: callbackUrl,
      is_manual_creation: true,
      keywords_origin_type: "competitor",
      attributes: {
        product: prefs.product_attributes ?? {},
        seasonal: prefs.seasonal_attributes ?? {},
        customer: prefs.customer_attributes ?? {},
        market: prefs.market_attributes ?? {},
        location: prefs.location_attributes ?? {},
      },
    };

    const manualCreation = await axios.post(endPoint, payLoad, {
      headers: {
        "X-PROACTIVE-TOKEN": store.engine_api_token,
        "X-BINCHA-APP-TOKEN": appToken,
      },
    });

    if (manualCreation.data.status_code !== 200) {
      loggerError("Engine returned error for manual creation", requestBody.shopName);
      return withCors({
        message: "Failed to create collection via engine",
        data: null,
      }, 400);
    }

    const { error: collectionJobDataError } = await supabase.from("collection_jobs").insert({
      store_id: store.id,
      task_id: manualCreation.data.data.task_id,
      task_status: "started",
      attributes: payLoad.attributes,
      origin: "admin",
      updated_at: new Date().toISOString(),
    });

    if (collectionJobDataError) {
      loggerError("Failed to insert collection job", requestBody.shopName, collectionJobDataError.message);
      return withCors({
        message: "Failed to insert collection job",
        data: null,
      }, 400);
    }

    return withCors({
      message: manualCreation.data.message,
      data: null,
    }, manualCreation.data.status_code);

  } catch(error) {
    loggerError("Unexpected error in manual creation", requestBody.shopName, error.message);
    return withCors({
      message: "Something went wrong",
      data: null,
    }, 400);
  }
};
