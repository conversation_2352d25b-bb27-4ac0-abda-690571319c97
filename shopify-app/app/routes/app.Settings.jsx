import React, { useCallback, useState, useEffect } from "react";
import { json } from "@remix-run/node";
import { useActionData, useSubmit, useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  TextField,
  RadioButton,
  Badge,
  Button,
  Spinner,
  InlineStack,
  BlockStack,
  Select,
  Box,
  Banner,
  Icon,
  Avatar,
  SkeletonDisplayText,
  SkeletonBodyText,
  Thumbnail,
} from "@shopify/polaris";
import { Modal, TitleBar, useAppBridge } from "@shopify/app-bridge-react";
import {
  DeleteIcon,
  ExternalIcon,
  ExternalSmallIcon,
} from "@shopify/polaris-icons";
import { authenticate } from "../shopify.server";
import {
  fetchSettingsData,
  saveEmailStatus,
  saveShuffleStatus,
} from "../models/saveEmailStatus.server";
import flag from "../configs/featureFlag.json";

import data from "../locales/en.json";
import content from "../locales/en.json";
import { getGoogleOauth } from "../models/googleOauthFlow.server";
import {
  selectPropertyId,
  saveGoogleAccount,
} from "../models/saveEmailStatus.server";
import { googleAccountLogout } from "../models/googleAccountLogout.server";
import { trackButtonClick, usePageViewTracking } from "../helper/analytics";
import { googleOAuthForGSC } from "../models/googleOAuthForGSC.server";
import { googleSearchConsoleLogout } from "../models/googleSearchConsoleLogout.server";
import { saveWeeklyReportStatus } from "../models/saveEmailStatus.server";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const shopName = session.shop;
  return json({ shopName });
};

export const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const data = { ...Object.fromEntries(await request.formData()) };
  let actionData;

  switch (data.type) {
    case "emailSave":
      actionData = await saveEmailStatus(admin, data.emailValue);
      break;
    case "fetchSettingsData":
      actionData = await fetchSettingsData(admin);
      break;
    case "shuffleSave":
      actionData = await saveShuffleStatus(admin, data.data);
      break;
    case "googleOauth":
      actionData = await getGoogleOauth(request, admin);
      break;
    case "selectPropertyId":
      actionData = await selectPropertyId(admin, data.data);
      break;
    case "googleAccountSave":
      actionData = await saveGoogleAccount(
        admin,
        data.account_id,
        data.property_id,
      );
      break;
    case "validAccountLogout":
      actionData = await googleAccountLogout(admin);
      break;
    case "googleSearchConsoleConnect":
      actionData = await googleOAuthForGSC(admin);
      break;
    case "googleSearchConsoleLogout":
      actionData = await googleSearchConsoleLogout(admin);
      break;
    case 'weeklyReportSave':
      actionData = await saveWeeklyReportStatus(admin, data.data);
      break;
  }

  return json({
    status: actionData?.status,
    message: actionData?.message,
    data: actionData?.data,
    type: data.type,
  });
};

export default function Settings() {
  const submit = useSubmit();
  const shopify = useAppBridge();
  const actionData = useActionData();
  const { shopName } = useLoaderData();
  const [emailEnabled, setEmailEnabled] = useState("disabled");
  const [turnOnButtonLoading, setTurnOnButtonLoading] = useState(false);
  const [shuffleStatus, setShuffleStatus] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [openAIValue, setOpenAIValue] = useState("");
  const [existingKey, setExistingKey] = useState("");
  const [saveButtonLoading, setSaveButtonLoading] = useState(false);
  const [combinedAccounts, setCombinedAccounts] = useState([]);
  const [combinedAccountId, setCombinedAccountId] = useState([]);
  const [propertyName, setPropertyName] = useState([]);
  const [propertyId, setPropertyId] = useState([]);
  const [googleConnected, setGoogleConnected] = useState(false);
  const [userName, setUserName] = useState("");
  const [userPicture, setUserPicture] = useState("");
  const [gscConnectCheck, setGscConnectCheck] = useState(false);
  const [gscUserData, setGscUserData] = useState();
  const [gscLogoutLoading, setGSCLogoutLoading] = useState(false);
  const [isGSCFeatureEnabled, setISGSCFeatureEnabled] = useState(false);
  const [isMailFeatureEnabled, setIsMailFeatureEnabled] = useState(false);
  const [weeklyReportEnabled, setWeeklyReportEnabled] = useState("");


  // const STORAGE_KEY = "fetchSettingsData";
  // const EXPIRY_TIME = 3 * 24 * 60 * 60 * 1000; // 3 days in milliseconds

  // const saveToLocalStorage = (data) => {
  //   try {
  //     const timestampedData = { data, timestamp: Date.now() };
  //     localStorage.setItem(STORAGE_KEY, JSON.stringify(timestampedData));
  //   } catch (error) {
  //     console.error('Local Storage is not available:', error);
  //   }
  // };

  // const getFromLocalStorage = () => {
  //   try {
  //     const storedData = localStorage.getItem(STORAGE_KEY);
  //     if (!storedData) return null;

  //     const parsedData = JSON.parse(storedData);
  //     const isExpired = Date.now() - parsedData.timestamp >= EXPIRY_TIME;

  //     if (isExpired) {
  //       localStorage.removeItem(STORAGE_KEY);
  //       return null;
  //     }

  //     return parsedData.data;
  //   } catch (error) {
  //     console.error('Error reading from localStorage:', error);
  //     localStorage.removeItem(STORAGE_KEY);
  //     return null;
  //   }
  // };

  // const updateLocalStorage = (key, value) => {
  //   try {
  //     const currentData = getFromLocalStorage() || {}; // Default to empty object if null
  //     const updatedData = {
  //       ...currentData,
  //       [key]: value
  //     };
  //     saveToLocalStorage(updatedData);
  //   } catch (error) {
  //     console.error('Error updating localStorage:', error);
  //   }
  // };

  useEffect(() => {
    // const initializeData = async () => {
    // try {
    //   trackButtonClick("Initial Data Fetch", "Settings", { shop_name: shopName });
    setPageLoading(true);

    // const storedData = getFromLocalStorage();
    // if (storedData) {
    //   setEmailEnabled(storedData.emailEnabled);
    //   setShuffleStatus(storedData.shuffleEnabled);
    //   setCombinedAccounts(storedData.combinedAccount);
    //   setCombinedAccountId(storedData.combinedAccountId);
    //   setGoogleConnected(storedData.googleConnectCheck);
    //   setUserName(storedData.userName);
    //   setUserPicture(storedData.userPicture);
    //   setGscConnectCheck(storedData?.isGSCLoggedIn);
    //   setPageLoading(false);
    // } else {
    // Fetch fresh data from backend
    const formData = { type: "fetchSettingsData" };
    submit(formData, { method: "post" });
    // }
    // } catch (error) {
    //   console.error('Error initializing data:', error);
    //   setPageLoading(false);
    // }
    // };

    // initializeData();
  }, []);

  useEffect(() => {
    if (actionData?.status === 200) {
      switch (actionData.type) {
        case "fetchSettingsData":
          const settingsData = actionData.data;
          setEmailEnabled(settingsData.emailEnabled);
          setShuffleStatus(settingsData.shuffleEnabled);
          setCombinedAccounts(settingsData.combinedAccount);
          setCombinedAccountId(settingsData.combinedAccountId);
          setGoogleConnected(settingsData.googleConnectCheck);
          setUserName(settingsData.userName);
          setUserPicture(settingsData.userPicture);
          setGscConnectCheck(settingsData?.isGSCLoggedIn);
          setGscUserData(() => JSON.parse(settingsData?.dscDecryptedData));
          setISGSCFeatureEnabled(settingsData?.isGSCFeatureEnabled);
          setIsMailFeatureEnabled(settingsData?.isMailReportEnabled);
          setWeeklyReportEnabled(settingsData?.isWeeklyReportEnabled === true ? 'enabled' : 'disabled');
          setPageLoading(false);
          // localStorage.setItem('test', 'testing method is ok or not')
          // Save fresh data to localStorage
          // saveToLocalStorage(settingsData);
          break;

        case "shuffleSave":
          setShuffleStatus(actionData.data);
          setTurnOnButtonLoading(false);
          // updateLocalStorage("shuffleEnabled", actionData.data);
          break;

        case "emailSave":
          setEmailEnabled(actionData.data);
          // updateLocalStorage("emailEnabled", actionData.data);
          break;
        case "googleOauth":
          window.parent.location.href = actionData?.data;
          break;

        case "selectPropertyId":
          setPropertyName(actionData.data.label);
          setPropertyId(actionData.data.value);
          // updateLocalStorage("propertyId", actionData.data.value);
          break;

        case "googleAccountSave":
          if (Array.isArray(actionData.data)) {
            const accounts = actionData.data.map((item) => item.accountLabel);
            const accountIds = actionData.data.map((item) => item.accountId);
            const propertyLabels = actionData.data.map(
              (item) => item.propertyLabel,
            );
            const propertyIds = actionData.data.map((item) => item.propertyId);

            setCombinedAccounts(accounts);
            setCombinedAccountId(accountIds);
            setPropertyName(propertyLabels);
            setPropertyId(propertyIds);
          } else {
            setCombinedAccounts([actionData.data.accountLabel]);
            setCombinedAccountId([actionData.data.accountId]);
            setPropertyName([actionData.data.propertyLabel]);
            setPropertyId([actionData.data.propertyId]);
          }
          setGoogleAccountLoading(false);
          // updateLocalStorage("combinedAccount", actionData.data);
          break;

        case "validAccountLogout":
          setValidAccountLogoutLoading(false);
          setGoogleConnected(actionData.data);
          shopify.modal.hide("valid-account");
          // updateLocalStorage("googleConnectCheck", actionData.data);
          break;

        case "googleSearchConsoleConnect":
          setGSCLoading(false);
          window.parent.location.href = actionData?.data;
          break;

        case "googleSearchConsoleLogout":
          setGSCLogoutLoading(false);
          setGscConnectCheck(false);
          // updateLocalStorage("isGSCLoggedIn", actionData.data);
          break;
      }

      if (
        actionData.type !== "googleOauth" &&
        actionData.type !== "fetchSettingsData" &&
        actionData.type !== "selectPropertyId"
      ) {
        shopify.toast.show(actionData.message, actionData.status);
      }
    } else if (actionData?.status === 400) {
      setPageLoading(false);
    }
  }, [actionData]);

  // useEffect(() => {
  //   const openAI = localStorage.getItem("openAIkey");
  //   setExistingKey(openAI || "");
  // }, []);

  const handleEmailChange = useCallback((value) => {
    trackButtonClick("Email Save", "Settings", { shop_name: shopName });
    setEmailEnabled(value);
    submit({ type: "emailSave", emailValue: value }, { method: "post" });
  }, []);

  const handleOpenAIValueChange = useCallback((value) => {
    setOpenAIValue(value);
  }, []);

  const handleDeleteOpenAIKey = useCallback(() => {
    localStorage.removeItem("openAIkey");
    setOpenAIValue("");
    setExistingKey("");
  }, []);

  const handleSaveOpenAIKey = useCallback(async () => {
    if (openAIValue === null || openAIValue === "") {
      shopify.toast.show(
        "The input field is Empty. Please enter some valid OpenAI key..",
      );
      return;
    }
    setSaveButtonLoading(true);
    const keyValidation = await validateOpenAIkey(openAIValue);
    if (keyValidation.status === 200) {
      localStorage.setItem("openAIkey", openAIValue);
      setExistingKey(openAIValue);
      shopify.toast.show(keyValidation.message, keyValidation.status);
    } else {
      shopify.toast.show(keyValidation.message, keyValidation.status);
    }
    setSaveButtonLoading(false);
  }, [openAIValue]);

  const handleShuffleChange = useCallback((value) => {
    trackButtonClick("Auto Shuffle Function", "Settings", {
      shop_name: shopName,
    });
    setTurnOnButtonLoading(true);
    submit({ type: "shuffleSave", data: value }, { method: "post" });
  }, []);

  const handleGoogleOauth = () => {
    trackButtonClick("Google Oauth function", "Settings", {
      shop_name: shopName,
    });
    const formData = {
      type: "googleOauth",
    };
    submit(formData, { method: "post" });
  };

  // Google Account Logis

  const [GAaccountSelected, setGAaccountSelected] = useState("");
  const handleGAaccountChange = useCallback((value) => {
    setGAaccountSelected(value);
    trackButtonClick("Select GA4 Account", "Settings", { shop_name: shopName });
    const formData = {
      type: "selectPropertyId",
      data: value,
    };
    submit(formData, { method: "post" });
  }, []);

  const GAaccountOptions = combinedAccounts?.map((account, index) => ({
    label: account,
    value: combinedAccountId[index],
  }));

  // propertyid Google
  const [selectedPropertyId, setSelectedPropertyId] = useState("");
  const handlePropertyIdChange = useCallback(
    (value) => setSelectedPropertyId(value),
    [],
  );
  const propertyIdOptions = propertyName.map((names, index) => ({
    label: names,
    value: propertyId[index],
  }));

  const [googleAccountLoading, setGoogleAccountLoading] = useState(false);

  const handleGoogleAccountSave = () => {
    trackButtonClick("Save selected GA4 and Property Id", "Settings", {
      shop_name: shopName,
    });
    setGoogleAccountLoading(true);
    const formData = {
      account_id: GAaccountSelected,
      property_id: selectedPropertyId,
      type: "googleAccountSave",
    };
    submit(formData, { method: "post" });
  };

  const handleValidAccountLogoutModalOpen = (id) => {
    shopify.modal.show(id);
  };

  const [validAccountLogoutLoading, setValidAccountLogoutLoading] =
    useState(false);
  const handleValidAccountLogout = () => {
    trackButtonClick("Check goole valid Account", "Settings", {
      shop_name: shopName,
    });
    setValidAccountLogoutLoading(true);
    const formData = {
      type: "validAccountLogout",
    };
    submit(formData, { method: "post" });
  };

  const [GSCLoading, setGSCLoading] = useState(false);
  const handleGoogleSearchConsoleConnect = () => {
    trackButtonClick("Google Search Console Connect", "Settings", {
      shop_name: shopName,
    });
    if (!isGSCFeatureEnabled) {
      shopify.toast.show("Please upgrade your plan to use this feature", {
        isError: true,
      });
      return;
    }
    setGSCLoading(true);
    const formData = {
      type: "googleSearchConsoleConnect",
    };
    submit(formData, { method: "post" });
  };

  const handleGSCLogout = () => {
    setGSCLogoutLoading(true);
    trackButtonClick("Google Search Console Disconnect", "Settings", {
      shop_name: shopName,
    });
    const formData = {
      type: "googleSearchConsoleLogout",
    };
    submit(formData, { method: "post" });
  };

  const handleWeeklyReportChange = useCallback((value) => {
    if (value === 'enabled' && !isMailFeatureEnabled) {
      shopify.toast.show("Please upgrade your plan to use this feature", {
        isError: true,
      })
      return;
    }
    setWeeklyReportEnabled(value);
    const formData = {
      type: "weeklyReportSave",
      data: value,
    }
    submit(formData, { method: "post" });
  }, []);

  usePageViewTracking("Settings", { shop_name: shopName });

  return (
    <Page >
      {!pageLoading ? (
        <Layout>
          <Layout.Section>
            <Card padding={600}>
              <BlockStack gap="500">
                <BlockStack gap="200">
                  <Text variant="heading2xl" as="h1">
                    {content.Rank_collections.Settings.heading_1}
                  </Text>
                  <Text variant="bodyMd" as="p" color="subdued">
                    {content.Rank_collections.Settings.content_1}
                  </Text>
                </BlockStack>

                <Card title="Email Notification">
                  <BlockStack gap="300">
                    <Text variant="headingMd" as="h2" fontWeight="semibold">
                      {content.Rank_collections.Settings.heading_2}
                    </Text>
                    <Text variant="bodyMd" as="p" color="subdued">
                      {content.Rank_collections.Settings.content_2}
                    </Text>
                    <RadioButton
                      label={content.Rank_collections.Settings.radio_1}
                      helpText={content.Rank_collections.Settings.help_text_1}
                      checked={emailEnabled === "enabled"}
                      id="EmailEnabled"
                      name="Email"
                      onChange={() => handleEmailChange("enabled")}
                      tone="magic"
                    />
                    <RadioButton
                      label={content.Rank_collections.Settings.radio_2}
                      helpText={content.Rank_collections.Settings.help_text_2}
                      checked={emailEnabled === "disabled"}
                      id="EmailDisabled"
                      name="Email"
                      onChange={() => handleEmailChange("disabled")}
                      tone="magic"
                    />
                  </BlockStack>
                </Card>

                {/* <Card>
                <BlockStack gap="400">
                  <InlineStack align="space-between">
                    <Text variant="headingMd" as="h2">
                      {content.Rank_collections.Settings.heading_7}
                    </Text>
                    <Badge tone={shuffleStatus ? "success" : "critical"}>
                      {shuffleStatus
                        ? content.Rank_collections.Settings.badge_1
                        : content.Rank_collections.Settings.badge_2}
                    </Badge>
                  </InlineStack>
                  <InlineStack align="space-between">
                    <Text variant="bodyMd" as="p">
                      {content.Rank_collections.Settings.heading_8}
                    </Text>
                  </InlineStack>

                  <Text variant="bodyMd" as="p">
                    {content.Rank_collections.Settings.content_3}
                  </Text>
                  <Button
                    onClick={() => handleShuffleChange(!shuffleStatus)}
                    loading={turnOnButtonLoading}
                  >
                    {shuffleStatus
                      ? content.Rank_collections.Settings.button_5
                      : content.Rank_collections.Settings.button_4}
                  </Button>
                </BlockStack>
              </Card> */}

                {flag.settings.google_analytics_show && (
                  <Card>
                    {/* Header Section */}
                    <BlockStack gap="300">
                      <Box
                        style={{
                          padding: "4",
                          borderBottom: "divider",
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <Text variant="headingMd">
                          {content.Rank_collections.Settings.heading_4}
                        </Text>
                        <Badge tone={googleConnected ? "success" : "critical"}>
                          {googleConnected ? "Connected" : "Not Connected"}
                        </Badge>
                      </Box>

                      {/* Main Content Section */}
                      <BlockStack spacing="5" padding="4">
                        {/* Google Login or User Info */}
                        {!googleConnected ? (
                          <Button
                            id="google-button"
                            onClick={handleGoogleOauth}
                          >
                            <InlineStack
                              gap="200"
                              align="center"
                              blockAlign="center"
                            >
                              <img
                                src="https://ik.imagekit.io/1tudtg11f/7123025_logo_google_g_icon.png?updatedAt=1728050412764"
                                alt="Google Logo"
                                style={{ width: "20px", height: "20px" }}
                              />
                              <Text variant="bodyMd" fontWeight="regular">
                                {content.Rank_collections.Settings.button_1}
                              </Text>
                            </InlineStack>
                          </Button>
                        ) : (
                          <Box
                            padding="2"
                            border="divider"
                            display="inlineBlock"
                          >
                            <InlineStack
                              gap="300"
                              align="center"
                              blockAlign="center"
                            >
                              <Avatar
                                source={userPicture}
                                alt="profile picture"
                                size="small"
                              />
                              <Text variant="bodyMd" fontWeight="semibold">
                                {userName}
                              </Text>
                              <Button
                                plain
                                onClick={() =>
                                  handleValidAccountLogoutModalOpen(
                                    "valid-account",
                                  )
                                }
                              >
                                {content.Rank_collections.Settings.button_2}
                              </Button>
                            </InlineStack>
                          </Box>
                        )}

                        {/* Banner or Select Options */}
                        {googleConnected && combinedAccounts.length === 0 && (
                          <Banner status="warning">
                            {content.Rank_collections.Settings.warning_1}{" "}
                            <Button
                              plain
                              url="https://analytics.google.com"
                              target="_blank"
                            >
                              {content.Rank_collections.Settings.button_6}
                            </Button>{" "}
                            Or{" "}
                            <Button
                              loading={validAccountLogoutLoading}
                              plain
                              onClick={handleValidAccountLogout}
                            >
                              {content.Rank_collections.Settings.button_7}
                            </Button>{" "}
                            {content.Rank_collections.Settings.warning_2}
                          </Banner>
                        )}

                        {googleConnected && combinedAccounts.length > 0 && (
                          <BlockStack gap="300">
                            {/* Google Account Selector */}
                            <Select
                              placeholder={
                                content.Rank_collections.Settings.heading_5
                              }
                              label={
                                content.Rank_collections.Settings.heading_5
                              }
                              options={GAaccountOptions}
                              value={GAaccountSelected}
                              onChange={handleGAaccountChange}
                            />

                            {/* Property ID Selector */}
                            <Select
                              placeholder={
                                content.Rank_collections.Settings.heading_6
                              }
                              label={
                                content.Rank_collections.Settings.heading_6
                              }
                              options={propertyIdOptions}
                              value={selectedPropertyId}
                              onChange={handlePropertyIdChange}
                            />

                            {/* Save Button */}
                            <Button
                              primary
                              loading={googleAccountLoading}
                              onClick={handleGoogleAccountSave}
                            >
                              {content.Rank_collections.Settings.button_3}
                            </Button>
                          </BlockStack>
                        )}
                      </BlockStack>
                    </BlockStack>
                  </Card>
                )}
                <Card sectioned>
                  <BlockStack gap="300">
                    <InlineStack align="space-between" blockAlign="center">
                      <Text variant="headingMd" as="h6" fontWeight="semibold">
                        {content.Rank_collections.Settings.heading_9}
                      </Text>
                      <Badge tone={gscConnectCheck ? "success" : "critical"}>
                        {gscConnectCheck ? "Connected" : "Not Connected"}
                      </Badge>
                    </InlineStack>
                    {gscConnectCheck ? (
                      <InlineStack align="space-between" blockAlign="center">
                        <Text variant="bodyMd" as="p" fontWeight="semibold">
                          {content.Rank_collections.Settings.content_4}
                        </Text>
                        <Card sectioned>
                          <InlineStack
                            align="space-between"
                            blockAlign="center"
                            gap="500"
                          >
                            <Box
                              style={{
                                width: "25px",
                                height: "25x",
                                borderRadius: "50%",
                              }}
                            >
                              <img
                                width="100%"
                                height="100%"
                                src={gscUserData?.picture}
                                alt="profile picture"
                                style={{
                                  objectFit: "cover",
                                  borderRadius: "50%",
                                }}
                              />
                            </Box>
                            <Text
                              variant="bodyMd"
                              as="h6"
                              fontWeight="semibold"
                            >
                              {gscUserData.email}
                            </Text>
                          </InlineStack>
                        </Card>
                      </InlineStack>
                    ) : (
                      
                        <Button
                          
                          size="large"
                          loading={GSCLoading}
                          onClick={handleGoogleSearchConsoleConnect}
                        >
                          <InlineStack gap='200' align='center' blockAlign="center">
                          <img
                                src="https://ik.imagekit.io/1tudtg11f/7123025_logo_google_g_icon.png?updatedAt=1728050412764"
                                alt="Google Logo"
                                style={{ width: "20px", height: "20px" }}
                              />
                          {content.Rank_collections.Settings.button_8}
                          </InlineStack>
                        </Button>
                      
                    )}

                    {gscConnectCheck && (
                      <InlineStack align="end" blockAlign="end">
                        <Button
                          variant="primary"
                          size="large"
                          loading={gscLogoutLoading}
                          onClick={handleGSCLogout}
                        >
                          {content.Rank_collections.Settings.button_9}
                        </Button>
                      </InlineStack>
                    )}
                  </BlockStack>
                </Card>

                <Card sectioned>
                  <BlockStack gap="300">
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.Settings.heading_10}
                    </Text>
                    <Text variant="bodyMd" as="p" fontWeight="regular">
                      {content.Rank_collections.Settings.content_5}
                    </Text>
                    <RadioButton
                      label={content.Rank_collections.Settings.radio_3}
                      helpText={content.Rank_collections.Settings.help_content_1}
                      checked={weeklyReportEnabled === "enabled"}
                      name="weeklyReport"
                      id="enabled"
                      onChange={() => handleWeeklyReportChange("enabled")}
                    />

                    <RadioButton
                      label={content.Rank_collections.Settings.radio_4}
                      helpText={content.Rank_collections.Settings.help_content_2}
                      checked={weeklyReportEnabled === "disabled"}
                      name="weeklyReport"
                      id="disabled"
                      onChange={() => handleWeeklyReportChange("disabled")}
                    />
                  </BlockStack>
                </Card>
              </BlockStack>
            </Card>
          </Layout.Section>
        </Layout>
      ) : (
        <>
          <Page fullWidth>
            <Card sectioned>
              <BlockStack gap="300">
                <SkeletonDisplayText />
                <Box width="300px">
                  <SkeletonBodyText lines={1} />
                </Box>

                <Card sectioned>
                  <BlockStack gap="300">
                    <SkeletonDisplayText />
                    <Box width="300px">
                      <SkeletonBodyText lines={1} />
                    </Box>
                    <SkeletonDisplayText />
                    <Box width="400px">
                      <SkeletonBodyText lines={1} />
                    </Box>
                    <SkeletonDisplayText />
                    <Box width="600px">
                      <SkeletonBodyText lines={1} />
                    </Box>
                  </BlockStack>
                </Card>

                {flag.settings.google_analytics_show && (
                  <Card sectioned>
                    <BlockStack gap="300">
                      <InlineStack align="space-between">
                        <SkeletonDisplayText size="small" />
                        <SkeletonDisplayText size="small" />
                      </InlineStack>
                      <Box width="100%">
                        <SkeletonBodyText line={2} />
                      </Box>
                    </BlockStack>
                  </Card>
                )}
              </BlockStack>
            </Card>
          </Page>
        </>
      )}

      <Modal id="valid-account">
        <TitleBar title="Confirm Logout"></TitleBar>
        <Box style={{ padding: "10px", overflow: "hidden" }}>
          <Text variant="bodyLg" as="p" fontWeight="medium">
            Once you logout this account you won't receive any further update of
            your collection page's visit in future. Are you sure want to Logout
            this account
          </Text>
          <br />
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "rgba(128,128,128,0.1)",
            borderTop: "1px solid rgba(128, 128, 128, 0.1)",
            padding: "10px",
          }}
        >
          <InlineStack align="end" blockAlign="center">
            <Button
              loading={validAccountLogoutLoading}
              onClick={handleValidAccountLogout}
            >
              Logout
            </Button>
          </InlineStack>
        </Box>
      </Modal>
    </Page>
  );
}
