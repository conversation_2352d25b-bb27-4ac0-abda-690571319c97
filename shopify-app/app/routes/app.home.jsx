import { useEffect, useRef, useState, useCallback } from "react";
import { authenticate } from "../shopify.server";
import { json } from "@remix-run/node";
import {
  useLoaderData,
  useNavigate,
  useSubmit,
  useActionData,
} from "@remix-run/react";
import {
  Page,
  Card,
  BlockStack,
  Box,
  InlineStack,
  Text,
  Button,
  Icon,
  Link,
  Spinner,
  Badge,
  Layout,
  Grid,
} from "@shopify/polaris";
import { ClockIcon, WandIcon, HomeIcon } from "@shopify/polaris-icons";
import { Modal, TitleBar, useAppBridge } from "@shopify/app-bridge-react";

import SupportBanner from "../components/SupportBanner";
import KeyMetricsDisplay from "../components/KeyMetricsDisplay";
import StatusOverview from "../components/StatusOverview";
import HelpResources from "../components/HelpResources";
import GenerateAction from "../components/ActionCards";

import AutoScheduleCollectionsModal from "../components/autoScheduleCollectionModal";
import content from "../locales/en.json";

import { synctSchedulingInputsToAgent } from "../models/syncSchedulingInputsToAgent.server";
import { fetchScheduleConfig } from "../models/fetchScheduleConfig.server";
import { trackButtonClick, usePageViewTracking } from "../helper/analytics";
import { manualGenerate } from "../models/manualGenerate.server";
import { fetchTaskStatus } from "../models/fetchTaskStatusById.server";
import { fetchBestScheduleTime } from "../models/fetchBestScheduleTime.server";
import { fetchProductCount } from "../models/fetchProductCountFromAgent.server";
import { fetchDashboardMetrics } from "../models/fetchDahboardMetrics.server";
import flag from "../configs/featureFlag.json";
import { ContactLink } from "../configs/config";
import { syncCompetitorURL } from "../models/syncCompetitorURLToAgent.server";
import { checkCollectionLimit } from "../models/collectionLimitCheck.server";
import { fetchProductSyncStatusFromAgent } from "../models/fetchProductSyncStatus.server";

import { useModalManager } from "../hooks/useModalManager";
import { useInitialData } from "../hooks/useInitialData";
import { useDashboardActions } from "../hooks/useDashboardActions";

export const loader = async ({ request }) => {
  const { session, admin } = await authenticate.admin(request);
  const collectionLimit = await checkCollectionLimit(admin);
  try {
    const productCountResult = await fetchProductCount(admin);
    return json({
      shopName: session.shop,
      collectionLimit: collectionLimit.data,
      initialProductCount: productCountResult?.data?.count || 0,
    });
  } catch (error) {
    console.error("Failed to fetch product count in loader:", error);
    return json({
      shopName: session.shop,
      collectionLimit: collectionLimit.data,
      initialProductCount: 0,
    });
  }
};

async function handleInitialFetch(admin, formData) {
  const id = formData.get("product_sync_id");
  let productSyncStatus;
  if (id !== null) {
    productSyncStatus = await fetchProductSyncStatusFromAgent(admin, id);
  }
  const actionData = await fetchScheduleConfig(admin);
  const scheduleTime = await fetchBestScheduleTime(admin);
  const dashboardMetrics = await fetchDashboardMetrics(admin);

  return {
    status: actionData.status,
    message: actionData.message,
    data: actionData.data,
    domain: actionData.shopDomain,
    schedule: scheduleTime,
    metrics: dashboardMetrics.data,
    productSyncStatus: productSyncStatus?.data,
  };
}

async function handleFetchMetrics(admin) {
  const actionData = await fetchDashboardMetrics(admin);
  return {
    status: actionData.status,
    message: actionData.message,
    data: actionData.data,
  };
}

async function handleTaskStatus(admin, formData) {
  const id = formData.get("id");
  const jobStatus = await fetchTaskStatus(admin, id);
  return {
    status: jobStatus.status,
    message: jobStatus.message,
    data: jobStatus.data,
  };
}

async function handleSyncSchedule(admin, formData) {
  const attribute = formData.get("attributes");
  const scheduleSettings = formData.get("schedule-settings");
  const actionData = await synctSchedulingInputsToAgent(
    admin,
    attribute,
    scheduleSettings,
  );
  return {
    status: actionData.status,
    message: actionData.message,
    data: actionData.data,
  };
}

async function handleSyncUrl(admin, formData) {
  const url = formData.get("url");
  const actionData = await syncCompetitorURL(admin, url);
  return {
    status: actionData?.status,
    message: actionData?.message,
    data: actionData?.data,
  };
}

async function handleManualGenerateAction(admin, formData) {
  const compititorUrl = formData.get("competitorUrl");
  const attributes = formData.get("attributes");
  const actionData = await manualGenerate(admin, compititorUrl, attributes);
  return {
    status: actionData.status,
    message: actionData.message,
    data: actionData.data,
  };
}

async function handleFetchProductSync(admin, formData) {
  const id = formData.get("id");
  const actionData = await fetchProductSyncStatusFromAgent(admin, id);
  return {
    status: actionData.status,
    message: actionData.message,
    data: actionData.data,
  };
}

export const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const type = formData.get("type");

  try {
    let result;
    switch (type) {
      case "initial-fetch":
        result = await handleInitialFetch(admin, formData);
        break;
      case "fetch-metrics":
        result = await handleFetchMetrics(admin);
        break;
      case "task-status":
        result = await handleTaskStatus(admin, formData);
        break;
      case "sync-url":
        result = await handleSyncUrl(admin, formData);
        break;
      case "manual-generate":
        result = await handleManualGenerateAction(admin, formData);
        break;
      case "fetch-product-sync":
        result = await handleFetchProductSync(admin, formData);
        break;
      default:
        return json({
          status: 400,
          message: "Invalid action type",
          type,
        });
    }
    if (type === "sync-schedule") {
      result = await handleSyncSchedule(admin, formData);
    }

    return json({ ...result, type });

  } catch (error) {
    console.error(`Error handling action type "${type}":`, error);
    return json({
      status: 500,
      message: error.message || "An unexpected error occurred processing your request.",
      data: null,
      type: type,
    });
  }
};

// Custom Hook for manual generation task polling (non-blocking)
const useManualGenTaskPolling = (shopName, onTaskComplete) => {
  const submit = useSubmit();
  const actionData = useActionData();
  const shopify = useAppBridge();
  const [taskId, setTaskId] = useState(null);
  const [isPolling, setIsPolling] = useState(false);
  const initialFetchCalled = useRef(false);
  const singleTimeCallRef = useRef(false);

  // Check for existing task on component mount
  useEffect(() => {
    const existingTaskId = localStorage.getItem("manual-generate-task-id");
    if (existingTaskId) {
      setTaskId(existingTaskId);
      setIsPolling(true);
      handleCheckTaskStatus(existingTaskId);
    }
  }, []);

  const handleCheckTaskStatus = useCallback(
    (taskIdToCheck) => {
      const currentTaskId = taskIdToCheck || taskId;
      if (currentTaskId) {
        const formData = new FormData();
        formData.append("type", "task-status");
        formData.append("id", currentTaskId);
        submit(formData, { method: "post" });
      }
    },
    [submit, taskId],
  );

  // Handle task status responses
  useEffect(() => {
    if (actionData?.type === "task-status" && actionData?.status === 200) {
      if (actionData?.data === "STARTED" && !initialFetchCalled.current) {
        // Task started, call the refresh callback and continue polling
        initialFetchCalled.current = true;
        if (onTaskComplete) {
          onTaskComplete();
        }
        setTimeout(() => handleCheckTaskStatus(), 10000);
      } else if (
        actionData?.data === "COMPLETED" ||
        actionData?.data === "FAILED" ||
        actionData?.data === "NO_COLLECTIONS"
      ) {
        localStorage.removeItem("manual-generate-task-id");
        setTaskId(null);
        setIsPolling(false);
        initialFetchCalled.current = false;
        singleTimeCallRef.current = false;

        // Call the refresh callback to update data
        if (onTaskComplete) {
          onTaskComplete();
        }

        // Show user-friendly notifications
        if (actionData?.data === "COMPLETED") {
          shopify.toast.show("Collection generation completed successfully!", {
            isError: false,
          });
        } else if (
          actionData?.data === "NO_COLLECTIONS" ||
          actionData?.data === "FAILED"
        ) {
          shopify.toast.show(
            "No valid products available for the given keyword",
            { isError: true },
          );
          console.error(
            "Manual generation task failed or no collections found",
          );
        }
      } else {
        // Continue polling for other statuses (like PENDING)
        setTimeout(() => handleCheckTaskStatus(), 10000);
      }
    } else if (
      actionData?.type === "task-status" &&
      actionData?.status !== 200
    ) {
      if (!singleTimeCallRef.current) {
        singleTimeCallRef.current = true;
        initialFetchCalled.current = true;
        setIsPolling(false);

        // Call refresh callback on error to ensure data is up to date
        if (onTaskComplete) {
          onTaskComplete();
        }
      }
    }
  }, [actionData, handleCheckTaskStatus, onTaskComplete, shopify]);

  const startPolling = useCallback(
    (newTaskId) => {
      setTaskId(newTaskId);
      setIsPolling(true);
      localStorage.setItem("manual-generate-task-id", newTaskId);
      handleCheckTaskStatus(newTaskId);
    },
    [handleCheckTaskStatus],
  );

  return {
    taskId,
    isPolling,
    startPolling,
  };
};

// Custom Hook for product sync task polling (non-blocking)
const useProductSyncPolling = (
  initialTaskId,
  setProductSyncTaskId,
  onTaskComplete,
) => {
  const submit = useSubmit();
  const actionData = useActionData();
  const shopify = useAppBridge();
  const [syncTaskId, setSyncTaskId] = useState(initialTaskId);
  const [isSyncPolling, setIsSyncPolling] = useState(false);
  const initialFetchCalled = useRef(false);
  const singleTimeCallRef = useRef(false);

  // Initialize with existing task
  useEffect(() => {
    if (initialTaskId) {
      setSyncTaskId(initialTaskId);
      setIsSyncPolling(true);
      handleCheckProductSyncStatus(initialTaskId);
    }
  }, [initialTaskId]);

  const handleCheckProductSyncStatus = useCallback(
    (taskIdToCheck) => {
      const currentTaskId = taskIdToCheck || syncTaskId;
      if (currentTaskId) {
        const formData = new FormData();
        formData.append("type", "fetch-product-sync");
        formData.append("id", currentTaskId);
        submit(formData, { method: "post" });
      }
    },
    [submit, syncTaskId],
  );

  // Handle product sync status responses
  useEffect(() => {
    if (
      actionData?.type === "fetch-product-sync" &&
      actionData?.status === 200
    ) {
      if (actionData?.data === "STARTED" && !initialFetchCalled.current) {
        // Task started, call the refresh callback and continue polling
        initialFetchCalled.current = true;
        if (onTaskComplete) {
          onTaskComplete();
        }
        setTimeout(() => handleCheckProductSyncStatus(), 10000);
      } else if (
        actionData?.data === "COMPLETED" ||
        actionData?.data === "FAILED"
      ) {
        setSyncTaskId(null);
        setIsSyncPolling(false);
        setProductSyncTaskId(null);
        initialFetchCalled.current = false;
        singleTimeCallRef.current = false;

        // Call the refresh callback to update data
        if (onTaskComplete) {
          onTaskComplete();
        }

        // Show user-friendly notifications
        if (actionData?.data === "COMPLETED") {
          shopify.toast.show("Product sync completed successfully!", {
            isError: false,
          });
        } else if (actionData?.data === "FAILED") {
          shopify.toast.show("Product sync failed", { isError: true });
          console.error("Product sync task failed");
        }
      } else {
        // Continue polling for other statuses (like PENDING)
        setTimeout(() => handleCheckProductSyncStatus(), 10000);
      }
    } else if (
      actionData?.type === "fetch-product-sync" &&
      actionData?.status !== 200
    ) {
      if (!singleTimeCallRef.current) {
        singleTimeCallRef.current = true;
        initialFetchCalled.current = true;
        setIsSyncPolling(false);

        // Call refresh callback on error to ensure data is up to date
        if (onTaskComplete) {
          onTaskComplete();
        }
      }
    }
  }, [
    actionData,
    handleCheckProductSyncStatus,
    setProductSyncTaskId,
    onTaskComplete,
    shopify,
  ]);

  return {
    syncTaskId,
    isSyncPolling,
  };
};

export default function Home() {
  const navigate = useNavigate();
  const { shopName, collectionLimit, initialProductCount } = useLoaderData();
  const submit = useSubmit();

  const modalManager = useModalManager();
  const {
    isLoading: isPageLoading,
    isMetricsLoading,
    shopDomain,
    scheduleConfigs,
    conversionRate,
    opportunity,
    productCount,
    aiTime,
    automaticUpdate,
    scheduleStatus,
    productSyncTaskId: initialProductSyncTaskId,
    setProductSyncTaskId,
    updateCachedScheduleConfig,
    updateCachedUrlSyncStatus,
  } = useInitialData();

  // Create a refresh function to trigger initial data fetch
  const refreshHomeData = useCallback(() => {
    const formData = new FormData();
    formData.append("type", "initial-fetch");
    const storedSyncId = localStorage.getItem(
      "rankCollections-product_sync_task_id",
    );
    if (storedSyncId) {
      formData.append("product_sync_id", storedSyncId);
    }
    submit(formData, { method: "post", replace: true });
  }, [submit]);

  // Use the new non-blocking task polling hooks with proper refresh callbacks
  const {
    taskId: manualGenTaskId,
    isPolling: isManualGenPolling,
    startPolling: startManualGenPolling,
  } = useManualGenTaskPolling(shopName, refreshHomeData);

  const { syncTaskId: productSyncTaskId, isSyncPolling: isProductSyncPolling } =
    useProductSyncPolling(
      initialProductSyncTaskId,
      setProductSyncTaskId,
      refreshHomeData,
    );

  const dashboardActions = useDashboardActions(
    shopName,
    modalManager.closeAutoScheduleModal,
    startManualGenPolling,
    updateCachedScheduleConfig,
    updateCachedUrlSyncStatus
  );

  const displayProductCount = productCount || 0;

  useEffect(() => {
    if (scheduleConfigs?.urlSync !== undefined) {
      dashboardActions.setIsUrlSynced(scheduleConfigs.urlSync);
    }
  }, [scheduleConfigs?.urlSync, dashboardActions.setIsUrlSynced]);

  usePageViewTracking("Home Page", { shop_name: shopName });

  const isGeneratingBlocked = manualGenTaskId !== null || productSyncTaskId !== null;

  const handleGenerateClick = () => {
    if (productSyncTaskId !== null) {
      modalManager.openProductSyncModal();
    } else if (collectionLimit === 0) {
      modalManager.openLimitReachedModal();
    } else if (manualGenTaskId !== null) {
      modalManager.openInfoModal();
    } else {
      navigate("/app/generate-collections");
    }
  };

  return (
    <Page>
      {isPageLoading && (
        <Box
          style={{
            backdropFilter: "blur(3px)",
            backgroundColor: "rgba(128, 128, 128, 0.2)",
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            pointerEvents: "auto",
            zIndex: 1000,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            backfaceVisibility: "hidden",
          }}
        >
          <Spinner size="large" />
        </Box>
      )}

      <Box paddingBlockStart="400" paddingInline="0">
        <Layout>
          {/* Hero Section */}
          <Layout.Section>
            <Card background="bg-surface-secondary">
              <Box padding="500">
                <Grid>
                  <Grid.Cell columnSpan={{ xs: 6, sm: 4, md: 4, lg: 7, xl: 7 }}>
                    <BlockStack gap="400">
                      <Text variant="headingXl" as="h1" fontWeight="bold">
                        {content.Rank_collections.home.heading_1}
                      </Text>
                      <Text variant="bodyLg" as="p">
                        {content.Rank_collections.home.content_1}
                      </Text>
                      <Box paddingBlockStart="200">
                        <Button
                          size="large"
                          variant="primary"
                          onClick={handleGenerateClick}
                          disabled={isGeneratingBlocked}
                        >
                          {isGeneratingBlocked
                            ? "Generation in Progress..."
                            : "Generate Collections"}
                        </Button>
                      </Box>
                    </BlockStack>
                  </Grid.Cell>
                  <Grid.Cell columnSpan={{ xs: 6, sm: 2, md: 2, lg: 5, xl: 5 }}>
                    <Box
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "100%",
                      }}
                    >
                      <img
                        src="https://ik.imagekit.io/1tudtg11f/Rank%20(1).png?updatedAt=1746247899339"
                        alt="Collection optimization illustration"
                        style={{
                          maxWidth: "100%",
                          maxHeight: "220px",
                          objectFit: "contain",
                        }}
                      />
                    </Box>
                  </Grid.Cell>
                </Grid>
              </Box>
            </Card>
          </Layout.Section>

          {/* Metrics Section */}
          <Layout.Section>
            <Card>
              <Box padding="400">
                <BlockStack gap="400">
                  <Text variant="headingMd" as="h2" fontWeight="semibold">
                    Store Performance
                  </Text>
                  <Grid>
                    <Grid.Cell
                      columnSpan={{ xs: 6, sm: 2, md: 2, lg: 4, xl: 4 }}
                    >
                      <Box
                        padding="300"
                        background="bg-surface-secondary"
                        borderRadius="200"
                      >
                        <BlockStack gap="200" align="center">
                          <Icon source={HomeIcon} color="highlight" />
                          <Text
                            variant="headingLg"
                            as="p"
                            fontWeight="bold"
                            alignment="center"
                          >
                            {isPageLoading ? (
                              <Spinner size="small" />
                            ) : (
                              displayProductCount
                            )}
                          </Text>
                          <Text variant="bodySm" as="p" alignment="center">
                            {content.Rank_collections.home.heading_2}
                          </Text>
                        </BlockStack>
                      </Box>
                    </Grid.Cell>
                    <Grid.Cell
                      columnSpan={{ xs: 6, sm: 2, md: 2, lg: 4, xl: 4 }}
                    >
                      <Box
                        padding="300"
                        background="bg-surface-secondary"
                        borderRadius="200"
                      >
                        <BlockStack gap="200" align="center">
                          <Icon source={ClockIcon} color="success" />
                          <Text
                            variant="headingLg"
                            as="p"
                            fontWeight="bold"
                            alignment="center"
                          >
                            {isMetricsLoading ? (
                              <Spinner size="small" />
                            ) : (
                              opportunity || "N/A"
                            )}
                          </Text>
                          <Text variant="bodySm" as="p" alignment="center">
                            {content.Rank_collections.home.heading_3}
                          </Text>
                        </BlockStack>
                      </Box>
                    </Grid.Cell>
                    <Grid.Cell
                      columnSpan={{ xs: 6, sm: 2, md: 2, lg: 4, xl: 4 }}
                    >
                      <Box
                        padding="300"
                        background="bg-surface-secondary"
                        borderRadius="200"
                      >
                        <BlockStack gap="200" align="center">
                          <Icon source={WandIcon} color="warning" />
                          <Text
                            variant="headingLg"
                            as="p"
                            fontWeight="bold"
                            alignment="center"
                          >
                            {isMetricsLoading ? (
                              <Spinner size="small" />
                            ) : conversionRate ? (
                              `+${conversionRate}%`
                            ) : (
                              "N/A"
                            )}
                          </Text>
                          <Text variant="bodySm" as="p" alignment="center">
                            {content.Rank_collections.home.heading_4}
                          </Text>
                        </BlockStack>
                      </Box>
                    </Grid.Cell>
                  </Grid>
                </BlockStack>
              </Box>
            </Card>
          </Layout.Section>

          {/* Action Cards Section */}
          <Layout.Section>
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
                <Card>
                  <Box padding="400">
                    <BlockStack gap="400">
                      <InlineStack align="space-between" blockAlign="center">
                        <Text variant="headingMd" as="h2" fontWeight="semibold">
                          {content.Rank_collections.home.heading_5}
                        </Text>
                        <Badge tone={automaticUpdate ? "success" : "critical"}>
                          {automaticUpdate ? "Available" : "Upgrade Required"}
                        </Badge>
                      </InlineStack>
                      <Text variant="bodyMd" as="p">
                        {content.Rank_collections.home.content_2}
                      </Text>
                      <Box>
                        <Button
                          onClick={() =>
                            automaticUpdate
                              ? modalManager.openAutoScheduleModal("auto")
                              : modalManager.openUpgradeModal()
                          }
                          icon={ClockIcon}
                        >
                          {content.Rank_collections.home.button_1}
                        </Button>
                      </Box>
                    </BlockStack>
                  </Box>
                </Card>
              </Grid.Cell>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 6, xl: 6 }}>
                <Card>
                  <Box padding="400">
                    <BlockStack gap="400">
                      <Text variant="headingMd" as="h2" fontWeight="semibold">
                        {content.Rank_collections.home.heading_6}
                      </Text>
                      <Text variant="bodyMd" as="p">
                        {content.Rank_collections.home.content_3}
                      </Text>
                      <Box>
                        <Button
                          onClick={handleGenerateClick}
                          disabled={isGeneratingBlocked}
                          icon={WandIcon}
                          primary
                        >
                          {content.Rank_collections.home.button_2}
                        </Button>
                      </Box>
                    </BlockStack>
                  </Box>
                </Card>
              </Grid.Cell>
            </Grid>
          </Layout.Section>

          {/* Support and Resources Section */}
          <Layout.Section>
            <Grid>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 8, xl: 8 }}>
                <SupportBanner contactLink={ContactLink} />
              </Grid.Cell>
              <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 4, xl: 4 }}>
                <HelpResources featureFlags={flag} />
              </Grid.Cell>
            </Grid>
          </Layout.Section>
        </Layout>
      </Box>

      <Modal
        open={modalManager.isCreateModalOpen}
        onHide={modalManager.closeCreateModal}
      >
        <TitleBar
          title={content.Rank_collections.home.modal_heading_1}
        ></TitleBar>

        <Box style={{ padding: "var(--p-space-400)" }}>
          <BlockStack gap="400">
            <Card sectioned>
              <Box
                style={{
                  cursor: automaticUpdate ? "pointer" : "default",
                  opacity: automaticUpdate ? 1 : 0.6,
                }}
                onClick={() => {
                  if (!automaticUpdate) {
                    modalManager.openUpgradeModal();
                  } else {
                    modalManager.openAutoScheduleModal("auto");
                  }
                }}
              >
                <BlockStack gap="200">
                  <InlineStack align="space-between" blockAlign="center">
                    <InlineStack gap="200" blockAlign="center">
                      <Icon source={ClockIcon} />
                      <Text variant="headingMd" as="h6" fontWeight="semibold">
                        {content.Rank_collections.home.modal_heading_2}
                      </Text>
                    </InlineStack>
                    <Badge tone={automaticUpdate ? "attention" : "critical"}>
                      {automaticUpdate
                        ? content.Rank_collections.home.modal_badge_1
                        : "Upgrade Required"}
                    </Badge>
                  </InlineStack>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    {content.Rank_collections.home.modal_content_1}
                  </Text>
                </BlockStack>
              </Box>
            </Card>

            <Card sectioned>
              <Box
                style={{ cursor: "pointer" }}
                onClick={() => {
                  modalManager.closeCreateModal();
                  navigate("/app/generate-collections");
                }}
              >
                <BlockStack gap="200">
                  <InlineStack gap="200" blockAlign="center">
                    <Box>
                      <Icon source={WandIcon} />
                    </Box>
                    <Text variant="headingMd" as="h6" fontWeight="semibold">
                      {content.Rank_collections.home.modal_heading_3}
                    </Text>
                  </InlineStack>
                  <Text variant="bodyMd" as="p" tone="subdued">
                    {content.Rank_collections.home.modal_content_2}
                  </Text>
                </BlockStack>
              </Box>
            </Card>
          </BlockStack>
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "var(--p-color-bg-surface-secondary)",
            padding: "var(--p-space-400)",
            borderTop: "var(--p-border-divider)",
          }}
        ></Box>
      </Modal>

      {modalManager.isAutoScheduleModalOpen && (
        <AutoScheduleCollectionsModal
          open={modalManager.isAutoScheduleModalOpen}
          hide={modalManager.closeAutoScheduleModal}
          onUpdate={dashboardActions.handleConfigSchedule}
          loading={dashboardActions.isScheduleLoading}
          configs={scheduleConfigs}
          from={modalManager.autoScheduleSource}
          domain={shopDomain}
          manualCreatePayload={dashboardActions.handleManualGenerate}
          manualLoading={dashboardActions.isManualGenerateLoading}
          pageLoad={isPageLoading}
          scheduleTime={{}}
          syncUrl={dashboardActions.handleSyncCompetitorUrl}
          syncLoading={dashboardActions.isCompetitorSyncLoading}
          isUrlSynced={dashboardActions.isUrlSynced}
        />
      )}

      <Modal
        open={modalManager.isUpgradeModalOpen}
        onHide={modalManager.closeUpgradeModal}
      >
        <TitleBar title={content.Rank_collections.home.modal_heading_4}>
          <button onClick={() => navigate("/app/plans")} variant="primary">
            {content.Rank_collections.home.modal_button_1}
          </button>
        </TitleBar>
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyMd" as="p">
            {content.Rank_collections.home.modal_content_3}
          </Text>
        </Box>
      </Modal>

      <Modal
        open={modalManager.isInfoModalOpen}
        onHide={modalManager.closeInfoModal}
      >
        <TitleBar
          title={content.Rank_collections.home.modal_heading_5}
        ></TitleBar>
        <Box style={{ padding: "10px" }}>
          <Text variant="bodyLg" as="p" fontWeight="semibold">
            {content.Rank_collections.home.modal_content_5}
          </Text>
        </Box>
      </Modal>

      <Modal
        open={modalManager.isLimitReachedModalOpen}
        onHide={modalManager.closeLimitReachedModal}
      >
        <TitleBar></TitleBar>
        <Box style={{ padding: "10px" }}>
          <Text
            variant="headingMd"
            as="h6"
            fontWeight="semibold"
            alignment="center"
          >
            {content.Rank_collections.home.modal_content_6}
          </Text>
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            padding: "10px",
          }}
        ></Box>
      </Modal>

      <Modal
        open={modalManager.isProductSyncModalOpen}
        onHide={modalManager.closeProductSyncModal}
      >
        <TitleBar
          title={content.Rank_collections.home.modal_heading_6}
        ></TitleBar>
        <Box style={{ padding: "10px" }}>
          <Text
            variant="headingMd"
            as="h6"
            fontWeight="semibold"
            alignment="center"
          >
            {content.Rank_collections.home.modal_content_7}
          </Text>
        </Box>
      </Modal>
    </Page>
  );
}
