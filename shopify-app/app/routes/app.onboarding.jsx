import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { json } from "@remix-run/node";
import {
  useNavigate,
  useSubmit,
  useActionData,
  useLoaderData,
} from "@remix-run/react";
import {
  Page,
  Text,
  InlineStack,
  BlockStack,
  Card,
  Box,
  Button,
  Grid,
  Banner,
  Badge,
  Select,
  TextField,
} from "@shopify/polaris";
import { Modal, TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";
import { getProductDetails } from "../models/getProductDetails.server";
import { saveAccessToken } from "../models/saveAccessToken.server";
import { productsSyncToAgent } from "../models/syncProductsToAgent.server";
import { fetchOnboardingConfiguration } from "../models/onboardingStep.server";
import { pricingPlan } from "../models/planDetails.server";
import { collectionMetafieldCreate } from "../models/getProductDetails.server";
import {
  fetchCountries,
  fetchRegions,
} from "../models/fetchRegionsAndCountries.server";
import { fetchActiveCollections } from "../models/fetchActiveCollections.server";
import { saveStoreRegion } from "../models/saveStoreRegion.server";
import { syncCompetitorURL } from "../models/syncCompetitorURLToAgent.server";
import OwnSpinner from "../components/spinner";
import { usePageViewTracking, trackButtonClick } from "../helper/analytics";
import content from "../locales/en.json";

export const loader = async ({ request }) => {
  const { session } = await authenticate.admin(request);
  const shopName = session.shop;
  return json({ shopName });
};

export const action = async ({ request }) => {
  const { admin } = await authenticate.admin(request);
  const formData = await request.formData();
  const type = formData.get("type");

  if (type === "initialFetch") {
    const onboardConfigurationFetch = await fetchOnboardingConfiguration(admin);
    const actionData = await getProductDetails(admin);
    const collections = await fetchActiveCollections(admin);
    return json({
      status: actionData.status || onboardConfigurationFetch.status,
      message: actionData.message || onboardConfigurationFetch.message,
      data: {
        productData: actionData?.data,
        onboardData: onboardConfigurationFetch?.data,
      },
      type: type,
      collections: collections.data,
    });
  } else if (type === "store-connect") {
    const productsLength = formData.get("products-length");
    const metafield = await collectionMetafieldCreate(admin);
    const actionData = await saveAccessToken(admin, productsLength);
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type,
    });
  } else if (type === "products-sync") {
    const products = formData.get("products");
    const actionData = await productsSyncToAgent(admin, products);
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type,
    });
  } else if (type === "fetch-regions") {
    const actionData = await fetchRegions(admin);
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type,
    });
  } else if (type === "fetch-country") {
    const region = formData.get("region");
    const actionData = await fetchCountries(admin, region);
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type,
    });
  } else if (type === "sync-regions") {
    const storeType = formData.get("storeType");
    const storeRegion = formData.get("storeRegion");
    const storeCountry = formData.get("storeCountry");
    const actionData = await saveStoreRegion(
      admin,
      storeType,
      storeRegion,
      storeCountry,
    );
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type,
    });
  } else if (type === "sync-competitor-url") {
    const url = formData.get("url");
    const actionData = await syncCompetitorURL(admin, url);
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type,
    });
  } else if (type === "plan-update") {
    const planKey = formData.get("planName");
    const actionData = await pricingPlan(admin, planKey);
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type,
      plan: planKey,
    });
  }

  return json({
    status: 400,
    message: "Something went wrong",
    data: null,
  });
};

export default function Index() {
  const actionData = useActionData();
  const navigate = useNavigate();
  const submit = useSubmit();
  const { shopName } = useLoaderData();

  const [pageLoading, setPageLoading] = useState(true);
  const [productCount, setProductCount] = useState(0);
  const [collectionCount, setCollectionCount] = useState(0);
  const [productDetails, setProductDetails] = useState([]);
  const [isStoreConnected, setIsStoreConnected] = useState(false);
  const [isProductSynced, setIsProductSynced] = useState(false);
  const [opportunityLoading, setOpportunityLoading] = useState(false);
  const [storeConnectLoading, setStoreConnectLoading] = useState(false);
  const [opportunityModalOpen, setOpportunityModalOpen] = useState(true); // Set to true by default to always show modal
  const [pricingPlanOpen, setPricingPlanOpen] = useState(false);
  const [planLoading, setPlanLoading] = useState({});
  const [pageNavigateShow, setPageNavigateShow] = useState(false);
  const [regions, setRegions] = useState([]);
  const [countries, setCountries] = useState([]);
  const [regionLoading, setRegionLoading] = useState(false);
  const [storeCategory, setStoreCategory] = useState("");
  const [aboutStore, setAboutStore] = useState(false);
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedCountries, setSelectedCountries] = useState("");
  const [regionScreen, setRegionScreen] = useState(false);
  const [regionSyncLoading, setRegionSyncLoading] = useState(false);
  const [competitorUrl, setCompetitorUrl] = useState("");
  const [enteredCompetitorUrl, setEnteredCompetitorUrl] = useState([]);
  const [syncCompetitorUrlLoading, setSyncCompetitorUrlLoading] =
    useState(false);
  const [competitorScreen, setCompetitorScreen] = useState(false);
  const [numberOfProducts, setNumberOfProducts] = useState(0);
  const [countryFetchLoading, setCountryFetchLoading] = useState(false);
  const [showIntroCard, setShowIntroCard] = useState(true); // State for controlling the intro card visibility
  const [isModalOpen, setIsModalOpen] = useState(true); // State to control modal visibility

  const storeOptions = content.Rank_collections.onboarding.store_options.map(
    (options) => ({
      label: options.label,
      value: options.value,
    }),
  );

  const regionsOptions = regions.map((regions) => ({
    label: regions,
    value: regions,
  }));

  const countryOptions = countries.map((country) => ({
    label: country.name,
    value: country.name,
  }));

  const handleStoreCategoryChange = useCallback(
    (value) => setStoreCategory(value),
    [],
  );
  const handleChangeRegion = useCallback((value) => {
    setSelectedRegion(value);
    const formData = new FormData();
    setCountryFetchLoading(true);
    formData.append("type", "fetch-country");
    formData.append("region", value);
    submit(formData, { method: "post" });
    trackButtonClick("Fetching Country", "Onboarding", {
      shop_name: shopName,
    });
  }, []);
  const handleChangeCountry = useCallback(
    (value) => setSelectedCountries(value),
    [],
  );
  const handleCompetitorURLChange = useCallback(
    (value) => setCompetitorUrl(value),
    [],
  );

  const formatURL = (url) => {
    let formattedURL =
      url.startsWith("http://") || url.startsWith("https://")
        ? url
        : `https://${url}`;
    // Remove trailing '/' if present
    return formattedURL.endsWith("/")
      ? formattedURL.slice(0, -1)
      : formattedURL;
  };

  const handleOpenOpportunityModal = () => {
    setShowIntroCard(false); // Hide the intro card
    setOpportunityLoading(isProductSynced || isStoreConnected ? false : true);
    setStoreConnectLoading(true);

    const formData = new FormData();
    formData.append("type", "store-connect");

    if (!isStoreConnected) {
      formData.append("products-length", productDetails.length);
      submit(formData, { method: "post" });
      trackButtonClick("Store Connection", "Onboarding", {
        shop_name: shopName,
      });
    } else if (isStoreConnected && !isProductSynced) {
      formData.append("type", "products-sync");
      formData.append("products", JSON.stringify(productDetails));
      submit(formData, { method: "post" });
      trackButtonClick("Products Sync", "Onboarding", {
        shop_name: shopName,
      });
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false); // Close the modal
  };

  const handleOpenModal = () => {
    setIsModalOpen(true); // Open the modal
  };

  const handlePricingPlans = (planKey) => {
    setPlanLoading((prev) => ({
      ...prev,
      [planKey]: true,
    }));
    const formData = new FormData();
    formData.append("type", "plan-update");
    formData.append("planName", planKey);
    submit(formData, { method: "post" });
    trackButtonClick("Pricing Plan", "Onboarding", {
      shop_name: shopName,
    });
  };

  const handleFetchRegions = () => {
    setRegionLoading(true);
    const formData = new FormData();
    formData.append("type", "fetch-regions");
    submit(formData, { method: "post" });
    trackButtonClick("Fetching Regions", "Onboarding", {
      shop_name: shopName,
    });
  };

  const handleRegionScreenNavigate = () => {
    if (!storeCategory) {
      shopify.toast.show(content.Rank_collections.onboarding.toast_1, {
        isError: true,
      });
      return;
    }

    if (!selectedRegion) {
      shopify.toast.show(content.Rank_collections.onboarding.toast_2, {
        isError: true,
      });
      return;
    }

    if (!selectedCountries || selectedCountries.length === 0) {
      shopify.toast.show(content.Rank_collections.onboarding.toast_3, {
        isError: true,
      });
      return;
    }

    setRegionSyncLoading(true);
    const formData = new FormData();
    formData.append("type", "sync-regions");
    formData.append("storeType", storeCategory);
    formData.append("storeRegion", selectedRegion);
    formData.append("storeCountry", selectedCountries);
    submit(formData, { method: "post" });

    trackButtonClick("Syncing Regions", "Onboarding", {
      shop_name: shopName,
    });
  };

  const handleAddCompetitorURL = () => {
    if (competitorUrl.trim() !== "") {
      setEnteredCompetitorUrl((prevUrls) => [
        ...prevUrls,
        formatURL(competitorUrl),
      ]);
      setCompetitorUrl("");
    }
  };

  const handleRemoveCompetitorURL = (index) => {
    setEnteredCompetitorUrl((prevUrls) =>
      prevUrls.filter((_, i) => i !== index),
    );
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      handleAddCompetitorURL();
    }
  };

  const handleSyncCompetitorUrl = () => {
    if (enteredCompetitorUrl.length === 0) {
      shopify.toast.show("Please enter some competitor url", { isError: true });
      return;
    }
    setSyncCompetitorUrlLoading(true);
    const formData = new FormData();
    formData.append("type", "sync-competitor-url");
    formData.append("url", JSON.stringify(enteredCompetitorUrl));
    submit(formData, { method: "post" });
    trackButtonClick("Syncing Competitor URL", "Onboarding", {
      shop_name: shopName,
    });
  };

  const handleSkipCompetitorUrl = () => {
    setCompetitorScreen(true);
    setPricingPlanOpen(true);
  };

  const pageNavigate = () => {
    navigate("/app/home");
  };

  useEffect(() => {
    setPageLoading(true);
    const formData = new FormData();
    formData.append("type", "initialFetch");
    submit(formData, { method: "post" });
    trackButtonClick("Fetching Initial Data", "Onboarding", {
      shop_name: shopName,
    });
  }, []);

  useEffect(() => {
    if (actionData?.type === "initialFetch" && actionData?.status === 200) {
      setPageLoading(false);
      setProductDetails(actionData?.data?.productData);
      setIsStoreConnected(actionData?.data?.onboardData?.storeConnected);
      setIsProductSynced(actionData?.data?.onboardData?.productSynced);
      setProductCount(
        actionData?.data?.productData?.length > 0
          ? actionData?.data?.productData?.length
          : actionData?.data?.onboardData?.productCount,
      );
      setNumberOfProducts(actionData?.data?.productData?.length);
      setCollectionCount(actionData?.collections);
    } else if (
      actionData?.type === "initialFetch" &&
      actionData?.status !== 200
    ) {
      setPageLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.type === "store-connect" && actionData?.status === 200) {
      setStoreConnectLoading(false);
      setIsStoreConnected(actionData?.data?.storeConnected);
      setProductCount(actionData?.data?.productsCount);
      const formData = new FormData();
      formData.append("type", "products-sync");
      formData.append("products", JSON.stringify(productDetails));
      submit(formData, { method: "post" });
    } else if (
      actionData?.type === "store-connect" &&
      actionData?.status !== 200
    ) {
      setStoreConnectLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.type === "products-sync" && actionData?.status === 200) {
      setIsProductSynced(actionData?.data?.isProductSynced);
      localStorage.setItem("product_sync_task_id", actionData?.data?.task_id);
      localStorage.setItem("productCount", numberOfProducts);
      setOpportunityLoading(false);
    } else if (
      actionData?.type === "products-sync" &&
      actionData?.status !== 200
    ) {
      setOpportunityLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.type === "plan-update" && actionData?.status === 200) {
      if (actionData?.plan === "freePlan") {
        navigate("/app/home");
        setPlanLoading((prev) => ({ ...prev, [actionData?.plan]: false }));
        return;
      } else if (actionData?.plan !== "freePlan") {
        window.parent.location.href = actionData?.data;
        setPlanLoading((prev) => ({ ...prev, [actionData?.plan]: false }));
      }
    } else if (
      actionData?.type === "plan-update" &&
      actionData?.status !== 200
    ) {
      setPlanLoading((prev) => ({ ...prev, [actionData?.plan]: false }));
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.type === "fetch-regions" && actionData?.status === 200) {
      setRegions(actionData?.data);
      setAboutStore(true);
      setRegionLoading(false);
    } else if (
      actionData?.type === "fetch-regions" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setRegionLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.type === "fetch-country" && actionData?.status === 200) {
      setCountryFetchLoading(false);
      setCountries(actionData?.data);
    } else if (
      actionData?.type === "fetch-country" &&
      actionData?.status !== 200
    ) {
      setCountryFetchLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData]);

  useEffect(() => {
    if (actionData?.type === "sync-regions" && actionData?.status === 200) {
      setStoreCategory(actionData?.data?.storeType);
      setSelectedRegion(actionData?.data?.storeRegion);
      setSelectedCountries(actionData?.data?.storeCountry);
      setRegionSyncLoading(false);
      setRegionScreen(true);
    } else if (
      actionData?.type === "sync-regions" &&
      actionData?.status !== 200
    ) {
      shopify.toast.show(actionData?.message, { isError: true });
      setRegionSyncLoading(false);
    }
  }, [actionData]);

  useEffect(() => {
    if (
      actionData?.type === "sync-competitor-url" &&
      actionData?.status === 200
    ) {
      setSyncCompetitorUrlLoading(false);
      setCompetitorScreen(true);
      setPricingPlanOpen(true);
    } else if (
      actionData?.type === "sync-competitor-url" &&
      actionData?.status !== 200
    ) {
      setSyncCompetitorUrlLoading(false);
      shopify.toast.show(actionData?.message, { isError: true });
    }
  }, [actionData]);

  usePageViewTracking("onboarding", {
    shop_name: shopName,
  });

  if (pageLoading) {
    return <OwnSpinner />;
  }

  return (
    <Page>
      {!isModalOpen && (
        <Box style={{ height: '100vh', width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <BlockStack gap="400">
            <Text variant="headingLg" as="h5" fontWeight="bold">
              {content.Rank_collections.onboarding.main_heading}
            </Text>
            <Button variant="primary" onClick={handleOpenModal}>
              {content.Rank_collections.onboarding.complete_button}
            </Button>
          </BlockStack>
        </Box>
      )}
      <Modal open={isModalOpen} onHide={handleCloseModal}>
        <TitleBar
          title={
            showIntroCard
              ? content.Rank_collections.onboarding.heading_4
              : !aboutStore && !pricingPlanOpen
                ? content.Rank_collections.onboarding.heading_16
                : !regionScreen && !pricingPlanOpen && aboutStore
                  ? content.Rank_collections.onboarding.heading_17
                  : !competitorScreen &&
                    !pricingPlanOpen &&
                    aboutStore &&
                    regionScreen
                    ? content.Rank_collections.onboarding.heading_18
                    : pricingPlanOpen
                      ? content.Rank_collections.onboarding.heading_19
                      : content.Rank_collections.onboarding.heading_4
          }
        ></TitleBar>
        <Box style={{ padding: "1rem" }}>
          {showIntroCard ? (
            <Grid>
              {/* Left Side with Image */}
              <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 3, xl: 3 }}>
                <Box padding="400">
                  <img
                    src="https://ik.imagekit.io/1tudtg11f/Rank%20(1).png?updatedAt=1746247899339"
                    alt="onboarding illustration"
                    style={{
                      width: "100%",
                      height: "500px",
                      objectFit: "cover",
                      borderRadius: "12px",
                    }}
                  />
                </Box>
              </Grid.Cell>

              {/* Right Side with Text */}
              <Grid.Cell columnSpan={{ xs: 3, sm: 3, md: 3, lg: 3, xl: 3 }}>
                <Box
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    height: "100%",
                    gap: "10px",
                    padding: "1.2rem",
                  }}
                >
                  <BlockStack gap="300" alignment="center">
                    <Text variant="headingLg" as="h5" fontWeight="bold">
                      {content.Rank_collections.onboarding.heading_14} 👋
                    </Text>
                    <Text as="p" variant="bodyMd" fontweight="regular">
                      {content.Rank_collections.onboarding.content_14}
                    </Text>
                    <Text as="p" variant="bodyMd" fontweight="regular">
                      {content.Rank_collections.onboarding.content_16}
                    </Text>
                  </BlockStack>
                  <Box style={{ marginTop: "auto" }}>
                    <InlineStack align="end" blockAlign="end">
                      <Button
                        variant="primary"
                        onClick={handleOpenOpportunityModal}
                      >
                        {content.Rank_collections.onboarding.button_6}
                      </Button>
                    </InlineStack>
                  </Box>
                </Box>
              </Grid.Cell>
            </Grid>
          ) : opportunityLoading ? (
            <BlockStack gap="400">
              <Card sectioned background="rgba(128, 128, 128, 0.1)">
                <InlineStack gap="300" blockAlign="center">
                  <Text variant="headingLg" as="h5" fontWeight="bold">
                    {content.Rank_collections.onboarding.heading_5}
                  </Text>
                  <Text variant="headingLg" as="h5" fontWeight="semibold">
                    &#128075;
                  </Text>
                </InlineStack>
              </Card>
              <Card sectioned>
                <BlockStack gap="300">
                  <Text variant="headingMd" as="h6">
                    {content.Rank_collections.onboarding.heading_6}
                  </Text>
                  <Text variant="bodyMd" as="p" fontWeight="subdued">
                    {content.Rank_collections.onboarding.content_1}
                  </Text>
                  <Box
                    style={{
                      margin: "0 auto",
                      width: "100px",
                      height: "100px",
                      borderRadius: "50%",
                      border: "2 px solid transparent",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                  >
                    <Text
                      variant="headingLg"
                      as="h6"
                      fontWeight="semibold"
                      tone="magic"
                    >
                      {isProductSynced ? 70 : isStoreConnected ? 30 : 0} %
                    </Text>
                  </Box>
                  <Banner tone={!isStoreConnected ? "info" : "success"}>
                    <Text variant="bodyLg" as="p">
                      {!isStoreConnected
                        ? content.Rank_collections.onboarding.content_2
                        : content.Rank_collections.onboarding.content_3}
                    </Text>
                  </Banner>
                  <Banner tone={!isProductSynced ? "info" : "success"}>
                    <Text variant="bodyLg" as="p">
                      {!isProductSynced
                        ? content.Rank_collections.onboarding.content_4
                        : content.Rank_collections.onboarding.content_5}
                    </Text>
                  </Banner>
                  <Banner tone="info" hideIcon={true}>
                    <InlineStack gap="300" blockAlign="center">
                      <Text variant="bodyLg" as="h5" fontWeight="semibold">
                        &#10227;
                      </Text>
                      <Text variant="bodyLg" as="p">
                        {content.Rank_collections.onboarding.content_6}
                      </Text>
                    </InlineStack>
                  </Banner>
                </BlockStack>
              </Card>
            </BlockStack>
          ) : (
            <>
              {!aboutStore && !pricingPlanOpen ? (
                <BlockStack gap="400">
                  <Card sectioned background="rgba(128, 128, 128, 0.1)">
                    <InlineStack gap="300" blockAlign="center">
                      <Text variant="headingLg" as="h5" fontweight="bold">
                        {content.Rank_collections.onboarding.heading_7}
                      </Text>
                      <Text variant="headingLg" as="h6" fontWeight="semibold">
                        &#x1F389;
                      </Text>
                    </InlineStack>
                  </Card>

                  <Card sectioned>
                    <BlockStack gap="500">
                      <Grid>
                        <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
                          <Card sectioned>
                            <BlockStack gap="300">
                              <Text
                                variant="headingMd"
                                as="h6"
                                fontWeight="subdued"
                              >
                                {content.Rank_collections.onboarding.content_7}
                              </Text>
                              <Text
                                variant="headingLg"
                                as="h5"
                                fontWeight="bold"
                              >
                                {productCount || numberOfProducts}{" "}
                              </Text>
                            </BlockStack>
                          </Card>
                        </Grid.Cell>
                        <Grid.Cell columnSpan={{ xs: 6, sm: 3, md: 3, lg: 3 }}>
                          <Card sectioned>
                            <BlockStack gap="300">
                              <Text
                                variant="headingMd"
                                as="h6"
                                fontWeight="subdued"
                              >
                                {content.Rank_collections.onboarding.content_8}
                              </Text>
                              <Text
                                variant="headingLg"
                                as="h5"
                                fontWeight="bold"
                              >
                                {collectionCount}{" "}
                              </Text>
                            </BlockStack>
                          </Card>
                        </Grid.Cell>
                      </Grid>

                      <Card sectioned>
                        <BlockStack gap="300">
                          <Text
                            variant="headingMd"
                            as="h6"
                            fontWeight="semibold"
                          >
                            {content.Rank_collections.onboarding.heading_8}
                          </Text>

                          <Card sectioned background="rgba(128, 128, 128, 0.1)">
                            <InlineStack
                              align="space-between"
                              blockAlign="center"
                            >
                              <Text
                                variant="bodyMd"
                                as="p"
                                fontWeight="regular"
                              >
                                {content.Rank_collections.onboarding.content_9}
                              </Text>
                              <Button
                                variant="primary"
                                onClick={handleFetchRegions}
                                loading={regionLoading}
                              >
                                {content.Rank_collections.onboarding.button_2}
                              </Button>
                            </InlineStack>
                          </Card>

                          <Card sectioned background="rgba(128, 128, 128, 0.1)">
                            <InlineStack
                              align="space-between"
                              blockAlign="center"
                            >
                              <Text
                                variant="bodyMd"
                                as="p"
                                fontWeight="regular"
                              >
                                {content.Rank_collections.onboarding.content_10}
                              </Text>
                              <Badge tone="info" size="large">
                                {content.Rank_collections.onboarding.badge_1}
                              </Badge>
                            </InlineStack>
                          </Card>

                          <Card sectioned background="rgba(128, 128, 128, 0.1)">
                            <InlineStack
                              align="space-between"
                              blockAlign="center"
                            >
                              <Text
                                variant="bodyMd"
                                as="p"
                                fontWeight="regular"
                              >
                                {content.Rank_collections.onboarding.content_11}
                              </Text>
                              <Badge tone="info" size="large">
                                {content.Rank_collections.onboarding.badge_2}
                              </Badge>
                            </InlineStack>
                          </Card>
                        </BlockStack>
                      </Card>
                    </BlockStack>
                  </Card>
                  {/* <Button
                    variant="primary"
                    size="large"
                    onClick={handleFetchRegions}
                    loading={regionLoading}
                  >
                    {content.Rank_collections.onboarding.button_3}
                  </Button> */}
                </BlockStack>
              ) : (
                !regionScreen &&
                !pricingPlanOpen && (
                  <BlockStack gap="600">
                    <Card sectioned>
                      <BlockStack gap="600">
                        <BlockStack gap="200">
                          <Text
                            variant="headingLg"
                            as="h6"
                            fontWeight="semibold"
                          >
                            {content.Rank_collections.onboarding.heading_9}
                          </Text>
                          <Text variant="bodyLg" as="p" fontWeight="subdued">
                            {content.Rank_collections.onboarding.content_12}
                          </Text>
                        </BlockStack>
                        <Select
                          label={content.Rank_collections.onboarding.heading_10}
                          options={storeOptions}
                          onChange={handleStoreCategoryChange}
                          value={storeCategory}
                          placeholder={
                            content.Rank_collections.onboarding.placholder_1
                          }
                        />
                      </BlockStack>
                    </Card>
                    <Card sectioned>
                      <BlockStack gap="500">
                        <Text variant="headingLg" as="h6" fontWeight="semibold">
                          {content.Rank_collections.onboarding.heading_11}
                        </Text>
                        <Text variant="bodyLg" as="p" fontWeight="subdued">
                          {content.Rank_collections.onboarding.content_13}
                        </Text>
                        <Select
                          label={content.Rank_collections.onboarding.heading_12}
                          placeholder={
                            content.Rank_collections.onboarding.placeholder_2
                          }
                          options={regionsOptions}
                          onChange={handleChangeRegion}
                          value={selectedRegion}
                        />
                        <Select
                          label={content.Rank_collections.onboarding.heading_13}
                          placeholder={
                            content.Rank_collections.onboarding.placholder_3
                          }
                          disabled={countryFetchLoading}
                          options={countryOptions}
                          onChange={handleChangeCountry}
                          value={selectedCountries}
                        />
                        <InlineStack align="space-between" blockAlign="center">
                          <Button onClick={() => setAboutStore(false)}>
                            {content.Rank_collections.onboarding.button_4}
                          </Button>
                          <Button
                            loading={regionSyncLoading}
                            variant="primary"
                            onClick={handleRegionScreenNavigate}
                          >
                            {content.Rank_collections.onboarding.button_5}
                          </Button>
                        </InlineStack>
                        <Banner tone="info">
                          {content.Rank_collections.onboarding.content_info}
                        </Banner>
                      </BlockStack>
                    </Card>
                  </BlockStack>
                )
              )}

              {!competitorScreen &&
                !pricingPlanOpen &&
                aboutStore &&
                regionScreen && (
                  <Box
                    style={{
                      height: "500px",
                      display: "flex",
                      flexDirection: "column",
                      overflowY: "scroll",
                      scrollbarWidth: "thin",
                    }}
                  >
                    <Box
                      style={{
                        display: "flex",
                        flexDirection: "column",
                        gap: "1rem",
                        height: "100%",
                      }}
                    >
                      <Card sectioned>
                        <BlockStack gap="300">
                          <Banner tone="info">
                            {content.Rank_collections.onboarding.content_15}
                          </Banner>
                          <InlineStack gap="100" blockAlign="end">
                            <Box style={{ flex: 1 }} onKeyDown={handleKeyDown}>
                              <TextField
                                label={
                                  content.Rank_collections.onboarding.heading_15
                                }
                                placeholder={
                                  content.Rank_collections.onboarding
                                    .placeholder_1
                                }
                                value={competitorUrl}
                                onChange={handleCompetitorURLChange}
                              />
                            </Box>
                            <Box>
                              <Button
                                variant="primary"
                                onClick={handleAddCompetitorURL}
                              >
                                {content.Rank_collections.onboarding.button_7}
                              </Button>
                            </Box>
                          </InlineStack>
                          <InlineStack
                            align="start"
                            gap="100"
                            blockAlign="center"
                          >
                            {enteredCompetitorUrl.length > 0 &&
                              enteredCompetitorUrl.map((url, index) => (
                                <BlockStack gap="200" key={index}>
                                  <Badge tone="base">
                                    <InlineStack gap="200" blockAlign="center">
                                      <Text variant="bodyMd" as="span">
                                        {url}
                                      </Text>
                                      <Button
                                        variant="plain"
                                        tone="critical"
                                        onClick={() =>
                                          handleRemoveCompetitorURL(index)
                                        }
                                      >
                                        {
                                          content.Rank_collections.onboarding
                                            .button_8
                                        }
                                      </Button>
                                    </InlineStack>
                                  </Badge>
                                </BlockStack>
                              ))}
                          </InlineStack>
                        </BlockStack>
                      </Card>
                    </Box>

                    <Box
                      style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "end",
                        gap: "1rem",
                      }}
                    >
                      <Button
                        variant="primary"
                        loading={syncCompetitorUrlLoading}
                        onClick={handleSyncCompetitorUrl}
                      >
                        {content.Rank_collections.onboarding.button_9}
                      </Button>
                      <Button
                        variant="primary"
                        tone="critical"
                        onClick={handleSkipCompetitorUrl}
                      >
                        {content.Rank_collections.onboarding.button_10}
                      </Button>
                    </Box>
                  </Box>
                )}

              {pricingPlanOpen && (
                <BlockStack gap="400">
                  <Card sectioned background="rgba(128, 128, 128, 0.1">
                    <InlineStack gap="300" blockAlign="center">
                      <Text variant="headingLg" as="h5" fontWeight="bold">
                        {content.Rank_collections.plans_page.heading}
                      </Text>
                      <Text variant="headingLg" as="h6" fontWeight="semibold">
                        &#128640;
                      </Text>
                    </InlineStack>
                  </Card>

                  {content.Rank_collections.plans_page.planDetails
                    .filter(
                      (plan) =>
                        plan.planKey === "freePlan" ||
                        plan.planKey === "automatic",
                      // (productCount > planProductCount &&
                      //   plan.planKey === "advancedPlan") ||
                      // (productCount <= planProductCount &&
                      //   plan.planKey === "growthPlan"),
                    )
                    .map((plan, index) => (
                      <Card key={index}>
                        <BlockStack gap="300">
                          <Text
                            variant="headingLg"
                            as="h6"
                            fontWeight="bold"
                            tone={plan.planKey !== "freePlan" ? "magic" : null}
                          >
                            {plan.planName}
                          </Text>
                          <Text
                            variant="bodyLg"
                            as="p"
                            tone={plan.planKey !== "freePlan" ? "magic" : null}
                          >
                            {plan.planDescription}
                          </Text>
                          <BlockStack gap="200">
                            {plan.features.map((feature, index) => (
                              <InlineStack
                                key={index}
                                gap="300"
                                blockAlign="center"
                              >
                                <Text
                                  variant="bodyLg"
                                  as="p"
                                  tone={
                                    plan.planKey !== "freePlan" ? "magic" : null
                                  }
                                >
                                  &#10004;
                                </Text>
                                <Text
                                  variant="bodyLg"
                                  as="p"
                                  tone={
                                    plan.planKey !== "freePlan" ? "magic" : null
                                  }
                                >
                                  {feature}
                                </Text>
                              </InlineStack>
                            ))}
                          </BlockStack>
                        </BlockStack>
                        <InlineStack align="end" blockAlign="center">
                          <Button
                            variant="primary"
                            onClick={() => handlePricingPlans(plan.planKey)}
                            loading={!!planLoading[plan.planKey]} // Ensure loading is a boolean
                            disabled={
                              !!planLoading["freePlan"] ||
                              // !!planLoading["advancedPlan"] ||
                              // !!planLoading["growthPlan"] ||
                              !!planLoading["automatic"] ||
                              pageNavigateShow
                            } // Convert all loading states to boolean
                          >
                            {plan.buttonLabel}
                          </Button>
                        </InlineStack>
                      </Card>
                    ))}
                </BlockStack>
              )}
            </>
          )}
        </Box>
        <Box
          style={{
            height: "3rem",
            backgroundColor: "rgba(128, 128, 128, 0.1)",
            padding: "10px",
          }}
        >
          {pageNavigateShow && (
            <InlineStack align="end" blockAlign="center">
              <Button onClick={pageNavigate} variant="primary">
                All set
              </Button>
            </InlineStack>
          )}
        </Box>
      </Modal>
    </Page>
  );
}
