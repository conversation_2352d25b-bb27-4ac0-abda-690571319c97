import { authenticate } from "../shopify.server";
import { Page, Box, Spinner } from "@shopify/polaris";
import { useEffect, useState } from "react";
import { useSubmit, redirect } from "@remix-run/react";

import { fetchOnboardingConfiguration } from "../models/onboardingStep.server";

export const action = async ({ request }) => {
  try {
    const { admin } = await authenticate.admin(request);
    if (!admin) {
      throw new Error("Session not initialized yet");
    }
    const formData = await request.formData();
    const type = formData.get("type");

    if (type === "initialFetch") {
      const actionData = await fetchOnboardingConfiguration(admin);
      if (!actionData?.data) {
        throw new Error("Failed to fetch onboarding data");
      }
      const isValidRoute =
        !actionData.data.onboardStatus && actionData.data.paymentId === null;
      return redirect(isValidRoute ? "/app/onboarding" : "/app/home");
    }
  } catch (error) {
    console.error("Error in action:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
};

export default function Index() {
  const submit = useSubmit();

  const [pageLoading, setPageLoading] = useState(false);

  useEffect(() => {
    setPageLoading(true);
    const formData = new FormData();
    formData.append("type", "initialFetch");
    submit(formData, { method: "post" });
  }, []);

  return (
    <Page fullWidth>
      {pageLoading && (
        <Box
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            width: "100vw",
            height: "100vh",
          }}
        >
          <Spinner size="large"></Spinner>
        </Box>
      )}
    </Page>
  );
}
