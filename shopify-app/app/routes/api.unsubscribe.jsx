import {json} from "@remix-run/node";
import {useActionData, useSubmit, useLocation} from '@remix-run/react';
import {useState, useEffect} from 'react';
import {mailUnsubscribe} from '../models/mailUnsubscribe.server';

export const action = async ({request}) => {
  const formData = await request.formData();
  const type = formData.get('type');
  if(type === 'unsubscribe'){
    const source = formData.get('source');
    const shopName = formData.get('shopName'); // Changed storeName to shopName
    const actionData = await mailUnsubscribe(shopName, source);
    return json({
      status: actionData.status,
      message: actionData.message,
      data: actionData.data,
      type: type
    });
  }
};

export default function Unsubscribe() {
  const actionData = useActionData();
  const submit = useSubmit();
  const [source, setSource] = useState('');
  const [shopName, setShopName] = useState(''); // Changed storeName to shopName
  const [unsubscribeLoading, setUnsubscribeLoading] = useState(false);

  // Get URL parameters on client-side only
  useEffect(() => {
    // This runs only on the client side
    if (typeof window !== "undefined") {
      const url = new URL(window.location.href);
      const params = url.searchParams;

      const store = params.get("store_name") || "";
      const src = params.get("source") || "";

      setShopName(store); // Changed setStoreName to setShopName
      setSource(src);
    }
  }, []);

  // Reset loading state when action completes
  useEffect(() => {
    if (actionData) {
      setUnsubscribeLoading(false);
      if (actionData.status === 200) {
        
      }
    }
  }, [actionData]);

  const handleUnsubscribe = () => {
    setUnsubscribeLoading(true);
    const formData = new FormData();
    formData.append('type', 'unsubscribe');
    formData.append('source', source);
    formData.append('shopName', shopName); // Changed storeName to shopName
    submit(formData, {method: 'post'});
    
  };

  return (
    <div style={styles.page}>
      <div style={styles.card}>
        <h1 style={styles.title}>We're sorry to see you go</h1>
        <p style={styles.text}>Click the button below to unsubscribe from future reports.</p>
        <button 
          onClick={handleUnsubscribe} 
          disabled={unsubscribeLoading || !source || !shopName} // Added checks for required fields
          style={styles.button}
        >
          {unsubscribeLoading ? 'Unsubscribing...' : 'Unsubscribe'}
        </button>
        
        {actionData && (
          <p style={actionData.status === 200 ? styles.successMessage : styles.errorMessage}>
            {actionData.message}
          </p>
        )}
      </div>
    </div>
  );
}

const styles = {
    page: {
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      background: '#f4f6f8',
    },
    card: {
      background: '#fff',
      padding: '40px',
      borderRadius: '12px',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
      textAlign: 'center',
      width: '100%',
      maxWidth: '400px',
    },
    title: {
      marginBottom: '16px',
      fontSize: '24px',
      fontWeight: '600',
      color: '#202223',
    },
    text: {
      marginBottom: '24px',
      color: '#6d7175',
      fontSize: '16px',
    },
    button: {
      background: '#bf0711',
      color: 'white',
      border: 'none',
      padding: '12px 20px',
      borderRadius: '8px',
      fontSize: '16px',
      cursor: 'pointer',
      transition: 'background 0.2s',
    },
    successMessage: {
      marginTop: '16px',
      color: '#008060',
      fontSize: '14px',
    },
    errorMessage: {
      marginTop: '16px',
      color: '#d82c0d',
      fontSize: '14px',
    }
  };