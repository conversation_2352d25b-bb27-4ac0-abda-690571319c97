// helpers/formatProduct.js

export const formatProductForAgent = async (payload, shop) => {
    if (!payload || typeof payload !== 'object') return [];
  
    return [
      {
        id: payload.admin_graphql_api_id,
        title: payload.title,
        handle: payload.handle,
        totalInventory: payload.variants.reduce(
          (acc, variant) => acc + (variant.inventory_quantity || 0),
          0
        ),
        tags:
          payload.tags && payload.tags.trim() !== ""
            ? payload.tags.split(",").map((tag) => tag.trim())
            : [],
        vendor: payload.vendor,
        description: payload.body_html
          ? payload.body_html.replace(/<\/?p>/g, "")
          : "",
        createdAt: payload.created_at,
        variants: Array.isArray(payload.variants)
          ? payload.variants.map((variant) => ({
              cursor: null,
              node: {
                id: variant.admin_graphql_api_id,
                title: variant.title,
                price: variant.price,
                position: variant.position,
                displayName: variant.title,
              },
            }))
          : [],
        product_url: `https://${shop}/products/${payload.handle}`,
        image_url:
          payload.images && payload.images.length > 0
            ? payload.images[0].src
            : null,
         
      },
    ];
  };
  