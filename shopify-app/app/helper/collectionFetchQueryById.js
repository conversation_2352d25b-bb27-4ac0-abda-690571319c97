export const collectionFetchById = async (admin, collectionId) => {
    
    
    const collectionQuery = await admin.graphql(`
      {
        collection(id: "${collectionId}") {
          id
          title
          handle
          products(first: 250) {
            edges {
              node {
                id
                title
                totalInventory
                priceRangeV2{
                  minVariantPrice{
                    amount
                  }
                }
                media(first: 1) {
                  edges {
                    node {
                      preview {
                        image {
                          url
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    `);

    const shopifyData = await collectionQuery.json();

    if (shopifyData.data && shopifyData.data.collection) {
        const products = shopifyData.data.collection.products.edges.map((edge) => ({
            id: edge.node.id,
            title: edge.node.title,
            totalInventory: edge.node.totalInventory,
            price: edge?.node?.priceRangeV2?.minVariantPrice?.amount,
            imageUrl: edge.node.media.edges.length > 0 ? edge.node.media.edges[0].node.preview.image.url : null,
        }));

        return {
            collectionId,
            title: shopifyData.data.collection.title,
            handle: shopifyData.data.collection.handle,
            products,
            image: products.length > 0 && products[0].imageUrl ? products[0].imageUrl : null, // First product's image URL
        };
    } else {
        console.error(
            `Failed to fetch details for collection ID: ${collectionId}`,
            shopifyData.errors || "Unknown error"
        );
        return null;
    }
};
