import moment from "moment";

export const formatDate = (dateString) => {
  const date = new Date(dateString);
  const options = { year: "numeric", month: "short", day: "numeric" };
  return new Intl.DateTimeFormat("en-US", options).format(date);
};

export const formatTime = (dateString) => {
  const date = new Date(dateString);
  const options = { hour: "numeric", minute: "numeric", hour12: true };
  return new Intl.DateTimeFormat("en-US", options).format(date);
};

export const humanReadableTime = (time) => {
  return moment(time).fromNow();
};

export const addDaysToCurrentTime = (time, days) => {
  return moment
    .utc(time)
    .add(days, "days")
    .format("YYYY-MM-DDTHH:mm:ss[Z]");
};
