import { collectionGID} from "../configs/config";
import { isCollectionAvailable } from "./checkCollectionAvailable";
import pino from 'pino';
const logger = pino();

export const collectionsDelete = async (admin, id) => {
    
    
        const collectionDeleteFromShopify = await admin.graphql(`
            mutation collectionDelete($input: CollectionDeleteInput!){
                collectionDelete(input: $input){
                    deletedCollectionId
                    userErrors{
                        field
                        message
                    }
                }
            }
        `, {
            variables: {
                input: {
                    id: id
                }
            }
        })
    const responseDelete = await collectionDeleteFromShopify.json();
    const responseDeleteError = responseDelete?.data?.collectionDelete?.userErrors;
    return responseDeleteError
    
}