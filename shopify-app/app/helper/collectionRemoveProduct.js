export const collectionRemoveProduct = async (
  admin,
  collectionId,
  productId,
) => {
  const productDelete = await admin.graphql(
    ` 
   mutation collectionRemoveProducts($id: ID!, $productIds: [ID!]!){
        collectionRemoveProducts(id: $id, productIds: $productIds){
          job{
            done
            id
          }
          userErrors{
            field
            message
          }
        }
      }
    `,
    {
      variables: {
        id: collectionId,
        productIds: [productId],
      },
    },
  );
  const response = await productDelete.json();
  
  const userErrors = response.data.collectionRemoveProducts.userErrors;
  
  
  if (userErrors && userErrors.length > 0) {
    return {
        status: 400,
        message: "Something went wrong",
        data: null
    };
  } else {
    return {
        status: 200,
        message: "The product deleted from the collection",
        data: null
    }
  }
};
