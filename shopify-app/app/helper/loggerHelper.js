
import path from "path";
import pino from "pino";

const isNode = typeof window === "undefined";
let logger = null;

async function setupLogger() {
  if (isNode) {
    const fs = await import("fs");
    const logsDir = "./logs";

    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
    }

    logger = pino(pino.destination(path.join(logsDir, "app.log")));
  } else {
    logger = pino({ browser: { asObject: true } });
  }
}

(async () => {
  await setupLogger();
})();

export function loggerInfo(text, shopName, bodyParams) {
  if (!logger) {
    console.error("Logger not initialized");
    return;
  }

  const logData = {
    message: text || "",
    store: shopName || "unknown",
    timestamp: new Date().toISOString(),
  };

  if (bodyParams != null) {
    logData.bodyParams = bodyParams;
  }

  if (isNode) {
    logger.info(logData);
  }
}

export function loggerError(text, shopName, errorMessage, bodyParams) {
  if (!logger) {
    console.error("Logger not initialized");
    return;
  }

  const logData = {
    message: text || "",
    store: shopName || "unknown",
    timestamp: new Date().toISOString(),
    errorMessage: errorMessage,
  };

  if (bodyParams != null) {
    logData.bodyParams = bodyParams;
  }

  if (isNode) {
    logger.error(logData);
  }
}

