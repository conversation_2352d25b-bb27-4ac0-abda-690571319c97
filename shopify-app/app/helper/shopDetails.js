import pino from "pino";
const logger = pino();

export const shopDetails = async (admin) => {
  try {
    const response = await admin.graphql(
      `
                query{
                    shop{
                      name
                      email
                      myshopifyDomain
                      contactEmail
                      ianaTimezone
                      billingAddress {
                        countryCodeV2
                        phone
                      }
                    }
                    app {
                      handle
                    }
                }
            `,
    );
    const data = await response.json();
    return data;
  } catch (error) {
    logger.error(error, "Shop details fetched failed or Something went wrong.");
  }
};