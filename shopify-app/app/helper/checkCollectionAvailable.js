import { removeGid } from "./removeGID"
import { collectionGID } from "../configs/config"


export const isCollectionAvailable = async (admin, collectionId) => {
    const collectionGlobalId = `${collectionGID}${collectionId}`
    const collectionAvailable = await admin.graphql(`
            {
                collection(id: "${collectionGlobalId}"){
                    id
                    title
                }
            }
        `)
    const response = await collectionAvailable.json();
    const collectionResponse = response?.data?.collection;
    const id= collectionResponse !== null ? collectionResponse?.id : null
    return id
}