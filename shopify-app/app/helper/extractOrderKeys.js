export async function extractOrderKeys(template, data) {
    const result = {};
  
    for (const key in template) {
      // Skip if key doesn't exist in data
      if (!Object.prototype.hasOwnProperty.call(data, key)) {
        continue;
      }
  
      const templateValue = template[key];
      const dataValue = data[key];
  
      // Handle arrays
      if (Array.isArray(templateValue)) {
        if (Array.isArray(dataValue) && dataValue.length > 0) {
          // Keep the original array data if it has content
          if (dataValue.some(item => item && typeof item === 'object' && Object.keys(item).length > 0)) {
            result[key] = dataValue;
          } else {
            // Try processing with template if items are empty
            const processedItems = dataValue.map(item => 
              item && typeof item === 'object' && Object.keys(templateValue).length > 0
                ? extractOrderKeys(templateValue[0], item) 
                : item
            );
            result[key] = processedItems;
          }
        } else {
          // Keep original empty array
          result[key] = dataValue;
        }
      }
      // Handle nested objects
      else if (typeof templateValue === 'object' && templateValue !== null) {
        if (typeof dataValue === 'object' && dataValue !== null) {
          // Check if the data object has meaningful content
          if (Object.keys(dataValue).length > 0) {
            // Use original data directly if it has content
            result[key] = dataValue;
          } else {
            // Try extraction only if original is empty
            const extracted = extractOrderKeys(templateValue, dataValue);
            result[key] = Object.keys(extracted).length > 0 ? extracted : dataValue;
          }
        } else {
          // Use original non-object value
          result[key] = dataValue;
        }
      }
      // Handle primitive values
      else {
        result[key] = dataValue;
      }
    }
  
    return result;
  }