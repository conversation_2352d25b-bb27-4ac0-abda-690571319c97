import {mixPanelTokenProduction} from "../configs/config";

import mixpanel from "mixpanel-browser";



const MIXPANEL_TOKEN = mixPanelTokenProduction;

// Event types
const EVENTS = {
  PAGE_VIEW: "Page View",
  BUTTON_CLICK: "Button Click",
};

// Initialize Mixpanel
if (MIXPANEL_TOKEN) {
  mixpanel.init(MIXPANEL_TOKEN, {
    debug: process.env.NODE_ENV !== "production",
  });
} else {
  console.warn("Mixpanel token is not set. Tracking will be disabled.");
}

// Generic track function
const track = (eventName, properties = {}) => {
  if (MIXPANEL_TOKEN) {
    mixpanel.track(eventName, properties);
  }
};

// Track page view
export const trackPageView = (pageName, additionalProperties = {}) => {
  track(`${pageName} ${EVENTS.PAGE_VIEW}`, {
    page_name: pageName,
    path: window.location.pathname,
    ...additionalProperties,
  });
};

// Track button click
export const trackButtonClick = (
  buttonName,
  pageName,
  additionalProperties = {},
) => {
  track(`${pageName} ${EVENTS.BUTTON_CLICK}`, {
    button_name: buttonName,
    page_name: pageName,
    path: window.location.pathname,
    ...additionalProperties,
  });
};

// Custom hook for page view tracking
import { useEffect, useRef } from "react";

export const usePageViewTracking = (pageName, additionalProperties = {}) => {
  const isFirstRender = useRef(true);

  useEffect(() => {
    if (isFirstRender.current) {
      trackPageView(pageName, additionalProperties);
      isFirstRender.current = false;
    }
  }, [pageName, additionalProperties]);
};