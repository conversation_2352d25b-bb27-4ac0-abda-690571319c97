import { createCipheriv, createDecipheriv, randomBytes } from 'crypto';
import dotenv from 'dotenv'
dotenv.config();


const algorithm = 'aes-256-cbc'; // Encryption algorithm
// const key = randomBytes(32); // Generate a secure key (this should be securely managed)
const iv = randomBytes(16); // Initialization vector
const key = Buffer.from(process.env.GOOGLE_ENCRYPTION_KEY, 'hex');

// Encrypt data
export const encrypt = (data) => {
    const cipher = createCipheriv(algorithm, key, iv);
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return {
        iv: iv.toString('hex'), // Store the IV with the encrypted data
        content: encrypted,
    };
};

// Decrypt data
export const decrypt = (encryptedData, encryptedIv) => {
    // Create decipher instance using the algorithm, key, and IV
    const decipher = createDecipheriv(algorithm, key, Buffer.from(encryptedIv, 'hex'));

    // Decrypt the data
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
}

export const gscDecrypt = (encryptedData) => {
    const decipher = createDecipheriv(algorithm, key, Buffer.from(encryptedData.iv, 'hex'));
    let decrypted = decipher.update(encryptedData.content, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
}