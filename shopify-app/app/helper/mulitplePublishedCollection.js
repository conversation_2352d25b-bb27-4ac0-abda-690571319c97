import { supabase } from "../db/supabase_insert_helper";
import { shopDetails } from "./shopDetails";
import { collectionGID, contactEmail, productGID } from '../configs/config';
import { removeGid } from "../helper/removeGID";
import { loggerInfo, loggerError } from '../helper/loggerHelper';
import { ContactLink } from "../configs/config";
import dotenv from 'dotenv';
import { ServerClient } from "postmark";
// Add these new imports
import { GraphQLClient, gql } from "graphql-request";
import crypto from "crypto";

dotenv.config();

const client = new ServerClient(process.env.POST_MARK_COLLECTION_API);
const senderMail = process.env.COLLECTION_SENDER_MAIL;

// Add the decryption function from the second file
const decode = (encryptedKey, encryptedIV, encryptedToken) => {
  const decipher = crypto.createDecipheriv(
    "aes-256-cbc",
    Buffer.from(encryptedKey, "hex"),
    Buffer.from(encryptedIV, "hex"),
  );
  let accessToken = decipher.update(encryptedToken, "hex", "utf8");
  accessToken += decipher.final("utf8");
  return accessToken;
};

async function sendPublishedCollectionsEmail(storeEmail, emailData) {
    // This function stays the same
    try {
        const response = await client.sendEmailWithTemplate({
            From: senderMail,
            To: storeEmail,
            TemplateId: process.env.PUBLISHED_COLLECTION_ID,
            TemplateModel: emailData,
            Tag: "MultipleBulkPublishedCollectionNotification",
            TrackOpens: true,
            TrackLinks: "HtmlAndText"
        });
        loggerInfo(`✅ Bulk published notification email sent successfully to ${storeEmail}`);
        return response;
    } catch (error) {
        loggerError(`❌ Error sending bulk published notification email to ${storeEmail}:`, error.message);
        throw error;
    }
}

export const multiplePublishCollection = async (storeId, collectionData) => {
    try {
        loggerInfo(`Multiple Collection Publish function is initialized. Total collections: ${collectionData.length}`);


        const {data: storeData, error: storeDataError} = await supabase.from('stores').select('*').eq('id', storeId);
        if(storeDataError) {
            loggerError("Store data not found", storeId, storeDataError.message);
            return {
                status: 400,
                message: "Store data not found",
                data: null
            };
        }
        const storeName = storeData[0].store_name;
        const storeDomain = storeData[0].store_url;
        const appHandle = 'test-collections';
        
        // Get and decrypt the access token
        const encryptedToken = storeData[0].access_token;
        const encryptedKey = storeData[0].encryption_key;
        const encryptedIV = storeData[0].iv;
        
        if (!encryptedToken || !encryptedKey || !encryptedIV) {
            loggerError("Missing encrypted data", storeName);
            return {
                status: 400,
                message: "Missing authentication data",
                data: null
            };
        }
        
        const accessToken = decode(encryptedKey, encryptedIV, encryptedToken);
        
        // Create GraphQL client
        const graphQLClient = new GraphQLClient(
            `https://${storeName}.myshopify.com/admin/api/2024-07/graphql.json`,
            {
                headers: {
                    "X-Shopify-Access-Token": accessToken,
                },
            }
        );
        
        // Get preferences data
        const { data: preferencesData, error: preferencesDataError } = await supabase
            .from('preferences')
            .select('*')
            .eq('store_id', storeId);
            
        if (preferencesDataError) {
            loggerError("Preferences data not available", storeName, preferencesDataError.message);
            return {
                status: 400,
                message: "Preferences data not available",
                data: null
            };
        }
        
        // Fetch Publication for publish the collections in online store
        const publications = await graphQLClient.request(gql`
            {
                publications(first: 100, catalogType: APP){
                    edges{
                        node{
                            id
                            name
                            catalog{
                                title
                            }
                        }
                    }
                }
            }
        `);
        
        const onlineStore = publications.publications.edges.find(edge => 
            edge.node?.name?.toLowerCase().includes('online store')
        );
        const publicationId = onlineStore?.node?.id;
        
        if (!publicationId) {
            loggerError(`Online store publication ID not found (Multiple Collection Publish)`, storeName);
            return {
                status: 400,
                message: "Online store publication ID not found",
                data: null
            };
        }
        
        // Process each collection
        const publishedCollections = [];
        const failedCollections = [];
        
        for (const collection of collectionData) {
            try {
                const { collection_name, collection_url, product_details, collection_id, id, description } = collection;
                
                loggerInfo(`Processing collection: ${collection_name} (ID: ${id})`);
                
                let collectionGlobalId = collection_id ? `${collectionGID}${collection_id}` : null;
                const currentTime = new Date().toISOString();
                
                // Check if collection exists
                if (collectionGlobalId) {
                    const findCollectionResponse = await graphQLClient.request(gql`
                        {
                          collection(id: "${collectionGlobalId}") {
                            id
                            title
                          }
                        }
                    `);
                    
                    const isCollectionAvailable = findCollectionResponse?.collection;
                    
                    if (!isCollectionAvailable) {
                        collectionGlobalId = null; // Reset if not found
                    }
                }
                
                // Create collection if it doesn't exist
                if (!collectionGlobalId) {
                    const createCollectionResponse = await graphQLClient.request(gql`
                        mutation collectionCreate($input: CollectionInput!){
                            collectionCreate(input: $input){
                            
                                userErrors{
                                    field
                                    message
                                }
                                collection{
                                    id
                                    title
                                    handle
                                    descriptionHtml
                                    
                                }
                            }
                        }
                    `, {
                        input: {
                            title: collection_name,
                            handle: collection_url,
                            descriptionHtml: `<p>${description}</p>`,
                            metafields: [
      {
        namespace: "custom",
        key: "generated_by",
        type: "single_line_text_field",  // or use "date" if you're using the appropriate format
        value: "Rank Collections"
      }
    ]
                        }
                    });
                    
                    const userErrors = createCollectionResponse?.collectionCreate?.userErrors;
                    
                    if (userErrors && userErrors.length > 0) {
                        loggerError(`Failed to create collection ${collection_name}`, storeName, JSON.stringify(userErrors));
                        failedCollections.push({
                            id,
                            name: collection_name,
                            error: "Failed to create collection"
                        });
                        continue;
                    }
                    
                    collectionGlobalId = createCollectionResponse?.collectionCreate?.collection?.id;
                    
                    // Add products to collection
                    if (product_details && product_details.length > 0) {
                        const productIds = product_details.map(product => `${productGID}${product.id}`);
                        
                        const addProductsResponse = await graphQLClient.request(gql`
                            mutation collectionAddProducts($id: ID!, $productIds: [ID!]!){
                                collectionAddProducts(id: $id, productIds: $productIds){
                                    collection{
                                        id
                                        handle
                                        title
                                    }
                                    userErrors{
                                        field
                                        message
                                    }
                                }
                            }
                        `, {
                            id: collectionGlobalId,
                            productIds: productIds
                        });
                        
                        const productErrors = addProductsResponse?.collectionAddProducts?.userErrors;
                        
                        if (productErrors && productErrors.length > 0) {
                            loggerError(`Failed to add products to collection ${collection_name}`, storeName, JSON.stringify(productErrors));
                            // Continue despite product errors
                        }
                    }
                }
                
                // Publish collection to online store - FIXED THIS PART
                const publishCollectionResponse = await graphQLClient.request(gql`
                    mutation publishablePublish($id: ID!, $input: [PublicationInput!]!) {
                        publishablePublish(id: $id, input: $input) {
                            publishable {
                                ... on Collection {
                                    id
                                    title
                                    handle
                                    publishedOnPublication(publicationId: "${publicationId}")
                                }
                            }
                            userErrors {
                                field
                                message
                            }
                        }
                    }
                `, {
                    id: collectionGlobalId,
                    input: [{
                        publicationId,
                        publishDate: currentTime
                    }]
                });
                
                const publishErrors = publishCollectionResponse?.publishablePublish?.userErrors;
                
                if (publishErrors && publishErrors.length > 0) {
                    loggerError(`Failed to publish collection ${collection_name}`, storeName, JSON.stringify(publishErrors));
                    failedCollections.push({
                        id,
                        name: collection_name,
                        error: "Failed to publish collection"
                    });
                    continue;
                }
                
                // Update collection status in database
                const { data: updateData, error: updateError } = await supabase
                    .from('collections')
                    .update({
                        status: 'published',
                        collection_id: removeGid(collectionGlobalId),
                        published_time: currentTime,
                        published_type: 'bulk'
                    })
                    .eq('id', id)
                    .eq('store_id', storeId)
                    .select("*");
                
                if (updateError) {
                    loggerError(`Failed to update collection status for ${collection_name}`, storeName, updateError.message);
                    failedCollections.push({
                        id,
                        name: collection_name,
                        error: "Failed to update collection status"
                    });
                    continue;
                }
                
                publishedCollections.push({
                    id,
                    name: collection_name,
                    url: `https://${storeDomain}/collections${collection_url}`
                });
                
                loggerInfo(`Successfully published collection: ${collection_name}`);
                
            } catch (collectionError) {
                loggerError(`Error processing collection`, storeName, collectionError.message);
                failedCollections.push({
                    id: collection.id,
                    name: collection.collection_name,
                    error: "Unexpected error during processing"
                });
            }
        }
        
        // Send email if enabled and collections were published
        if (preferencesData[0].email_enabled && publishedCollections.length > 0) {
            const emailData = {
                store_name: storeName,
                collections: publishedCollections,
                ctaUrl: `https://admin.shopify.com/store/${storeName}/apps/${appHandle}/app/collection_drafts`,
                supportUrl: contactEmail,
                supportLinkText: contactEmail
            };
            
            try {
                await sendPublishedCollectionsEmail(storeData[0].store_email, emailData);
            } catch (emailError) {
                loggerError(`Failed to send bulk published collections email`, storeName, emailError.message);
                // Continue despite email error
            }
        }
        
        return {
            status: 200,
            message: `Published ${publishedCollections.length} collections${failedCollections.length > 0 ? `, ${failedCollections.length} failed` : ''}`,
            data: {
                published: publishedCollections,
                failed: failedCollections
            }
        };
        
    } catch (error) {
        loggerError('Unexpected error in multiple collection publish', null, error.message);
        return {
            status: 500,
            message: "An unexpected error occurred",
            data: null
        };
    }
};