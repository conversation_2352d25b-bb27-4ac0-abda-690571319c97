{"Rank_collections": {"navbar_contents": {"nav_1": "Home", "nav_2": "Collections Gallery", "nav_3": "Analytics", "nav_4": "Settings", "nav_5": "Pricing"}, "onboarding": {"main_heading": "You are almost there!", "complete_button": "Complete Setup", "heading_1": "Discover he ultimate collections app for your e-commerce store!", "heading_2": "Automatically gather and analyze data to dynamically generate and refresh collections, keeping your store engaging and relevant. Frequent updates ensure increased traction and convert visitors into loyal customers.", "heading_3": "To simplify your tasks, we will analyse your store and curate a collection combination for you. Click on “Generate Now” to get started and start curating your collections.", "button_1": "Create Collection Opportunity", "heading_4": "Welcome to Rank Collections", "heading_5": "Ready to level up your collections?", "heading_6": "Analyzing Your Store", "content_1": "Almost there, just a few seconds", "content_2": "Connecting to your shopify store...", "content_3": "Connected to your shopify store.", "content_4": "Analyzing your product range...", "content_5": "Products analyzed.", "content_6": "Identifying new collection opportunities...", "heading_7": "Analysis Complete. Let's get to work!", "content_7": "Products", "content_8": "Collections", "heading_8": "What’s the first step you'd like to take?", "content_9": "Create new collection pages", "button_2": "Proceed", "content_10": "Start optimizing exisiting collections", "badge_1": "Launching Soon", "content_11": "Start optimizing collection's internal linking", "badge_2": "Launching Soon", "button_3": "Continue to setup", "heading_9": "Tell us more about your store", "content_12": "This input lets us craft the perfect collection pages for your store's category.", "heading_10": "Store Category", "placholder_1": "Select a category", "heading_11": "Where do you sell?", "content_13": "This input lets us generate collection pages for your ideal customers.", "heading_12": "Target Region", "placeholder_2": "Select a region", "heading_13": "Target Country", "placholder_3": "Select a country", "button_4": "Back", "button_5": "Continue", "content_info": "Please select the region and country where most of your customers are based.", "store_options": [{"label": "Fashion and Apparel", "value": "fashion_and_apparel"}, {"label": "Electronics and Gadgets", "value": "electronics_and_gadgets"}, {"label": "Health and Beauty", "value": "health_and_beauty"}, {"label": "Home and Living", "value": "home_and_living"}, {"label": "Sports and Outdoors", "value": "sports_and_outdoors"}, {"label": "Books and Stationery", "value": "books_and_stationery"}, {"label": "Food and Beverage", "value": "food_and_beverage"}, {"label": "Toys and Hobbies", "value": "toys_and_hobbies"}, {"label": "Automotive", "value": "automotive"}, {"label": "Jewelry and Accessories", "value": "jewelry_and_accessories"}, {"label": "Pet Supplies", "value": "pet_supplies"}, {"label": "Art and Craft", "value": "art_and_craft"}, {"label": "Baby and Kids", "value": "baby_and_kids"}, {"label": "Gaming", "value": "gaming"}, {"label": "Office Supplies", "value": "office_supplies"}, {"label": "Furniture", "value": "furniture"}, {"label": "Travel and Luggage", "value": "travel_and_luggage"}, {"label": "Musical Instruments", "value": "musical_instruments"}, {"label": "Garden and Outdoor", "value": "garden_and_outdoor"}, {"label": "Industrial and Tools", "value": "industrial_and_tools"}, {"label": "Luxury Goods", "value": "luxury_goods"}, {"label": "Digital Products", "value": "digital_products"}, {"label": "Secondhand and Vintage", "value": "secondhand_and_vintage"}], "heading_14": "Let's Get Started", "content_14": "Create, Optimize, Rank, and Boost Sales from your collection pages.", "button_6": "Start Now", "content_15": "Adding competitor URLs allows us to provide tailored collection recommendations, helping you dominate your market, optimize your collection strategy, and stay ahead of the competition.", "heading_15": "Competitor URL", "placeholder_1": "Enter a competitor", "button_7": "Add", "button_8": "X", "button_9": "Proceed", "button_10": "<PERSON><PERSON>", "toast_1": "Please choose a store category", "toast_2": "Please choose a store region", "toast_3": "Please choose a country", "content_16": "Generate Collections That Drive Traffic, Conversions, and Growth!", "heading_16": "Store Analysis", "heading_17": "Store Specifics", "heading_18": "Store Competitors", "heading_19": "Subscription Plans"}, "home": {"heading_1": "Leverage the fullest potential of your collection pages!", "content_1": "Automate, optimize, and sell more with high-impact collections.", "heading_2": "Number of Products", "heading_3": "Collection Page Opportunities", "heading_4": "Estimated Traffic Growth", "heading_5": "Schedule Collections", "content_2": "Schedule it now, manually or with AI-recommended timings, to boost traffic.", "button_1": "Schedule Now", "heading_6": "Generate New Collections", "content_3": "Unlock new collection opportunities we have spotted in your store. Start now!", "button_2": "Start Generation", "heading_7": "Pro Tips for Smarter Collections", "heading_8": "Best Practices", "link_content_1": "Discover how top merchants structure their collections to drive engagement and maximize sales.", "heading_9": "SEO Essentials", "link_content_2": "Learn how our AI fine-tunes your collections for search engines to boost visibility and organic traffic.", "heading_10": "Collection Metrics", "link_content_3": "Dive into actionable insights and know the right metrics to track that enhance your collection pages.", "content_4": "Need help getting started? Contact ", "link_content_4": "Chakril support ", "content_5": "for personalized assistance.", "modal_heading_1": "generating", "modal_badge_1": "Best Recommended", "modal_heading_2": "Fully Automated Generation", "modal_content_1": "Generates and auto-publishes collection pages by tracking trends and peak performance times", "modal_heading_3": "Semi-Automated Generation", "modal_content_2": "Generates collection pages and lets you publish them manually when needed", "info_content": "The number of new collection pages you can generate based on your product inventory and current market trends.", "info_content_1": "The projected average traffic increase to your store from the new collection pages.", "modal_content_3": "This is a pro feature. Upgrade your plan to automate collection generation and scheduling.", "modal_button_1": "Upgrade Now", "modal_heading_4": "Upgrade Plan", "modal_heading_5": "Almost There!", "modal_content_5": "Hang tight! Your collection pages are being generated.", "modal_content_6": "You have reached your limit to generate the collections", "toast_1": "Hang tight! Your collections will be available in the Collections Gallery soon.", "modal_heading_6": "Analyzing Your Products...", "modal_content_7": "We're currently processing your products. Sit tight—you'll receive an email once syncing is complete, and you can start creating collections!", "badge_1": "Need Help? We’re Here for You!", "heading_11": "Our support team is ready to assist you at every step. Here’s what you can expect:", "benefits": [{"bold": "Fast responses", "content": "to your questions and setup needs."}, {"bold": "Expert advice", "content": "on optimizing your collection strategy."}, {"bold": "Quick resolution", "content": "for any technical or account issues."}]}, "schedule_collections": {"heading_0": "Schedule Collections", "heading_1": "AI Recommendations", "heading_2": "Custom Scheduling", "heading_3": "AI-powered Scheduling", "content_1": "Our AI analyzes traffic patterns, competition, and custormer behavior to suggest optimal publishing times.", "heading_4": "Publish Time", "heading_5": "Impact Score", "heading_6": "Date", "heading_7": "Time", "heading_8": "Posting Frequency", "placeholder_1": "Select a frequency", "help_content_1": "One collection page will go live in your store based on your selected posting frequency.", "button_1": "Save", "custom_options": [{"label": "Every 3 Days", "value": "every_three_days"}, {"label": "Weekly", "value": "weekly"}, {"label": "Bi-weekly", "value": "every_two_weeks"}, {"label": "Monthly", "value": "monthly"}]}, "collection_generation": {"heading_1": "Generate Collection Pages", "heading_2": "Fully Automated Generation", "heading_3": "Semi-Automated Generation", "content_1": "Select which opportunities you want the ai to monitor and automatically create collection for.", "heading_4": "Enter your competitor's Store URL", "help_text_1": "Need to a text below this : This helps us to analyze the potential competition and stay ahead of it.", "button_1": "Don't know your competitors? Use the AI-suggestion engine", "button_2": "Add", "content_2": "Or", "button_3": "X", "button_6": "Analyze", "heading_5": "Select your collection preferences", "content_3": "Pick the preferred categories to create your collection pages", "heading_6": "Product Attributes Collections (Collections based on product features and characteristics)", "product_attributes": [{"label": "Color-Based Collections", "help_text": "(e.g., \"All Black Collection\", \"Pastel Collection\")", "badge": "Medium"}, {"label": "Price-Based Collections", "help_text": "(e.g., Budget Buys, Premium Picks)", "badge": "High"}, {"label": "Material-Based Collections", "help_text": "(e.g., <PERSON>ther Goods, Cotton Apparel)", "badge": "High"}, {"label": "Size-Based Collections", "help_text": "(e.g., Plus Size Fashion, Compact Gadgets)", "badge": "Medium"}, {"label": "Feature-Based Collections", "help_text": " (e.g., Focus on specific features like \"Waterproof Gear\" or \"Wireless Devices.\")", "badge": "Medium"}], "heading_7": "Seasonal Collections (Collections tailored to seasons, holidays or special events)", "trends": [{"label": "Seasonal Collections ", "help_text": "(e.g., Winter Essentials, Summer Must-Haves)", "badge": "High"}, {"label": "Holiday Collections", "help_text": " (e.g., Christmas Specials, Valentine's Day Gifts)", "badge": "High"}, {"label": "Event-Based Collections", "help_text": "(e.g., Wedding Essentials, Party Decor)", "badge": "Medium"}, {"label": "Festival Collections ", "help_text": "(e.g., <PERSON><PERSON><PERSON>, Halloween Costumes)", "badge": "Medium"}], "heading_8": "Customer Behavior Collections(Collections driven by customer actions and preferences)", "behaviors": [{"label": "Best-Seller Collections", "help_text": "(Showcases top-performing products.)", "badge": "High"}, {"label": "Frequently Bought Together Collections", "help_text": "(Groups products commonly purchased as bundles.)", "badge": "High"}, {"label": "Wishlist Collections", "help_text": "(Highlights most wishlisted or favorited products.)", "badge": "Medium"}, {"label": "Recently Viewed Collections", "help_text": " (Displays products recently viewed by customers.)", "badge": "Medium"}, {"label": "Trending Now Collections", "help_text": "(Features products rapidly gaining popularity.)", "badge": "Medium"}], "heading_9": "Location-Based Collections (Collections optimized for local SEO and specific markets)", "location": [{"label": "Local-Based Collections", "help_text": "(e.g., \"Made in California\", \"New York Favorites\")", "badge": "Medium"}, {"label": "Region-Specific Collections", "help_text": "(e.g., \"European Styles\", \"Asian Cuisine Essentials\")", "badge": "Medium"}, {"label": "Cultural Collections", "help_text": "(e.g., \"Japanese Minimalism\", \"Mediterranean Flavors\")", "badge": "Medium"}], "heading_10": "Market Analysis Collections (Collections driven by competitive insights and market trends)", "market": [{"label": "Competitive Gap Collections", "help_text": "Identify and fill gaps in competitor offerings with unique product groupings.", "badge": "Medium"}, {"label": "Trend-Based Collections", "help_text": "Curate products based on emerging market trends and analytics.", "badge": "Medium"}, {"label": "Social Trend Collections", "help_text": "Showcase products inspired by trending topics on social media.", "badge": "Medium"}], "heading_11": "Schedule Collections", "heading_12": "Posting Frequency", "info": "We will identify collection opportunities and publish them to your store based on your preferred posting frequency.", "placeholder_1": "Select a frequency", "heading_13": "Select Impact Score", "frequency_options": [{"label": "Every 3 Days", "value": "every_three_days"}, {"label": "Weekly", "value": "weekly"}, {"label": "Bi-weekly", "value": "every_two_weeks"}, {"label": "Monthly", "value": "monthly"}], "impact_score_options": [{"label": "High Impact", "value": "high"}, {"label": "Low Impact", "value": "Low"}, {"label": "Medium Impact", "value": "medium"}, {"label": "Both High and Medium", "value": "highAndMedium"}, {"label": "Both High and Low", "value": "highAndLow"}, {"label": "Both Low and Medium", "value": "lowAndMedium"}, {"label": "All High, Medium and Low", "value": "allHighMediumAndLow"}], "button_4": "Save and Schedule", "button_5": "Save and Proceed", "heading_14": "Impact", "heading_15": "Launching Soon", "toast_1": "The maximum number of collections allowed is 10.", "analysis": [{"label": "Competitor Gap", "help_text": "Collections filling gaps in competitor offerings", "badge": "High Impact"}, {"label": "Market Trends", "help_text": "Collections based on market analysis and trends", "badge": "High Impact"}, {"label": "Social Media Trends", "help_text": "Collections inspired by social media trends", "badge": "Medium Impact"}], "heading_16": "Impact Score", "placeholder_2": "Select an impact score", "heading_17": "Number of Collections", "toast_2": "Collection generation in progress. Check Collections Gallery to view them."}, "dashboard": {"heading_1": "Dashboard", "heading_2": "Generated Collections", "heading_3": "Active Collections", "heading_4": "Scheduled Collections", "heading_5": "Conversion Rate", "heading_6": "Date Range", "filter_options": [{"label": "Overall", "value": "overall"}, {"label": "Today", "value": "today"}, {"label": "This Week", "value": "thisWeek"}, {"label": "This Month", "value": "thisMonth"}, {"label": "This Year", "value": "thisYear"}], "heading_7": "Recent Collections", "heading_8": "Collection Name", "heading_9": "Last Published On", "heading_10": "URL Slug: ", "heading_11": "Top Performing Collections", "heading_12": "Collection Name", "heading_13": "Total Views", "empty_content_1": "No collection has been published", "empty_content_2": "Track your collections' performance once they're live!", "empty_content_3": "No Notifications Available", "heading_14": "Notifications", "content_1": "The", "content_2": "collection is live in your store!", "content_3": "Total products Available in the Store", "content_4": "Total products Out of Stock"}, "collection_gallery": {"heading_1": "Collection Gallery", "content_1": "Update, publish, view, or remove collections.", "placeholder_1": "Search for a collection", "button_1": "Schedule Now", "button_2": "Import / Export", "button_3": "Generate New Collections", "button_4": "Import", "button_5": "Export", "button_6": "Fully Automated", "button_7": "Semi-Automated", "button_8": "Delete", "button_9": "Cancel", "button_10": "Delete", "button_11": "Cancel", "button_12": "Delete", "button_13": "Cancel", "status_options": [{"label": "All", "value": "all"}, {"label": "Generated", "value": "generated"}, {"label": "Published", "value": "published"}, {"label": "Unpublished", "value": "unpublished"}, {"label": "Scheduled", "value": "scheduled"}], "heading_2": "Collection Name", "heading_3": "Products", "heading_4": "URL Slug", "heading_5": "Generated On", "heading_6": "Scheduled On", "heading_7": "Status", "heading_8": "Action", "heading_9": "Confirm Deletion", "heading_10": "Confirm Deletion", "content_2": "Showing", "content_3": "Are you sure you want to delete the selected collection?", "content_4": "Are you sure want to delete the selected collection?", "content_5": "Are you sure want to unpublish the selected Collection?", "modal_heading_1": "Import Collections", "modal_content_1": "Bulk import collections by uploading a .csv file.", "modal_button_1": "Download a sample .csv file", "modal_button_2": "Upload .csv file", "modal_button_3": "Upload", "modal_button_4": "Import", "modal_heading_2": "Edit Collections", "modal_heading_3": "Collection Name", "modal_heading_4": "URL Slug", "modal_heading_5": "Description", "modal_button_5": "Update", "modal_heading_6": "Unpublish Collection", "modal_button_6": "Unpublish", "toast_1": "Hang tight! Your collections will be available in the Collections Gallery soon.", "modal_heading_7": "Analyzing Your Products...", "modal_content_2": "We're currently processing your products. Sit tight—you'll receive an email once syncing is complete, and you can start creating collections!"}, "Settings": {"heading_1": "Settings", "content_1": "Tailor your collection settings just the way you want.", "heading_2": "Email Notifications", "content_2": "Get collection updates delivered straight to your inbox!", "radio_1": "Enable", "help_text_1": "Stay in the loop! Turn on email notifications to get updates when new collections are generated and published.", "radio_2": "Disable", "help_text_2": "Prefer to check yourself? Turn off email notifications and monitor your collections directly in the app.", "heading_3": "Connect Your Google Account", "heading_4": "Connect Google Analytics", "button_1": "Sign-in with Google", "button_2": "Logout", "heading_5": "Select an Account", "heading_6": "Select ID", "button_3": "Save", "heading_7": "Shuffle", "badge_1": "Active", "badge_2": "Inactive", "heading_8": "Auto-shuffle collections effectively", "content_3": "Enable auto-shuffle to keep your collections dynamic and fresh, to maintain user interest and optimize engagement.", "button_4": "Turn On", "button_5": "Turn Off", "warning_1": "This account does not have any google analytics account. You want create", "button_6": "here", "warning_2": "from this account and signin with different account", "button_7": "Logout", "heading_9": "Connect Google Search Console", "content_4": "The connected Account", "button_8": "Sign-in with Google", "button_9": "Logout", "heading_10": "Weekly Report for Collection Pages", "content_5": "Stay informed about how your collection pages are performing.", "radio_3": "Enable Weekly Report", "help_content_1": "Receive an email summary every week with performance metrics and actionable insights for your collections.", "radio_4": "Disable Weekly Report", "help_content_2": "Stop receiving weekly performance summaries for your collection pages."}, "product_details": {"content_1": "Products.", "content_2": "Last Updated", "content_3": "days ago", "button_1": "Schedule Now", "content_4": "Views (Last 7 Days)", "content_5": "vs previous", "heading_1": "In Stock", "heading_2": "Out of Stock", "heading_3": "Collection Products", "button_2": "Add Products", "badge_1": "High Performance", "heading_4": "AI Recommendations", "content_6": "Based on your store's data and market analysis", "heading_5": "Optimal Publish Time", "content_7": "Tuesday at 2 PM (Loacl Time)", "content_8": "92% optimization score. High traffic. Low competition", "heading_6": "product Recommendations", "content_9": "Add 3-5 more products in the $50-70 range", "heading_7": "SEO Optimization", "content_10": "Add \"summer fashion\" and \"beach wear\" to boost visibility", "heading_8": "Collection Settings", "heading_9": "(Coming Soon)", "placeholder_1": "Search for a product", "content_11": "No products found", "heading_10": "Sort Your Collections", "label_1": "Sort the collections", "placeholder_2": "Choose sorting option", "button_3": "Sort", "filter_options": [{"label": "All Products", "value": "all-products"}, {"label": "In Stock", "value": "in-stock"}, {"label": "Out of Stock", "value": "out-of-stock"}], "collection_settings": [{"label": "Auto-update products"}, {"label": "SEO optimization"}, {"label": "Price optimization"}], "modal_title_1": "Product Delete Confirmation", "modal_button_1": "Delete", "modal_button_2": "Cancel", "modal_msg_1": "Are you sure want to delete the product", "modal_msg_2": "Are you sure want to delete this product?", "modal_title_2": "Products Choose", "modal_button_3": "Cancel", "modal_button_4": "Confirm & Add", "sort_order_options": [{"label": "Product title A-Z", "value": "ALPHA_ASC"}, {"label": "Product title Z-A", "value": "ALPHA_DESC"}, {"label": "Highest price first", "value": "PRICE_DESC"}, {"label": "Lowest price first", "value": "PRICE_ASC"}, {"label": "Newest first", "value": "CREATED_DESC"}, {"label": "Oldest first", "value": "CREATED"}, {"label": "Best selling", "value": "BEST_SELLING"}, {"label": "Manual", "value": "MANUAL"}]}, "plans_page": {"heading": "Choose Your Plan", "button_1": "Subscribe Now", "button_2": "Subscribed", "content_1": "Are you sure you want to downgrade to the Free Plan? This will:", "content_2": "Cancel your current subscription", "content_3": "Remove access to premium features", "content_4": "Take effect immediately", "planDetails": [{"planKey": "freePlan", "planName": "Starter", "planDescription": "Start Smart, <PERSON>row Fast – Test Automated Collections for Free!", "amount": "Forever Free", "features": ["5 Collections", "Built-in Analytics Dashboard", "Custom Scheduling"], "buttonLabel": "Subscribe Now"}, {"planKey": "growthPlan", "planName": "Try Growth Plan", "planDescription": "Recommended for your store size", "amount": "59.99", "features": ["Create upto 10 optimized collections", "Advanced SEO Optimization", "Automatic Updates", "Priority Support"], "buttonLabel": "Upgrade"}, {"planKey": "advancedPlan", "planName": "Try Advanced Plan", "planDescription": "Recommended for your store size", "amount": "99.99", "features": ["Create upto 25 optimized collections", "Advanced SEO Optimization", "Automatic Updates", "Priority Support"], "buttonLabel": "Upgrade"}, {"planKey": "automatic", "planName": "Pro", "planDescription": "Scale with confidence - Unlock hidden opportunities & automation!", "amount": "19.99", "features": ["Everything in Starter+", "30 Collections", "Competitor Analysis", "Smart Auto-Scheduling", "Personalized Recommendations", "Conversion Tracking", "Out-of-Stock Monitoring"], "buttonLabel": "Subscribe Now"}]}, "components": {"heading_1": "Hang tight! We're getting the app ready for you."}}}