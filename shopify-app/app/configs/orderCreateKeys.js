export const orderKeys = {
    id: '',
    admin_graphql_api_id: '',
    buyer_accepts_marketing: false,
    contact_email: '',
    created_at: '',
    currency: '',
    current_shipping_price_set: {
      shop_money: {
        amount: '',
        currency_code: ''
      },
      presentment_money: {
        amount: '',
        currency_code: ''
      }
    },
    current_subtotal_price: '',
    current_subtotal_price_set: {
      shop_money: {
        amount: '',
        currency_code: ''
      },
      presentment_money: {
        amount: '',
        currency_code: ''
      }
    },
    current_total_additional_fees_set: null,
    current_total_discounts: '',
    current_total_discounts_set: {
      shop_money: {
        amount: '',
        currency_code: ''
      },
      presentment_money: {
        amount: '',
        currency_code: ''
      }
    },
    current_total_duties_set: null,
    current_total_price: '',
    current_total_price_set: {
      shop_money: {
        amount: '',
        currency_code: ''
      },
      presentment_money: {
        amount: '',
        currency_code: ''
      }
    },
    current_total_tax: '',
    current_total_tax_set: {
      shop_money: {
        amount: '',
        currency_code: ''
      },
      presentment_money: {
        amount: '',
        currency_code: ''
      }
    },
    customer_locale: '',
    discount_codes: [],
    duties_included: false,
    email: '',
    payment_gateway_names: [],
    phone: '',
    po_number: '',
    presentment_currency: '',
    processed_at: '',
    reference: '',
    referring_site: '',
    source_identifier: '',
    source_name: '',
    source_url: '',
    subtotal_price: '',
    subtotal_price_set: {
      shop_money: {
        amount: '',
        currency_code: ''
      },
      presentment_money: {
        amount: '',
        currency_code: ''
      }
    },
    tags: '',
    tax_exempt: false,
    tax_lines: [
      {
        price: '',
        rate: 0,
        title: '',
        price_set: {
          shop_money: {
            amount: '',
            currency_code: ''
          },
          presentment_money: {
            amount: '',
            currency_code: ''
          }
        },
        channel_liable: false
      }
    ],
    taxes_included: false,
    test: false,
    user_id: '',
    customer: {
      id: '',
      email: '',
      created_at: '',
      updated_at: '',
      first_name: '',
      last_name: '',
      state: '',
      note: '',
      verified_email: false,
      multipass_identifier: '',
      tax_exempt: false,
      phone: '',
      email_marketing_consent: {
        state: '',
        opt_in_level: '',
        consent_updated_at: ''
      },
      sms_marketing_consent: null,
      tags: '',
      currency: '',
      tax_exemptions: [],
      admin_graphql_api_id: '',
      default_address: {
        id: '',
        customer_id: '',
        first_name: '',
        last_name: '',
        company: '',
        address1: '',
        address2: '',
        city: '',
        province: '',
        country: '',
        zip: '',
        phone: '',
        name: '',
        province_code: '',
        country_code: '',
        country_name: '',
        default: false
      }
    },
    discount_applications: [],
    fulfillments: [],
    line_items: [
      {
        id: '',
        admin_graphql_api_id: '',
        attributed_staffs: [],
        current_quantity: 0,
        fulfillable_quantity: 0,
        fulfillment_service: '',
        fulfillment_status: '',
        gift_card: false,
        grams: 0,
        name: '',
        price: '',
        price_set: {
          shop_money: {
            amount: '',
            currency_code: ''
          },
          presentment_money: {
            amount: '',
            currency_code: ''
          }
        },
        product_exists: true,
        product_id: '',
        properties: [],
        quantity: 0,
        requires_shipping: true,
        sku: '',
        taxable: true,
        title: '',
        total_discount: '',
        total_discount_set: {
          shop_money: {
            amount: '',
            currency_code: ''
          },
          presentment_money: {
            amount: '',
            currency_code: ''
          }
        },
        variant_id: '',
        variant_inventory_management: '',
        variant_title: '',
        vendor: '',
        tax_lines: [
          {
            channel_liable: false,
            price: '',
            price_set: {
              shop_money: {
                amount: '',
                currency_code: ''
              },
              presentment_money: {
                amount: '',
                currency_code: ''
              }
            },
            rate: 0,
            title: ''
          }
        ],
        duties: [],
        discount_allocations: []
      }
    ]
  };
  