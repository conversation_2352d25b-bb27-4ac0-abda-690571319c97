export const productGID = `gid://shopify/Product/`
export const collectionGID = `gid://shopify/Collection/`
export const requiredHeader = [
    "Collection Name",
    "URL",
    "Description",
    "Keywords",
    "Product Title",
    "Product Handle",
    "Product ID"
]

export const contactEmail = `<EMAIL>`
export const ContactLink = `https://www.chakril.com/contact/`
export const bestPracticesLink = `https://www.chakril.com/blog/how-to-organize-products-on-shopify/`
export const seoLink = `https://www.chakril.com/blog/ecommerce-category-page-seo-guide/`
export const collectionMetricsLink = `https://www.chakril.com`
export const testCharge = true

export const intercomAppId = `y1rd43g0`
export const mixPanelTokenProduction = `5ee8fdff5d01bc48c57e04f70fad1293`

export const planProductCount = 100;
export const engineBaseUrl = `https://stage.proactive.ai.chakril.site`
export const engineAPIVersion = `/api/v1`;
export const engineAuth = `/auth`;
export const engineInstall = `/install`
export const collectionRoute = `/collections`;
export const syncRoute = `/sync`;
export const syncAllProduct = `/products`
export const schedulingRount = `/collection-scheduling-inputs`
export const keywordsSubroute = `/keywords`
export const keywordsRoute = `/get-trending-keywords`
export const manualCreateionRoute = `/create-collection`
export const taskStatusRoute = `/collection-status`
export const fetchSubroute = `/fetch`
export const timeRoute = `/best-schedule-timing`
export const competitorUrlRoute = `/competitor-url`
export const productCount = `/get-synced-product-count`
export const dashboardMetrics = `/dashboard-metrics`
export const collectionFetch = `/created-collections`
export const productStatusSubRoute = `/status`
export const gscDataSync = `/google-search-console`

export const extractCollection = `/collections/`
export const gscMailSendPeriod = 'weekly';
export const ordersRoute = `/orders-create`
export const deleteRoute = '/delete-products'
export const updateProductRoute = '/update-products'


export const manualCreationCallback = `/manual-collection-generate`
export const mixPanelToken = 'e998987f96c4e18842a4d5c65eb3167f'
