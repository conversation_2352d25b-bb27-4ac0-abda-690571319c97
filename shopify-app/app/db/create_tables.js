import {pool} from './supabase_create_helper.js'
import pino from 'pino';
import { storesTable,preferenceTable,collections, plan, productsTable, gscData, collectionJobsTable } from './schema.js';
const logger = pino();
export async function createTable() {

    const client = await pool.connect();
  
    try {
      await client.query(storesTable);
      logger.info("The stores table created successfully.");
      await client.query(preferenceTable);
      logger.info("The preferences table created successfully")
      await client.query(collections)
      logger.info("The collections table created successfully.")
      await client.query(plan)
      logger.info("The Plans table created successfully.")
      await client.query(productsTable)
      logger.info("The Products table created successfully.")
      await client.query(gscData)
      logger.info("The GSC Data table created successfully.")
      await client.query(collectionJobsTable)
      logger.info("The Collection Jobs table created successfully.")
    } catch (error) {
      logger.error(error, 'Error creating Tables:');
    } finally {
      client.release();
    }
  }
