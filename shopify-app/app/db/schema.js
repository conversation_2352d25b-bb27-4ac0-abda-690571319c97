export const storesTable = `
    CREATE TABLE IF NOT EXISTS public.Stores (
    id BIGSERIAL PRIMARY KEY,
    uuid VARCHAR(150) UNIQUE,
    store_name VARCHAR(100) NOT NULL,
    store_url VARCHAR(500),
    store_email VARCHAR(500),
    is_first_time BOOLEAN NOT NULL DEFAULT TRUE,
    charge_id VARCHAR(100),
    payment_status VARCHAR(50),
    plan_created_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    plan_expires_at TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    onboarding_status BOOLEAN NOT NULL DEFAULT FALSE,
    reinstalled_user BOOLEAN NOT NULL DEFAULT FALSE,
    access_token TEXT,
    encryption_key TEXT,
    iv TEXT,
    onboarded_step TEXT DEFAULT 'step-1',
    google_is_logged_in BOOLEAN NOT NULL DEFAULT FALSE,
    google_analytics_key TEXT,
    google_encryption_key TEXT,
    google_account_id BIGINT,
    google_property_id BIGINT,
    google_encrypted_iv TEXT,
    is_store_connected BOOLEAN DEFAULT FALSE,
    is_product_synced BOOLEAN DEFAULT FALSE,
    product_count BIGINT,
    collection_count BIGINT,
    manual_update BOOLEAN DEFAULT FALSE,
    automatic_update BOOLEAN DEFAULT FALSE,
    seo_optimization TEXT,
    priority_support BOOLEAN DEFAULT FALSE,
    url_synced BOOLEAN,
    engine_api_token TEXT,
    is_free_planned BOOLEAN, 
    is_growth_planned BOOLEAN,
    is_advance_planned BOOLEAN,
    is_automatic_plan BOOLEAN,
    collection_generated_count BIGINT,
    store_type TEXT,
    store_located_region TEXT,
    store_located_country TEXT,
    gsc_user_data TEXT null,
    is_gsc_logged_in BOOLEAN DEFAULT FALSE,
    is_gsc_feature_enabled BOOLEAN DEFAULT FALSE,
    is_mail_report_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);
`;

export const preferenceTable = `
   CREATE TABLE IF NOT EXISTS public.Preferences (
    s_no BIGSERIAL PRIMARY KEY,
    store_id BIGINT,
    uuid VARCHAR(150) UNIQUE,
    scheduled_status BOOLEAN DEFAULT TRUE,
    scheduled_frequency VARCHAR(50) DEFAULT 'everyThreeDays',
    scheduled_time VARCHAR(50),
    next_generated_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    next_scheduled_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    next_published_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    number_of_pages BIGINT,
    product_count BIGINT,
    email_enabled BOOLEAN DEFAULT FALSE,
    auto_shuffle_enabled BOOLEAN DEFAULT FALSE,
    product_attributes JSON,
    seasonal_attributes JSON,
    customer_attributes JSON,
    market_attributes JSON,
    location_attributes JSON,
    impact_type JSON,
    weekly_report_enabled BOOLEAN DEFAULT FALSE,
    is_generated_mail_unsubscribed BOOLEAN DEFAULT FALSE,
    is_scheduled_mail_unsubscribed BOOLEAN DEFAULT FALSE,
    is_published_mail_unsubscribed BOOLEAN DEFAULT FALSE,
    is_weekly_report_unsubscribed BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_store
        FOREIGN KEY(store_id)
        REFERENCES public.Stores(id)
        ON DELETE SET NULL
);
`;


export const collections = `
    CREATE TABLE IF NOT EXISTS public.Collections (
        id BIGSERIAL PRIMARY KEY,
        store_id BIGINT,
        uuid VARCHAR(150) UNIQUE,
        collection_url VARCHAR(500),
        collection_name VARCHAR(100),
        product_details JSON,
        keywords JSON,
        description VARCHAR,
        status VARCHAR(50),
        email_enabled BOOLEAN NOT NULL DEFAULT FALSE,
        schedule_on_status BOOLEAN NOT NULL DEFAULT TRUE,
        scheduled_time TIMESTAMP WITH TIME ZONE,
        generated_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        published_time TIMESTAMP WITH TIME ZONE,
        published_type TEXT DEFAULT NULL,
        collection_id BIGINT DEFAULT NULL,
        view_count BIGINT,
        sorting_value TEXT,
        generated_type TEXT,
        task_id UUID DEFAULT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY(store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    );

    
`;

export const plan = `
    CREATE TABLE IF NOT EXISTS Public.Plans (
        id BIGSERIAL PRIMARY KEY,
        store_id BIGINT,
        plan_name TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY(store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    )
`

export const productsTable = `
    CREATE TABLE IF NOT EXISTS public.ProductsTable (
        id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
        uuid UUID DEFAULT gen_random_uuid(),
        title TEXT,
        description TEXT,
        price REAL,
        handle TEXT,
        tags TEXT[],
        vendor TEXT,
        store_id BIGINT,
        shopify_gid TEXT,
        variants JSON[],
        image_url TEXT,
        product_url TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY (store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    )`;


export const gscData = `
CREATE TABLE IF NOT EXISTS public.gscData (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
    store_id BIGINT,
    collection_id BIGINT,
    query TEXT,
    page_url TEXT,
    country TEXT,
    device TEXT,
    clicks BIGINT,
    impressions BIGINT,
    ctr BIGINT,
    position BIGINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_store
        FOREIGN KEY (store_id)
        REFERENCES public.Stores(id)
        ON DELETE SET NULL,
    CONSTRAINT fk_collection
        FOREIGN KEY (collection_id)
        REFERENCES public.Collections(id)
        ON DELETE SET NULL
)`;




export const collectionJobsTable = `
    CREATE TABLE IF NOT EXISTS public.Collection_jobs (
        id BIGINT GENERATED BY DEFAULT AS IDENTITY NOT NULL,
        store_id BIGINT,
        task_id UUID,
        attributes JSON default '{}',
        origin TEXT,
        task_status TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT fk_store
            FOREIGN KEY (store_id)
            REFERENCES public.Stores(id)
            ON DELETE SET NULL
    )
`;

// New tables for advanced features
export const alertSettingsTable = `
CREATE TABLE IF NOT EXISTS public.AlertSettings (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) UNIQUE NOT NULL,
    low_performance_threshold INTEGER DEFAULT 30,
    high_performance_threshold INTEGER DEFAULT 80,
    out_of_stock_alerts BOOLEAN DEFAULT TRUE,
    low_inventory_threshold INTEGER DEFAULT 10,
    frequency VARCHAR(50) DEFAULT 'daily',
    email_notifications BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)`;

export const dismissedAlertsTable = `
CREATE TABLE IF NOT EXISTS public.DismissedAlerts (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    alert_id VARCHAR(255) NOT NULL,
    dismissed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shop_domain, alert_id)
)`;

export const inventorySettingsTable = `
CREATE TABLE IF NOT EXISTS public.InventorySettings (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) UNIQUE NOT NULL,
    auto_hide_out_of_stock BOOLEAN DEFAULT TRUE,
    promote_high_inventory BOOLEAN DEFAULT TRUE,
    low_inventory_threshold INTEGER DEFAULT 10,
    high_inventory_threshold INTEGER DEFAULT 100,
    update_frequency VARCHAR(50) DEFAULT 'daily',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
)`;

export const productPerformanceTable = `
CREATE TABLE IF NOT EXISTS public.ProductPerformance (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    collection_id VARCHAR(255) NOT NULL,
    view_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    performance_score INTEGER DEFAULT 0,
    position INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shop_domain, product_id, collection_id)
)`;

export const collectionAnalyticsTable = `
CREATE TABLE IF NOT EXISTS public.CollectionAnalytics (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    collection_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    views INTEGER DEFAULT 0,
    unique_views INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    conversions INTEGER DEFAULT 0,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    bounce_rate DECIMAL(5,2) DEFAULT 0.00,
    average_time_on_page INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shop_domain, collection_id, date)
)`;

export const alertHistoryTable = `
CREATE TABLE IF NOT EXISTS public.AlertHistory (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    alert_type VARCHAR(100) NOT NULL,
    alert_title VARCHAR(255) NOT NULL,
    alert_description TEXT,
    collection_id VARCHAR(255),
    product_id VARCHAR(255),
    severity VARCHAR(50) DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    action_taken VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    dismissed_at TIMESTAMP WITH TIME ZONE
)`;

export const productVisibilityTable = `
CREATE TABLE IF NOT EXISTS public.ProductVisibility (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    collection_id VARCHAR(255) NOT NULL,
    is_visible BOOLEAN DEFAULT TRUE,
    hidden_reason VARCHAR(255),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    UNIQUE(shop_domain, product_id, collection_id)
)`;

