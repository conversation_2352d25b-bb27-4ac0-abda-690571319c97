stages:
  - filebeat_setup

variables:
  FILEBEAT_CONFIG: "/etc/filebeat/filebeat.yml"
  FILESTREAM_ID: "my-filestream-id-kibana"

filebeat_setup:
  stage: filebeat_setup
  script:
    - |
      if [[ "$CI_COMMIT_TAG" == stage-kibana-v* ]]; then
        TARGET_IP="**************"
        LOG_PATH="/srv/rank-collections/shopify-app/logs/app.log"
        INDEX_NAME="rank-collections-stage"
      elif [[ "$CI_COMMIT_TAG" == rc-kibana-v* ]]; then
        TARGET_IP="*************"
        LOG_PATH="rank-collections/shopify-app/logs/app.log"
        INDEX_NAME="rank-collection-prod"
      else
        echo "Invalid or unknown tag: ${CI_COMMIT_TAG:-not set}. Exiting."
        exit 1
      fi
    - echo "Installing Filebeat on $TARGET_IP..."
    - |
      /usr/bin/sshpass -p "B1nch4@*2024" ssh -o StrictHostKeyChecking=no bincha-admin@$TARGET_IP "
        curl -fsSL https://artifacts.elastic.co/GPG-KEY-elasticsearch | sudo apt-key add - &&
        echo 'deb https://artifacts.elastic.co/packages/7.x/apt stable main' | sudo tee -a /etc/apt/sources.list.d/elastic-7.x.list &&
        sudo apt-get update &&
        sudo apt-get install -y filebeat
        sudo systemctl restart filebeat 
      "
    - echo "Filebeat Setup."
  environment:
    name: azure-shopify-runner
  rules:
    - if: '$CI_COMMIT_TAG =~ /^stage-kibana-v.*/ || $CI_COMMIT_TAG =~ /^rc-kibana-v.*/'