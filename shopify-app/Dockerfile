FROM node:18-alpine

EXPOSE 3000

WORKDIR /app
COPY . .


ARG SHOPIFY_API_SECRET
ARG SCOPES
ARG SHOPIFY_API_KEY
ENV NODE_ENV=production
ENV HOST=$HOST
ENV SCOPES=$SCOPES
ENV SHOPIFY_API_SECRET=$SHOPIFY_API_SECRET
ENV SHOPIFY_API_KEY=$SHOPIFY_API_KEY
ENV SUPABASE_HOST=$SUPABASE_HOST
ENV SUPABASE_DATA_BASE=$SUPABASE_DATA_BASE
ENV SUPABASE_PORT=$SUPABASE_PORT
ENV SUPABASE_USER_NAME=$SUPABASE_USER_NAME
ENV SUPABASE_PASSWORD=$SUPABASE_PASSWORD
ENV SUPABASE_URL=$SUPABASE_URL
ENV SUPABASE_API=$SUPABASE_API
ENV AWS_ACCESS_KEY=$AWS_ACCESS_KEY
ENV AWS_SECRET_KEY=$AWS_SECRET_KEY
ENV AWS_REGION=$AWS_REGION
ENV SCHEDULED_TEMPLATE_ID=$SCHEDULED_TEMPLATE_ID
ENV POST_MARK_API=$POST_MARK_API
ENV FROM_EMAIL=$FROM_EMAIL
ENV GOOGLE_CLIENT_ID=$GOOGLE_CLIENT_ID
ENV GOOGLE_CLIENT_SECRET=$GOOGLE_CLIENT_SECRET
ENV GOOGLE_REDIRECT_URL=$GOOGLE_REDIRECT_URL
ENV GOOGLE_ANALYTICS_JWT_SECRET_KEY=$GOOGLE_ANALYTICS_JWT_SECRET_KEY
ENV GOOGLE_ENCRYPTION_KEY=$GOOGLE_ENCRYPTION_KEY

RUN npm install --omit=dev
# Remove CLI packages since we don't need them in production by default.
# Remove this line if you want to run CLI commands in your container.
RUN npm remove @shopify/app @shopify/cli
RUN npm run build
RUN echo $HOST

# Using Supabase - no local database files to remove

CMD ["npm", "run", "docker-start"]
