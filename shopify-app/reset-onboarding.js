// <PERSON>ript to reset onboarding status for stores that need API tokens
import { supabase } from './app/db/supabase_insert_helper.js';
import dotenv from 'dotenv';

dotenv.config();

async function resetOnboarding() {
    console.log('🔄 Resetting onboarding status...');
    
    try {
        // Get stores that need API tokens
        const { data: stores, error } = await supabase
            .from('stores')
            .select('id, store_name, engine_api_token, onboarding_status')
            .is('engine_api_token', null);
            
        if (error) {
            console.error('❌ Error fetching stores:', error.message);
            return;
        }
        
        if (stores.length === 0) {
            console.log('✅ All stores already have API tokens!');
            return;
        }
        
        console.log(`\n📋 Found ${stores.length} stores that need API tokens:`);
        stores.forEach(store => {
            console.log(`   • ${store.store_name} (ID: ${store.id})`);
        });
        
        // Reset onboarding status for these stores
        const storeIds = stores.map(store => store.id);
        
        const { data: updatedStores, error: updateError } = await supabase
            .from('stores')
            .update({
                onboarding_status: false,
                onboarded_step: 'step-1',
                is_store_connected: false
            })
            .in('id', storeIds)
            .select('store_name');
            
        if (updateError) {
            console.error('❌ Error updating stores:', updateError.message);
            return;
        }
        
        console.log('\n✅ Successfully reset onboarding status for:');
        updatedStores.forEach(store => {
            console.log(`   • ${store.store_name}`);
        });
        
        console.log('\n🎯 Next Steps:');
        console.log('1. Navigate to: http://localhost:3000/app/onboarding');
        console.log('2. Complete the "Store Connection" step');
        console.log('3. This will call the Agent DB and get the API token');
        console.log('4. Complete the "Products Sync" step');
        console.log('5. Dashboard metrics will then work properly');
        
    } catch (error) {
        console.error('❌ Script failed:', error.message);
    }
}

// Run the script
resetOnboarding().then(() => {
    console.log('\n🏁 Onboarding reset completed');
    process.exit(0);
}).catch((error) => {
    console.error('❌ Script crashed:', error);
    process.exit(1);
});
