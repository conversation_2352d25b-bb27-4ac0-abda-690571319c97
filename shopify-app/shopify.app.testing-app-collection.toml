# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "961dc9a86decef57f13ed5c9ba32117f"
name = "testing-app-collection"
handle = "testing-app-collection"
application_url = "https://examinations-shadow-inspections-threatened.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "binary-review.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_products,read_publications,write_products,write_publications"

[auth]
redirect_urls = [
  "https://examinations-shadow-inspections-threatened.trycloudflare.com/auth/callback",
  "https://examinations-shadow-inspections-threatened.trycloudflare.com/auth/shopify/callback",
  "https://examinations-shadow-inspections-threatened.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-07"

[pos]
embedded = false
