# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "c570479fc2bb7efa7854bfa0ded0c386"
name = "test-collection"
handle = "test-collection"
application_url = "https://speak-failure-vancouver-original.trycloudflare.com"
embedded = true

[build]
dev_store_url = "binary-dev-store.myshopify.com"
automatically_update_urls_on_dev = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_orders,read_product_listings,read_products,read_publications,read_script_tags,unauthenticated_read_product_pickup_locations,write_product_listings,write_products,write_publications,write_script_tags"

[auth]
redirect_urls = [
  "https://speak-failure-vancouver-original.trycloudflare.com/auth/callback",
  "https://speak-failure-vancouver-original.trycloudflare.com/auth/shopify/callback",
  "https://speak-failure-vancouver-original.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-10"

  [[webhooks.subscriptions]]
  topics = [
  "app_subscriptions/update",
  "app/uninstalled",
  "orders/create",
  "products/create",
  "products/delete",
  "products/update"
]
  uri = "/webhooks"
  compliance_topics = [ "customers/data_request", "customers/redact", "shop/redact" ]
