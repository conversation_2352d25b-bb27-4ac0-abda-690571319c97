-- Supabase Setup SQL for New Features
-- Run this in your Supabase SQL Editor

-- 1. Alert Settings Table
CREATE TABLE IF NOT EXISTS public.AlertSettings (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) UNIQUE NOT NULL,
    low_performance_threshold INTEGER DEFAULT 30,
    high_performance_threshold INTEGER DEFAULT 80,
    out_of_stock_alerts BOOLEAN DEFAULT TRUE,
    low_inventory_threshold INTEGER DEFAULT 10,
    frequency VARCHAR(50) DEFAULT 'daily',
    email_notifications BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 2. Dismissed Alerts Table
CREATE TABLE IF NOT EXISTS public.DismissedAlerts (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    alert_id VARCHAR(255) NOT NULL,
    dismissed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shop_domain, alert_id)
);

-- 3. Inventory Settings Table
CREATE TABLE IF NOT EXISTS public.InventorySettings (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) UNIQUE NOT NULL,
    auto_hide_out_of_stock BOOLEAN DEFAULT TRUE,
    promote_high_inventory BOOLEAN DEFAULT TRUE,
    low_inventory_threshold INTEGER DEFAULT 10,
    high_inventory_threshold INTEGER DEFAULT 100,
    update_frequency VARCHAR(50) DEFAULT 'daily',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 4. Product Performance Table
CREATE TABLE IF NOT EXISTS public.ProductPerformance (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    collection_id VARCHAR(255) NOT NULL,
    view_count INTEGER DEFAULT 0,
    click_count INTEGER DEFAULT 0,
    conversion_count INTEGER DEFAULT 0,
    performance_score INTEGER DEFAULT 0,
    position INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shop_domain, product_id, collection_id)
);

-- 5. Collection Analytics Table
CREATE TABLE IF NOT EXISTS public.CollectionAnalytics (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    collection_id VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    views INTEGER DEFAULT 0,
    unique_views INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    conversions INTEGER DEFAULT 0,
    revenue DECIMAL(10,2) DEFAULT 0.00,
    bounce_rate DECIMAL(5,2) DEFAULT 0.00,
    average_time_on_page INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(shop_domain, collection_id, date)
);

-- 6. Alert History Table
CREATE TABLE IF NOT EXISTS public.AlertHistory (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    alert_type VARCHAR(100) NOT NULL,
    alert_title VARCHAR(255) NOT NULL,
    alert_description TEXT,
    collection_id VARCHAR(255),
    product_id VARCHAR(255),
    severity VARCHAR(50) DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    is_dismissed BOOLEAN DEFAULT FALSE,
    action_taken VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    dismissed_at TIMESTAMP WITH TIME ZONE
);

-- 7. Product Visibility Table
CREATE TABLE IF NOT EXISTS public.ProductVisibility (
    id BIGSERIAL PRIMARY KEY,
    shop_domain VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    collection_id VARCHAR(255) NOT NULL,
    is_visible BOOLEAN DEFAULT TRUE,
    hidden_reason VARCHAR(255),
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) DEFAULT 'system',
    UNIQUE(shop_domain, product_id, collection_id)
);

-- Create Indexes for Performance
CREATE INDEX IF NOT EXISTS idx_alert_settings_shop ON public.AlertSettings(shop_domain);
CREATE INDEX IF NOT EXISTS idx_dismissed_alerts_shop ON public.DismissedAlerts(shop_domain);
CREATE INDEX IF NOT EXISTS idx_inventory_settings_shop ON public.InventorySettings(shop_domain);
CREATE INDEX IF NOT EXISTS idx_product_performance_shop ON public.ProductPerformance(shop_domain);
CREATE INDEX IF NOT EXISTS idx_product_performance_collection ON public.ProductPerformance(collection_id);
CREATE INDEX IF NOT EXISTS idx_collection_analytics_shop ON public.CollectionAnalytics(shop_domain);
CREATE INDEX IF NOT EXISTS idx_collection_analytics_date ON public.CollectionAnalytics(date);
CREATE INDEX IF NOT EXISTS idx_alert_history_shop ON public.AlertHistory(shop_domain);
CREATE INDEX IF NOT EXISTS idx_alert_history_created ON public.AlertHistory(created_at);
CREATE INDEX IF NOT EXISTS idx_product_visibility_shop ON public.ProductVisibility(shop_domain);
CREATE INDEX IF NOT EXISTS idx_product_visibility_product ON public.ProductVisibility(product_id);

-- Create Update Trigger Function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply Update Triggers
DROP TRIGGER IF EXISTS update_alert_settings_updated_at ON public.AlertSettings;
CREATE TRIGGER update_alert_settings_updated_at 
    BEFORE UPDATE ON public.AlertSettings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_inventory_settings_updated_at ON public.InventorySettings;
CREATE TRIGGER update_inventory_settings_updated_at 
    BEFORE UPDATE ON public.InventorySettings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) - Optional but recommended
ALTER TABLE public.AlertSettings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.DismissedAlerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.InventorySettings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ProductPerformance ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.CollectionAnalytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.AlertHistory ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ProductVisibility ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies (adjust as needed for your security requirements)
-- These policies allow full access - modify based on your authentication setup

CREATE POLICY "Enable all operations for authenticated users" ON public.AlertSettings
    FOR ALL USING (true);

CREATE POLICY "Enable all operations for authenticated users" ON public.DismissedAlerts
    FOR ALL USING (true);

CREATE POLICY "Enable all operations for authenticated users" ON public.InventorySettings
    FOR ALL USING (true);

CREATE POLICY "Enable all operations for authenticated users" ON public.ProductPerformance
    FOR ALL USING (true);

CREATE POLICY "Enable all operations for authenticated users" ON public.CollectionAnalytics
    FOR ALL USING (true);

CREATE POLICY "Enable all operations for authenticated users" ON public.AlertHistory
    FOR ALL USING (true);

CREATE POLICY "Enable all operations for authenticated users" ON public.ProductVisibility
    FOR ALL USING (true);
