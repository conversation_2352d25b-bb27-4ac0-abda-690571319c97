# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "0852e63a01043b9118791789460bc7b3"
name = "collection-app"
handle = "collection-app-2"
application_url = "https://experienced-reliable-confidence-postal.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "binary-review.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_product_listings,read_products,read_publications,write_product_listings,write_products,write_publications"

[auth]
redirect_urls = [
  "https://experienced-reliable-confidence-postal.trycloudflare.com/auth/callback",
  "https://experienced-reliable-confidence-postal.trycloudflare.com/auth/shopify/callback",
  "https://experienced-reliable-confidence-postal.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2024-07"

[pos]
embedded = false
