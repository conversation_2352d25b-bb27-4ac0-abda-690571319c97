import { createClient } from "@supabase/supabase-js";
import { fileURLToPath } from "url";
import axios from 'axios';
import path from 'path';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from "uuid";
import { ServerClient } from "postmark";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, '.env') });

const engineBaseUrl = `https://proactive.ai.chakril.site`;
const engineAPIVersion = `/api/v1`;
const collectionRoute = `/collections`;
const fetchSubroute = `/fetch`;
const collectionFetch = `/created-collections`;
const contactLink = `https://www.chakril.com/contact/`
const contactEmail = `<EMAIL>`

const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${fetchSubroute}${collectionFetch}`;
const appToken = process.env.BINCHA_APP_TOKEN;
const senderMail = process.env.COLLECTION_SENDER_MAIL;

const client = new ServerClient(process.env.POST_MARK_COLLECTION_API)
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_API);

const removeGid = (gid) => {
    const match = gid.match(/(\d+)$/);
    return match ? match[0] : null;
 };

// Email notification function
async function sendGeneratedCollectionEmail(storeEmail, emailData) {
    try {
        const response = await client.sendEmailWithTemplate({
            From: senderMail,
            To: storeEmail,
            TemplateId: process.env.GENERATED_COLLECTION_ID,
            TemplateModel: emailData,
            Tag: "GeneratedCollectionNotification",
            TrackOpens: true,
            TrackLinks: "HtmlAndText"
        });
        console.log(`✅ Email sent successfully to ${storeEmail}`);
        return response;
    } catch (error) {
        console.error(`❌ Error sending email to ${storeEmail}:`, error.message);
        throw error;
    }
}

const formatHandle = (title) => {
    return title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/-$/, '')
        .replace(/^-/, '');
};

const calculateNextGenerationTime = (currentTime, frequency) => {
    const nextTime = new Date(currentTime);
    
    switch(frequency) {
        case 'weekly':
            nextTime.setDate(nextTime.getDate() + 5);
            break;
        case 'every_three_days':
            nextTime.setDate(nextTime.getDate() + 1);
            break;
        case 'every_two_weeks':
            nextTime.setDate(nextTime.getDate() + 12);
            break;
        case 'monthly':
            nextTime.setDate(nextTime.getDate() + 28);
            break;
        default:
            nextTime.setDate(nextTime.getDate() + 1);
    }
    
    return nextTime;
};

const processCollectionData = async (collectionData, productsData, storeId, store) => {
    // Get current collection counts
    const totalCollectionCount = parseInt(store.collection_count) || 0;
    const generatedCollectionCount = parseInt(store.collection_generated_count) || 0;
    const remainingSlots = totalCollectionCount - generatedCollectionCount;

    // If no slots remaining, return empty array
    if (remainingSlots <= 0) {
        console.log(`❌ Collection limit reached for store ${store.store_name}: ${generatedCollectionCount}/${totalCollectionCount}`);
        return [];
    }

    const collections = [];
    const currentTime = new Date().toISOString();

    // Only process up to the remaining slots
    const collectionsToProcess = collectionData.data.collections.slice(0, remainingSlots);
    console.log(`ℹ️ Processing ${collectionsToProcess.length} collections out of ${collectionData.data.collections.length} (Limit: ${remainingSlots} remaining)`);

    for (const collection of collectionsToProcess) {
        const productDetails = collection.product_list.map(productId => {
            const product = productsData.find(p => p.shopify_gid === productId);
            if (product) {
                return {
                    name: product.title,
                    handle: product.handle,
                    id: removeGid(product.shopify_gid)
                };
            }
            return null;
        }).filter(Boolean);

        const collectionObj = {
            store_id: storeId,
            uuid: uuidv4(),
            collection_name: collection.collection_title,
            collection_url: formatHandle(collection.collection_title),
            product_details: productDetails,
            keywords: collection.meta_keywords,
            description: collection.collection_description,
            status: 'generated',
            generated_time: currentTime,
            generated_type: 'auto'
        };

        collections.push(collectionObj);
    }

    return collections;
};

const fetchCollections = async () => {
    const { data: storeData, error: storeDataError } = await supabase.from('stores').select('*');
    if (storeDataError) {
        console.error("The store Data Not found", storeDataError.message);
        return;
    }

    const { data: preferencesData, error: preferencesError } = await supabase.from('preferences').select('*');
    if (preferencesError) {
        console.error("The preferences data not found or something went wrong");
        return;
    }

    const { data: productsData, error: productsDataError } = await supabase.from('productstable').select('*');
    if (productsDataError) {
        console.error("Something went wrong to fetch the products data from products Table", productsDataError.message);
        return;
    }

    const emailStatus = preferencesData[0].email_enabled;
    const currentTime = new Date();

    for (const schedule of preferencesData) {
        if (!schedule.scheduled_status) continue;

        const store = storeData.find(s => s.id === schedule.store_id);
        if (store) {
            if (store.collection_generated_count >= store.collection_count) {
                console.log(`❌ Collection limit reached for ${store.store_name}: ${store.collection_generated_count}/${store.collection_count}`);
                continue;
            }
            const generatedTime = new Date(schedule.next_generated_time);

            if (currentTime >= generatedTime) {
                console.log(`✅ Condition met! Fetching collections for ${store.store_name} (Store ID: ${schedule.store_id})`);

                try {
                    const apiToken = store.engine_api_token;
                    const count = schedule.number_of_pages;
                    const apiPath = `${endPoint}?count=${count}`;

                    const response = await axios.get(apiPath, {
                        headers: {
                            "X-PROACTIVE-TOKEN": apiToken,
                            "X-BINCHA-APP-TOKEN": appToken
                        }
                    });

                    const processedCollections = await processCollectionData(
                        response.data,
                        productsData,
                        store.id,
                        store // Pass store object to check limits
                    );

                    if (processedCollections.length > 0) {
                        const { error: insertError } = await supabase
                            .from('collections')
                            .insert(processedCollections);

                        if (insertError) throw new Error(`Collection insert error: ${insertError.message}`);

                        // Update the generated count
                        const newGeneratedCount = parseInt(store.collection_generated_count) + processedCollections.length;
                        const { error: updateCountError } = await supabase
                            .from('stores')
                            .update({ collection_generated_count: newGeneratedCount })
                            .eq('id', store.id);

                        if (updateCountError) {
                            console.error(`❌ Failed to update collection count for ${store.store_name}:`, updateCountError);
                        } else {
                            console.log(`✅ Updated collection count for ${store.store_name}: ${newGeneratedCount}/${store.collection_count}`);
                        }

                        // Send email notification if enabled
                        if (emailStatus) {
                            const emailData = {
                                store_name: store.store_name,
                                collections: processedCollections.map(collection => ({
                                    name: collection.collection_name
                                })),
                                ctaUrl: `https://admin.shopify.com/store/${store.store_name}/apps/collection-app-stage/app/collection_drafts`,
                                supportUrl: contactEmail,
                                supportLinkText: contactEmail
                            };

                            try {
                                await sendGeneratedCollectionEmail(store.store_email, emailData);
                                console.log(`✅ Generated collection notification email sent to ${store.store_name}`);
                            } catch (emailError) {
                                console.error(`❌ Failed to send generated collection email to ${store.store_name}:`, emailError.message);
                            }
                        }
                    } else {
                        console.log(`ℹ️ No collections were processed for ${store.store_name} due to limit restrictions`);
                    }

                    const nextGenTime = calculateNextGenerationTime(
                        schedule.next_published_time,
                        schedule.scheduled_frequency
                    );

                    const { error: updateError } = await supabase
                        .from('preferences')
                        .update({ next_generated_time: nextGenTime })
                        .eq('s_no', schedule.s_no)
                        .eq('store_id', store.id);

                    if (updateError) {
                        console.error(`❌ Failed to update schedule time for ${store.store_name}:`, updateError);
                    } else {
                        console.log(`✅ Updated generated time for ${store.store_name} to ${nextGenTime.toISOString()} (UTC)`);
                    }
                } catch (error) {
                    console.error(`❌ Error fetching and saving collections for ${store.store_name}:`, error);
                }
            } else {
                console.log(`⏳ Waiting... The generated for ${store.store_name} is still in the future.`);
            }
        }
    }
};

fetchCollections();


