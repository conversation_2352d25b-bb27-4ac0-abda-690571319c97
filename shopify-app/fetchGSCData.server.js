import { createClient } from "@supabase/supabase-js";
import { fileURLToPath } from "url";
import axios from 'axios';
import path from 'path';
import dotenv from 'dotenv';
import { google } from "googleapis";
import { createDecipheriv } from "crypto";

// Setup environment
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Initialize clients and constants
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_API);

const collection = `/collections/`

// API endpoints and configuration
const engineBaseUrl = `https://proactive.ai.chakril.site`;
const engineAPIVersion = `/api/v1`;
const collectionRoute = `/collections`;
const syncRoute = `/sync`;
const gscDataSync = `/google-search-console`;
const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${gscDataSync}`;
const appToken = process.env.BINCHA_APP_TOKEN;
const appURL = process.env.SHOPIFY_APP_URL;

// Encryption setup
const algorithm = 'aes-256-cbc';
const key = Buffer.from(process.env.GOOGLE_ENCRYPTION_KEY, 'hex');

// OAuth configuration
const redirectURL = `${appURL}/google-gsc-callback`;
const gscOAuthClient = new google.auth.OAuth2(
    process.env.GOOGLE_CLIENT_ID,
    process.env.GOOGLE_CLIENT_SECRET,
    redirectURL
);

/**
 * Decrypt Google OAuth data */
const gscDecrypt = (encryptedData) => {
    const decipher = createDecipheriv(algorithm, key, Buffer.from(encryptedData.iv, 'hex'));
    let decrypted = decipher.update(encryptedData.content, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
};

/**
 * Refresh Google OAuth access token */
const refreshAccessToken = async (refreshToken) => {
    try {
        const response = await axios.post('https://oauth2.googleapis.com/token', null, {
            params: {
                client_id: process.env.GOOGLE_CLIENT_ID,
                client_secret: process.env.GOOGLE_CLIENT_SECRET,
                refresh_token: refreshToken,
                grant_type: 'refresh_token',
            },
        });
        return response.data.access_token;
    } catch (error) {
        console.error('Error refreshing access token:', error.response?.data || error.message);
        throw new Error('Failed to refresh access token');
    }
};

/**
 * Fetch verified sites from Google Search Console */
const fetchGSCVerifiedSites = async (accessToken) => {
    try {
        const response = await axios.get("https://www.googleapis.com/webmasters/v3/sites", {
            headers: { Authorization: `Bearer ${accessToken}` },
        });
        return response.data?.siteEntry?.map((site) => site.siteUrl) || [];
    } catch (error) {
        console.error("Error fetching GSC sites:", error);
        throw new Error("Failed to fetch GSC sites");
    }
};

/**
 * Fetch search analytics data from Google Search Console */
const fetchGSCData = async (accessToken, siteURL, startDate, endDate) => {
    try {
        const response = await axios.post(
            `https://www.googleapis.com/webmasters/v3/sites/${encodeURIComponent(siteURL)}/searchAnalytics/query`,
            {
                startDate,
                endDate,
                dimensions: ["query", "page", "country", "device", "date"],
                rowLimit: 20000
            },
            { headers: { Authorization: `Bearer ${accessToken}`, "Content-Type": "application/json" } }
        );

        return response.data.rows?.map((row) => ({
            query: row.keys[0],
            pageUrl: row.keys[1],
            country: row.keys[2],
            device: row.keys[3],
            date: row.keys[4],
            clicks: row.clicks,
            impressions: row.impressions,
            ctr: row.ctr,
            position: row.position
        })) || [];
    } catch (error) {
        console.error("Error fetching GSC data:", error);
        throw new Error("Failed to fetch GSC data");
    }
};

/**
 * Extract collection path from page URL */
const extractCollectionPath = (pageUrl) => {
    try {
        const urlObj = new URL(pageUrl);
        const pathname = urlObj.pathname;
        
        if (pathname.includes(collection)) {
            const collectionsIndex = pathname.indexOf(collection);
            const afterCollections = pathname.substring(collectionsIndex + collection.length);
            
            // Extract just the collection handle without any trailing parts
            const nextSlashIndex = afterCollections.indexOf('/');
            const collectionHandle = nextSlashIndex !== -1 
                ? afterCollections.substring(0, nextSlashIndex) 
                : afterCollections;
                
            // Format to match database format: "/collection-handle"
            return `/${collectionHandle}`;
        }
        return null;
    } catch (error) {
        console.error("Error extracting collection path:", error);
        return null;
    }
};

/**
 * Save GSC data to Supabase database */
const saveGSCData = async (storeId, gscData, storeName) => {
    try {
        // Fetch collections for the store
        const { data: collectionData, error: collectionDataError } = await supabase
            .from('collections')
            .select('*')
            .eq('store_id', storeId);

        if (collectionDataError) {
            return { status: 400, message: 'Something went wrong', data: null };
        }

        if (!collectionData || collectionData.length === 0) {
            console.warn("No collections found for store", storeId);
            return { status: 400, message: "No collections found", data: null };
        }

        // Process each GSC data row
        for (const dataRow of gscData) {
            try {
                const collectionPath = extractCollectionPath(dataRow.pageUrl);
                
                if (!collectionPath) {
                    continue;
                }
                
                // Find a matching collection using the formatted path
                const matchingCollection = collectionData.find(collection => 
                    collectionPath === collection.collection_url
                );

                if (matchingCollection) {
                    const gscDataEntry = {
                        store_id: storeId,
                        collection_id: matchingCollection.id,
                        query: dataRow.query,
                        page_url: dataRow.pageUrl,
                        country: dataRow.country,
                        device: dataRow.device,
                        clicks: dataRow.clicks,
                        impressions: dataRow.impressions,
                        ctr: Math.round(dataRow.ctr * 100),
                        position: Math.round(dataRow.position),
                        updated_at: new Date().toISOString()
                    };

                    // Check if data already exists
                    const { data: existingData, error: existingError } = await supabase
                        .from('gscdata')
                        .select('id')
                        .eq('store_id', storeId)
                        .eq('collection_id', matchingCollection.id)
                        .eq('query', dataRow.query)
                        .eq('page_url', dataRow.pageUrl)
                        .eq('country', dataRow.country)
                        .eq('device', dataRow.device);

                    if (existingError) {
                        console.error("Error checking existing GSC data",existingError.message);
                        continue;
                    }

                    // Update or insert data
                    if (existingData && existingData.length > 0) {
                        const { error: updateError } = await supabase
                            .from('gscdata')
                            .update(gscDataEntry)
                            .eq('id', existingData[0].id);

                        if (!updateError) {
                            console.log(`Updated GSC data for collection: ${matchingCollection.collection_name}, ${storeName}`);
                        }
                    } else {
                        console.log("Inserting new GSC data entry:", gscDataEntry);

                        const { error: insertError } = await supabase
                            .from('gscdata')
                            .insert(gscDataEntry);

                        if (!insertError) {
                            console.log(`Inserted new GSC data for collection: ${matchingCollection.collection_name}, ${storeName}`);
                        }
                    }
                }
            } catch (error) {
                console.error("Error processing GSC data row:", error);
                continue;
            }
        }

        console.log("Finished processing all GSC data rows");
        return { status: 200, message: 'Successfully saved GSC data', data: null };
    } catch (error) {
        console.error("Unexpected error in saveGSCData:", error);
        return { status: 400, message: "Error saving fetched GSC data", data: null };
    }
};

/**
 * Sync GSC data to the Agent API */
const syncGSCDataToAgent = async (data, storeName) => {
    try {
        // Currently using webhook.site as in original code
        const endPoint = 'https://webhook.site/e7e7a55b-5b63-43ac-9cd9-39188a9db526';
        /* Uncomment for production use
        const {data: storeData, error: storeDataError} = await supabase
            .from('stores')
            .select('*')
            .eq('store_name', storeName);
            
        if(storeDataError){
            return {
                status: 400,
                message: "Something went wrong or store data not found",
                data: null
            }
        }
        
        const engineAPIToken = storeData[0].engine_api_token;
        const actualEndPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${syncRoute}${gscDataSync}`;
        
        const response = await axios.post(actualEndPoint, data, {
            headers: {
                "X-PROACTIVE-TOKEN": engineAPIToken,
                "X-BINCHA-APP-TOKEN": appToken
            }
        });
        */

        await axios.post(endPoint, data);
        
        return {
            status: 200,
            message: "GSC data is synced to agent successfully",
            data: null
        };
    } catch (error) {
        return {
            status: 400,
            message: "Something went wrong to send the GSC Data to agent",
            data: null
        };
    }
};

/**
 * Format a date to YYYY-MM-DD string */
const formatDate = (date) => date.toISOString().split("T")[0];

/**
 * Main function to handle Google callback for GSC */
export const handleGoogleCallbackForGSC = async () => {
    console.log("Google search console data fetching is started for all stores");

    // Get all stores
    const { data: storeData, error: storeDataError } = await supabase.from('stores').select('*');
    if (storeDataError) {
        console.error("The store Data not found", storeDataError.message);
        return;
    }

    // Process each store
    for (const store of storeData) {
        const storeName = store.store_name;
        const storeDomain = store.store_url;
        const storeId = store.id;

        // Skip stores not logged in to GSC
        if (!store.is_gsc_logged_in) {
            console.log(`The store ${storeName} is not logged in to google search console`);
            continue;
        }

        try {
            // Decrypt and parse GSC user data
            const gscUserDetails = JSON.parse(store.gsc_user_data);
            const decryptedGSCUserDetails = gscDecrypt(gscUserDetails);
            const parsedDecryptedData = JSON.parse(decryptedGSCUserDetails);
            const refreshToken = parsedDecryptedData.refresh_token;
            
            // Get fresh access token
            const newAccessToken = await refreshAccessToken(refreshToken, storeName);
            if (!newAccessToken) {
                console.log(`The refresh token for store ${storeName} is not valid`);
                continue;
            }

            // Get verified sites
            const sites = await fetchGSCVerifiedSites(newAccessToken);
            if (sites.length === 0) {
                console.log(`No sites found for store ${storeName}`);
                continue;
            }

            // Find matching site for this store
            const matchingSite = sites.find((site) => site.includes(storeDomain));
            if (!matchingSite) {
                console.log(`No matching site found for store ${storeName}`);
                continue;
            }

            // Set date range for data
            const endDate = new Date();
            endDate.setDate(endDate.getDate() - 1); // Yesterday
            
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30); // 30 days ago

            // Fetch GSC data
            const gscData = await fetchGSCData(
                newAccessToken, 
                matchingSite, 
                formatDate(startDate), 
                formatDate(endDate)
            );

            if (gscData.length === 0) {
                console.log(`No data found for store ${storeName}`);
                continue;
            }

            // Save data to database
            await saveGSCData(storeId, gscData, storeName);
            console.log("The gsc data is saved in Local DB for the store", storeName);

            // Sync data to agent
            await syncGSCDataToAgent(gscData, storeName);
            console.log("The gsc data is synced to Agent for the store", storeName);
        } catch (error) {
            console.error(`Error processing store ${storeName}:`, error);
            continue;
        }
    }
};

// Execute the job
await handleGoogleCallbackForGSC();