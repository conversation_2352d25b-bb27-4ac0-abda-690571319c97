{"name": "collection-app-dev", "private": true, "scripts": {"build": "remix vite:build", "dev": "shopify app dev", "config:link": "shopify app config link", "generate": "shopify app generate", "deploy": "shopify app deploy", "config:use": "shopify app config use", "env": "shopify app env", "start": "remix-serve ./build/server/index.js", "docker-start": "npm run setup && npm run start", "setup": "echo 'Using Supabase - no setup required'", "lint": "eslint --cache --cache-location ./node_modules/.cache/eslint .", "shopify": "shopify", "graphql-codegen": "graphql-codegen", "vite": "vite"}, "type": "module", "engines": {"node": "^18.20 || ^20.10 || >=21.0.0"}, "dependencies": {"@intercom/messenger-js-sdk": "^0.0.14", "@prisma/client": "^5.11.0", "@remix-run/dev": "^2.7.1", "@remix-run/node": "^2.7.1", "@remix-run/react": "^2.7.1", "@remix-run/serve": "^2.7.1", "@shopify/app-bridge-react": "^4.1.2", "@shopify/polaris": "^12.0.0", "@shopify/shopify-app-remix": "^3.0.2", "@shopify/shopify-app-session-storage-memory": "^4.0.18", "@supabase/supabase-js": "^2.44.4", "aws-sdk": "^2.1672.0", "axios": "^1.7.5", "csv-parser": "^3.0.0", "dotenv": "^16.4.5", "googleapis": "^144.0.0", "graphql": "^16.9.0", "graphql-request": "^7.1.0", "isbot": "^5.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mixpanel-browser": "^2.58.0", "moment": "^2.30.1", "node-cron": "^3.0.3", "openai": "^4.53.0", "pg": "^8.12.0", "pino": "^9.3.1", "postmark": "^4.0.5", "prisma": "^5.11.0", "react": "^18.2.0", "react-dom": "^18.2.0", "shopify": "^3.66.0", "uuid": "^10.0.0", "vite-tsconfig-paths": "^4.3.1"}, "devDependencies": {"@remix-run/eslint-config": "^2.7.1", "@shopify/api-codegen-preset": "^1.0.1", "@shopify/stylelint-polaris": "^16.0.7", "@types/eslint": "^8.40.0", "@types/node": "^20.6.3", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "eslint": "^8.42.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.2.4", "stylelint": "^15.11.0", "typescript": "^5.2.2", "vite": "^5.1.3"}, "workspaces": ["extensions/*"], "trustedDependencies": ["@shopify/plugin-cloudflare"], "resolutions": {"undici": "6.13.0"}, "overrides": {"undici": "6.13.0"}, "author": "<PERSON><PERSON><PERSON><PERSON>"}