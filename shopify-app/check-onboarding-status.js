// Script to check onboarding status of stores
import { supabase } from './app/db/supabase_insert_helper.js';
import dotenv from 'dotenv';

dotenv.config();

async function checkOnboardingStatus() {
    console.log('🔍 Checking onboarding status...');
    
    try {
        // Get all stores and their onboarding status
        const { data: stores, error } = await supabase
            .from('stores')
            .select(`
                id,
                store_name,
                store_url,
                onboarding_status,
                is_store_connected,
                engine_api_token,
                access_token,
                product_count,
                onboarded_step
            `);
            
        if (error) {
            console.error('❌ Error fetching stores:', error.message);
            return;
        }
        
        console.log(`\n📊 Found ${stores.length} stores:\n`);
        
        stores.forEach((store, index) => {
            console.log(`${index + 1}. ${store.store_name} (${store.store_url})`);
            console.log(`   Onboarding Status: ${store.onboarding_status ? '✅ Complete' : '❌ Incomplete'}`);
            console.log(`   Store Connected: ${store.is_store_connected ? '✅ Yes' : '❌ No'}`);
            console.log(`   API Token: ${store.engine_api_token ? '✅ Present' : '❌ Missing'}`);
            console.log(`   Access Token: ${store.access_token ? '✅ Present' : '❌ Missing'}`);
            console.log(`   Product Count: ${store.product_count || 'Not set'}`);
            console.log(`   Onboarded Step: ${store.onboarded_step || 'step-1'}`);
            console.log('');
        });
        
        // Check if any plans exist
        console.log('💳 Checking plans...');
        const { data: plans, error: plansError } = await supabase
            .from('plans')
            .select('*');
            
        if (plansError) {
            console.error('❌ Error fetching plans:', plansError.message);
        } else {
            console.log(`📋 Found ${plans.length} plans in database`);
            plans.forEach((plan, index) => {
                console.log(`   ${index + 1}. Store ID: ${plan.store_id}, Plan: ${plan.plan_name || 'Not set'}`);
            });
        }
        
        // Recommendations
        console.log('\n💡 Recommendations:');
        const incompleteStores = stores.filter(store => !store.onboarding_status || !store.engine_api_token);
        
        if (incompleteStores.length > 0) {
            console.log('❌ The following stores need to complete onboarding:');
            incompleteStores.forEach(store => {
                console.log(`   • ${store.store_name}: Missing ${!store.engine_api_token ? 'API token' : 'onboarding completion'}`);
            });
            console.log('\n🔧 To fix this:');
            console.log('   1. Navigate to http://localhost:3000/app/onboarding');
            console.log('   2. Complete the "Store Connection" step');
            console.log('   3. Complete the "Products Sync" step');
            console.log('   4. Complete the pricing plan selection');
        } else {
            console.log('✅ All stores have completed onboarding!');
        }
        
    } catch (error) {
        console.error('❌ Script failed:', error.message);
    }
}

// Run the script
checkOnboardingStatus().then(() => {
    console.log('\n🏁 Onboarding status check completed');
    process.exit(0);
}).catch((error) => {
    console.error('❌ Script crashed:', error);
    process.exit(1);
});
