import { createClient } from "@supabase/supabase-js";
import { fileURLToPath } from "url";
import axios from "axios";
import path from "path";
import dotenv from "dotenv";
import { v4 as uuidv4 } from "uuid";
import { ServerClient } from "postmark";

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, ".env") });

const engineBaseUrl = `https://proactive.ai.chakril.site`;
const engineAPIVersion = `/api/v1`;
const collectionRoute = `/collections`;
const fetchSubroute = `/fetch`;
const collectionFetch = `/created-collections`;
const contactLink = `https://www.chakril.com/contact/`
const contactEmail = `<EMAIL>`

const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${fetchSubroute}${collectionFetch}`;
const appToken = process.env.BINCHA_APP_TOKEN;
const senderMail = process.env.COLLECTION_SENDER_MAIL;

const client = new ServerClient(process.env.POST_MARK_COLLECTION_API)

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_API,
);

// Function to send email notification for scheduled collections
async function sendScheduledCollectionEmail(storeEmail, emailData) {
    try {
        const response = await client.sendEmailWithTemplate({
            From: senderMail,
            To: storeEmail,
            TemplateId: process.env.SCHEDULED_COLLECTION_ID,
            TemplateModel: emailData,
            Tag: "ScheduledCollectionNotification",
            TrackOpens: true,
            TrackLinks: "HtmlAndText"
        });
        console.log(`✅ Email sent successfully to ${storeEmail}`);
        return response;
    } catch (error) {
        console.error(`❌ Error sending email to ${storeEmail}:`, error.message);
        throw error;
    }
}

const calculateNextScheduledTime = (currentTime, frequency) => {
    const nextTime = new Date(currentTime);

    switch (frequency) {
        case "weekly":
            nextTime.setDate(nextTime.getDate() + 6);
            break;
        case "every_three_days":
            nextTime.setDate(nextTime.getDate() + 2);
            break;
        case "every_two_weeks":
            nextTime.setDate(nextTime.getDate() + 13);
            break;
        case "monthly":
            nextTime.setDate(nextTime.getDate() + 29);
            break;
        default:
            nextTime.setDate(nextTime.getDate() + 1);
    }

    return nextTime;
};

const scheduleCollections = async () => {
    const { data: storeData, error: storeDataError } = await supabase
        .from("stores")
        .select("*");
    if (storeDataError) {
        console.error("The store Data Not found", storeDataError.message);
        return;
    }

    const { data: preferencesData, error: preferencesError } = await supabase
        .from("preferences")
        .select("*");
    if (preferencesError) {
        console.error("The preferences data not found or something went wrong");
        return;
    }

    const emailStatus = preferencesData[0].email_enabled;
    
    const currentTime = new Date();
    for (const schedule of preferencesData) {
        if (!schedule.scheduled_status) continue;

        const store = storeData.find((s) => s.id === schedule.store_id);
        if (store) {
            const scheduledTime = new Date(schedule.next_scheduled_time);

            if (currentTime >= scheduledTime) {
                console.log(`✅ Condition met! Changing the collection status to "scheduled" for ${store.store_name} (Store ID: ${schedule.store_id})`);
                try {
                    // Get collections before updating status to prepare email data
                    const { data: collectionsToSchedule, error: fetchError } = await supabase
                        .from("collections")
                        .select("collection_name")
                        .eq('generated_type', 'auto')
                        .eq('store_id', store.id)
                        .eq('status', 'generated');  // Only get collections that are about to be scheduled

                    if (fetchError) {
                        console.error("Error fetching collections for email:", fetchError.message);
                        continue;
                    }

                    // Update collections status
                    const { data: collections, error: collectionsError } = await supabase
                        .from("collections")
                        .update({ status: "scheduled", scheduled_time: currentTime })
                        .eq('generated_type', 'auto')
                        .eq('store_id', store.id)
                        .eq('status', 'generated');

                    if (collectionsError) {
                        console.error(
                            "Something went wrong to fetch the collection data from products Table",
                            collectionsError.message,
                        );
                        continue;
                    }

                    // Send email if enabled and collections were scheduled
                    if (emailStatus && collectionsToSchedule?.length > 0) {
                        const emailData = {
                            store_name: store.store_name,
                            collections: collectionsToSchedule.map(collection => ({
                                name: collection.collection_name
                            })),
                            ctaUrl: `https://admin.shopify.com/store/${store.store_name}/apps/collection-app-stage/app/scheduled_collections`,
                            supportUrl: contactEmail,
                            supportLinkText: contactEmail
                        };

                        try {
                            await sendScheduledCollectionEmail(store.store_email, emailData);
                            console.log(`✅ Scheduled collection notification email sent to ${store.store_name}`);
                        } catch (emailError) {
                            console.error(`❌ Failed to send scheduled collection email to ${store.store_name}:`, emailError.message);
                        }
                    }

                    const nextScheduledTime = calculateNextScheduledTime(
                        schedule.next_published_time,
                        schedule.scheduled_frequency
                    );

                    const { error: updateError } = await supabase
                        .from('preferences')
                        .update({ next_scheduled_time: nextScheduledTime })
                        .eq('s_no', schedule.s_no)
                        .eq('store_id', store.id);

                    if (updateError) {
                        console.error(`❌ Failed to update schedule time for ${store.store_name}:`, updateError);
                    } else {
                        console.log(`✅ Updated generated time for ${store.store_name} to ${nextScheduledTime.toISOString()} (UTC)`);
                    }
                } catch (error) {
                    console.error(`Something went wrong to change the status of the collection`, error);
                }
            } else {
                console.log(`⏳ Waiting... The collection scheduled for ${store.store_name} is still in the future.`);
            }
        }
    }
};

scheduleCollections();