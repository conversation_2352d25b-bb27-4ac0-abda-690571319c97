// Debug script to check store connection and data
import { supabase } from './app/db/supabase_insert_helper.js';
import dotenv from 'dotenv';

dotenv.config();

async function debugStoreConnection() {
    console.log('🔍 Debugging store connection...');

    try {
        // Test basic Supabase connection
        console.log('\n1. Testing Supabase connection...');
        const { data: testData, error: testError } = await supabase
            .from('stores')
            .select('*')
            .limit(1);

        if (testError) {
            console.error('❌ Supabase connection failed:', testError.message);
            return;
        }
        console.log('✅ Supabase connection successful');

        // Check all stores in database
        console.log('\n2. Checking all stores in database...');
        const { data: allStores, error: storesError } = await supabase
            .from('stores')
            .select('id, store_name, store_url, engine_api_token')
            .limit(10);

        if (storesError) {
            console.error('❌ Error fetching stores:', storesError.message);
            return;
        }

        console.log(`📊 Found ${allStores.length} stores in database:`);
        allStores.forEach((store, index) => {
            console.log(`   ${index + 1}. ${store.store_name} (${store.store_url})`);
            console.log(`      API Token: ${store.engine_api_token ? '✅ Present' : '❌ Missing'}`);
        });

        // Check environment variables
        console.log('\n3. Checking environment variables...');
        console.log(`SUPABASE_URL: ${process.env.SUPABASE_URL ? '✅ Set' : '❌ Missing'}`);
        console.log(`SUPABASE_API: ${process.env.SUPABASE_API ? '✅ Set' : '❌ Missing'}`);
        console.log(`BINCHA_APP_TOKEN: ${process.env.BINCHA_APP_TOKEN ? '✅ Set' : '❌ Missing'}`);

        // Test the endpoint URL construction
        console.log('\n4. Testing endpoint construction...');
        const { engineBaseUrl, engineAPIVersion, collectionRoute, fetchSubroute, dashboardMetrics } = await import('./app/configs/config.js');
        const endPoint = `${engineBaseUrl}${engineAPIVersion}${collectionRoute}${fetchSubroute}${dashboardMetrics}`;
        console.log(`Dashboard metrics endpoint: ${endPoint}`);

    } catch (error) {
        console.error('❌ Debug script failed:', error.message);
        console.error('Stack trace:', error.stack);
    }
}

// Run the debug script
debugStoreConnection().then(() => {
    console.log('\n🏁 Debug script completed');
    process.exit(0);
}).catch((error) => {
    console.error('❌ Debug script crashed:', error);
    process.exit(1);
});
