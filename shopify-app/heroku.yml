build:
  docker:
    web: Dockerfile
  config:
    SHOPIFY_API_KEY: b7534921a9117726ce09f641866a105b
    SHOPIFY_API_SECRET: 5144d3ee84b36b0d94d6a373e3208f82
    SCOPES: "read_products,read_publications,write_products,write_publications"
    SHOP_CUSTOM_DOMAIN: 
    SUPABASE_HOST: aws-0-us-east-1.pooler.supabase.com
    SUPABASE_DATA_BASE: postgres
    SUPABASE_PORT: 6543
    SUPABASE_USER_NAME: postgres.hmbjfbmvykoeukjlllgq
    SUPABASE_PASSWORD: JackHack100@
    SUPABASE_URL: https://hmbjfbmvykoeukjlllgq.supabase.co
    SUPABASE_API: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhtYmpmYm12eWtvZXVramxsbGdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjA3NzEwOTgsImV4cCI6MjAzNjM0NzA5OH0._3oZTaNI7ToP_HlRcEIW4B-57PAbEM0nUZIEsLUnq7k
    AWS_ACCESS_KEY: ********************
    AWS_SECRET_KEY: D/ugdyxr4kKD6IHMLEnJOKV/8s+bkGnKLLKKCZvj
    AWS_REGION: us-east-1
    SCHEDULED_TEMPLATE_ID: 37240223
    POST_MARK_API: b0f725bf-bd22-4355-bdc5-c1091ff5dd8c
    FROM_EMAIL: <EMAIL>
    GOOGLE_CLIENT_ID: 1012311604282-ld4h1mfepjjqjcm99ntkkacq5e4s4519.apps.googleusercontent.com
    GOOGLE_CLIENT_SECRET: 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
    GOOGLE_REDIRECT_URL: https://collectionsai-stage-a1e079391a60.herokuapp.com/
    GOOGLE_ANALYTICS_JWT_SECRET_KEY: c6cea326310cf3dd074aba9a208900b7
    GOOGLE_ENCRYPTION_KEY: 4834d580efd78dff3b3b905c05064d024ba5f3eda27ae0078fb7b8295dd6af9b