import { google } from "googleapis";
import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';
import { createDecipheriv } from 'crypto';
import axios from 'axios';
import path from 'path';
import dotenv from 'dotenv';
import { ServerClient } from 'postmark';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
dotenv.config({ path: path.resolve(__dirname, '.env') });

const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_API);

// Initialize Postmark client
const postmarkClient = new ServerClient(process.env.POST_MARK_COLLECTION_API);

// Decryption function
const algorithm = 'aes-256-cbc';
const key = Buffer.from(process.env.GOOGLE_ENCRYPTION_KEY, 'hex');
const decrypt = (encryptedData, encryptedIv) => {
  const decipher = createDecipheriv(algorithm, key, Buffer.from(encryptedIv, 'hex'));
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
};

// Function to refresh Google Access Token
const refreshAccessToken = async (refreshToken) => {
  try {
    const response = await axios.post('https://oauth2.googleapis.com/token', null, {
      params: {
        client_id: process.env.GOOGLE_CLIENT_ID,
        client_secret: process.env.GOOGLE_CLIENT_SECRET,
        refresh_token: refreshToken,
        grant_type: 'refresh_token',
      },
    });
    console.log("New access token retrieved successfully.");
    return response.data.access_token;
  } catch (error) {
    console.error('Error refreshing access token:', error.response?.data || error.message);
    throw new Error('Failed to refresh access token');
  }
};

// Function to send email via Postmark
const sendCollectionReportEmail = async (store, emailData) => {
  try {
    console.log(`Sending email report for ${store.store_name}...`);
    
    const response = await postmarkClient.sendEmailWithTemplate({
      From: process.env.COLLECTION_SENDER_MAIL,
      To: store.store_email,
      TemplateId: process.env.WEEKLY_REPORT_ID,
      TemplateModel: emailData,
      Tag: "weeklyReportPublishedCollectionNotification",
      TrackOpens: true,
      TrackLinks: "HtmlAndText"
    });
    
    console.log(`Email report sent successfully for ${store.store_name}. Message ID: ${response.MessageID}`);
    return response;
  } catch (error) {
    console.error(`Error sending email report for ${store.store_name}:`, error.message);
    throw error;
  }
};

const fetchGA4Data = async () => {
  console.log("Fetching store data from Supabase...");
  const { data: storeData, error: storeDataError } = await supabase
    .from('stores')
    .select('id, store_name, google_analytics_key, google_encrypted_iv, google_account_id, google_property_id, store_email, store_url');

  if (storeDataError) {
    console.error('Error fetching store details:', storeDataError.message);
    return;
  }

  
  for (const store of storeData) {
    if (!store.google_analytics_key || !store.google_account_id || !store.google_property_id) {
      console.log(`${store.store_name} is missing Google credentials.`);
      continue;
    }

  const {data: preferencesData, error: preferencesError} = await supabase.from('preferences').select('*').eq('store_id', store.id)
  if(preferencesError){
    console.error('Error fetching preferences:', preferencesError.message);
    return;
  }


    try {
      console.log(`Processing store: ${store.store_name}`);
      const decryptedData = decrypt(store.google_analytics_key, store.google_encrypted_iv);
      const responseData = JSON.parse(decryptedData);
      const refreshToken = responseData.refreshToken;

      const newAccessToken = await refreshAccessToken(refreshToken);
      
      const oauth2Client = new google.auth.OAuth2(
        process.env.GOOGLE_CLIENT_ID,
        process.env.GOOGLE_CLIENT_SECRET
      );
      oauth2Client.setCredentials({ access_token: newAccessToken });

      const analyticsData = google.analyticsdata('v1beta');
      const propertyId = store.google_property_id;

      const response = await analyticsData.properties.runReport({
        auth: oauth2Client,
        property: `properties/${propertyId}`,
        requestBody: {
          dateRanges: [{ startDate: '30daysAgo', endDate: 'today' }],
          dimensions: [{ name: 'pagePath' }],
          metrics: [{ name: 'screenPageViews' }],
        },
      });
      
      if (!response.data.rows) continue;

      // Fetch all collections for this store to prepare email data
      const { data: storeCollections, error: collectionsError } = await supabase
        .from('collections')
        .select('id, collection_name, collection_url, view_count, updated_at')
        .eq('store_id', store.id)
        .eq('status', 'published');
      
      if (collectionsError) {
        console.error(`Error fetching collections for ${store.store_name}:`, collectionsError.message);
        continue;
      }

      // Create a map of collection slugs to existing view counts
      const collectionsMap = {};
      storeCollections.forEach(collection => {
        const slug = collection.collection_url.replace(/^\/+/, '');
        collectionsMap[slug] = {
          id: collection.id,
          name: collection.collection_name,
          url: collection.collection_url,
          previousViews: collection.view_count || 0,
          currentViews: 0
        };
      });

      // Process GA4 data and update collections
      for (const row of response.data.rows) {
        const pagePath = row.dimensionValues?.[0]?.value;
        const pageViews = parseInt(row.metricValues?.[0]?.value, 10) || 0;
        
        if (!pagePath.includes('/collections/')) continue;

        const collectionSlug = pagePath.replace(/\/collections\/+/, '').split('/')[0];
        console.log(`Found collection: ${collectionSlug} with ${pageViews} views`);
        
        if (collectionsMap[collectionSlug]) {
          collectionsMap[collectionSlug].currentViews = pageViews;
          
          // Update each collection individually with proper conditions
          const { error: updateError } = await supabase
            .from('collections')
            .update({
              view_count: pageViews,
              updated_at: new Date().toISOString()
            })
            .eq('store_id', store.id)
            .eq('collection_url', `/${collectionSlug}`)
            .eq('status', 'published');

          if (updateError) {
            console.error(`Error updating collection ${collectionSlug}:`, updateError.message);
          } else {
            console.log(`Successfully updated collection ${collectionSlug} with ${pageViews} views`);
          }
        }
      }

      // Prepare email data for Postmark template
      const collectionsForEmail = Object.values(collectionsMap)
        .filter(collection => collection.currentViews > 0 || collection.previousViews > 0)
        .map((collection, index) => ({
          index: index + 1,
          name: collection.name,
          url: `${store.store_url || ''}/collections${collection.url}`,
          lastWeekViews: collection.previousViews,
          currentWeekViews: collection.currentViews
        }))
        // .sort((a, b) => b.currentWeekViews - a.currentWeekViews);

      const emailData = {
        store_name_value: store.store_name,
        year: new Date().getFullYear(),
        your_company_name: "Rank Collections", // Update with your company name
        collections: collectionsForEmail
      };

      // Send email report
     // Check if weekly_report_enabled is true in preferences before sending email
if (collectionsForEmail.length > 0 && store.store_email && preferencesData[0]?.weekly_report_enabled === true && !preferencesData[0]?.is_weekly_mail_unsubscribed) {
  await sendCollectionReportEmail(store, emailData);
} else {
  console.log(`No email sent for ${store.store_name}: ${!collectionsForEmail.length ? 'No collections with views' : !store.store_email ? 'No email address' : 'Weekly reports disabled'}`);
}

    } catch (error) {
      console.error(`Error processing GA4 data for ${store.store_name}:`, error.message);
    }
  }
};

fetchGA4Data();
